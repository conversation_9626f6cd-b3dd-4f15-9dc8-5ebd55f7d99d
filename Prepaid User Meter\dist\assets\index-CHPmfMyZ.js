var kg=Object.defineProperty;var Cg=(t,e,n)=>e in t?kg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var L=(t,e,n)=>Cg(t,typeof e!="symbol"?e+"":e,n);function Mg(t,e){for(var n=0;n<e.length;n++){const r=e[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in t)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(t,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Pg(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Jh={exports:{}},Io={},ef={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qi=Symbol.for("react.element"),Eg=Symbol.for("react.portal"),Tg=Symbol.for("react.fragment"),Rg=Symbol.for("react.strict_mode"),Og=Symbol.for("react.profiler"),Dg=Symbol.for("react.provider"),$g=Symbol.for("react.context"),Lg=Symbol.for("react.forward_ref"),Fg=Symbol.for("react.suspense"),zg=Symbol.for("react.memo"),Ag=Symbol.for("react.lazy"),gu=Symbol.iterator;function Ig(t){return t===null||typeof t!="object"?null:(t=gu&&t[gu]||t["@@iterator"],typeof t=="function"?t:null)}var tf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},nf=Object.assign,rf={};function Lr(t,e,n){this.props=t,this.context=e,this.refs=rf,this.updater=n||tf}Lr.prototype.isReactComponent={};Lr.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Lr.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function sf(){}sf.prototype=Lr.prototype;function sc(t,e,n){this.props=t,this.context=e,this.refs=rf,this.updater=n||tf}var oc=sc.prototype=new sf;oc.constructor=sc;nf(oc,Lr.prototype);oc.isPureReactComponent=!0;var xu=Array.isArray,of=Object.prototype.hasOwnProperty,ac={current:null},af={key:!0,ref:!0,__self:!0,__source:!0};function lf(t,e,n){var r,i={},s=null,o=null;if(e!=null)for(r in e.ref!==void 0&&(o=e.ref),e.key!==void 0&&(s=""+e.key),e)of.call(e,r)&&!af.hasOwnProperty(r)&&(i[r]=e[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(t&&t.defaultProps)for(r in a=t.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Qi,type:t,key:s,ref:o,props:i,_owner:ac.current}}function Ug(t,e){return{$$typeof:Qi,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function lc(t){return typeof t=="object"&&t!==null&&t.$$typeof===Qi}function Hg(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var vu=/\/+/g;function da(t,e){return typeof t=="object"&&t!==null&&t.key!=null?Hg(""+t.key):e.toString(36)}function Ds(t,e,n,r,i){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case Qi:case Eg:o=!0}}if(o)return o=t,i=i(o),t=r===""?"."+da(o,0):r,xu(i)?(n="",t!=null&&(n=t.replace(vu,"$&/")+"/"),Ds(i,e,n,"",function(u){return u})):i!=null&&(lc(i)&&(i=Ug(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(vu,"$&/")+"/")+t)),e.push(i)),1;if(o=0,r=r===""?".":r+":",xu(t))for(var a=0;a<t.length;a++){s=t[a];var l=r+da(s,a);o+=Ds(s,e,n,l,i)}else if(l=Ig(t),typeof l=="function")for(t=l.call(t),a=0;!(s=t.next()).done;)s=s.value,l=r+da(s,a++),o+=Ds(s,e,n,l,i);else if(s==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return o}function is(t,e,n){if(t==null)return t;var r=[],i=0;return Ds(t,r,"","",function(s){return e.call(n,s,i++)}),r}function Wg(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var Fe={current:null},$s={transition:null},Bg={ReactCurrentDispatcher:Fe,ReactCurrentBatchConfig:$s,ReactCurrentOwner:ac};function cf(){throw Error("act(...) is not supported in production builds of React.")}z.Children={map:is,forEach:function(t,e,n){is(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return is(t,function(){e++}),e},toArray:function(t){return is(t,function(e){return e})||[]},only:function(t){if(!lc(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};z.Component=Lr;z.Fragment=Tg;z.Profiler=Og;z.PureComponent=sc;z.StrictMode=Rg;z.Suspense=Fg;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Bg;z.act=cf;z.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=nf({},t.props),i=t.key,s=t.ref,o=t._owner;if(e!=null){if(e.ref!==void 0&&(s=e.ref,o=ac.current),e.key!==void 0&&(i=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)of.call(e,l)&&!af.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Qi,type:t.type,key:i,ref:s,props:r,_owner:o}};z.createContext=function(t){return t={$$typeof:$g,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Dg,_context:t},t.Consumer=t};z.createElement=lf;z.createFactory=function(t){var e=lf.bind(null,t);return e.type=t,e};z.createRef=function(){return{current:null}};z.forwardRef=function(t){return{$$typeof:Lg,render:t}};z.isValidElement=lc;z.lazy=function(t){return{$$typeof:Ag,_payload:{_status:-1,_result:t},_init:Wg}};z.memo=function(t,e){return{$$typeof:zg,type:t,compare:e===void 0?null:e}};z.startTransition=function(t){var e=$s.transition;$s.transition={};try{t()}finally{$s.transition=e}};z.unstable_act=cf;z.useCallback=function(t,e){return Fe.current.useCallback(t,e)};z.useContext=function(t){return Fe.current.useContext(t)};z.useDebugValue=function(){};z.useDeferredValue=function(t){return Fe.current.useDeferredValue(t)};z.useEffect=function(t,e){return Fe.current.useEffect(t,e)};z.useId=function(){return Fe.current.useId()};z.useImperativeHandle=function(t,e,n){return Fe.current.useImperativeHandle(t,e,n)};z.useInsertionEffect=function(t,e){return Fe.current.useInsertionEffect(t,e)};z.useLayoutEffect=function(t,e){return Fe.current.useLayoutEffect(t,e)};z.useMemo=function(t,e){return Fe.current.useMemo(t,e)};z.useReducer=function(t,e,n){return Fe.current.useReducer(t,e,n)};z.useRef=function(t){return Fe.current.useRef(t)};z.useState=function(t){return Fe.current.useState(t)};z.useSyncExternalStore=function(t,e,n){return Fe.current.useSyncExternalStore(t,e,n)};z.useTransition=function(){return Fe.current.useTransition()};z.version="18.3.1";ef.exports=z;var j=ef.exports;const gt=Pg(j),Vg=Mg({__proto__:null,default:gt},[j]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yg=j,Xg=Symbol.for("react.element"),Qg=Symbol.for("react.fragment"),Kg=Object.prototype.hasOwnProperty,Gg=Yg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,qg={key:!0,ref:!0,__self:!0,__source:!0};function uf(t,e,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(o=e.ref);for(r in e)Kg.call(e,r)&&!qg.hasOwnProperty(r)&&(i[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)i[r]===void 0&&(i[r]=e[r]);return{$$typeof:Xg,type:t,key:s,ref:o,props:i,_owner:Gg.current}}Io.Fragment=Qg;Io.jsx=uf;Io.jsxs=uf;Jh.exports=Io;var c=Jh.exports,Ja={},df={exports:{}},Je={},hf={exports:{}},ff={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(P,D){var F=P.length;P.push(D);e:for(;0<F;){var K=F-1>>>1,Z=P[K];if(0<i(Z,D))P[K]=D,P[F]=Z,F=K;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var D=P[0],F=P.pop();if(F!==D){P[0]=F;e:for(var K=0,Z=P.length,bt=Z>>>1;K<bt;){var Te=2*(K+1)-1,Et=P[Te],Re=Te+1,rs=P[Re];if(0>i(Et,F))Re<Z&&0>i(rs,Et)?(P[K]=rs,P[Re]=F,K=Re):(P[K]=Et,P[Te]=F,K=Te);else if(Re<Z&&0>i(rs,F))P[K]=rs,P[Re]=F,K=Re;else break e}}return D}function i(P,D){var F=P.sortIndex-D.sortIndex;return F!==0?F:P.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();t.unstable_now=function(){return o.now()-a}}var l=[],u=[],d=1,h=null,f=3,m=!1,v=!1,x=!1,b=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(P){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=P)r(u),D.sortIndex=D.expirationTime,e(l,D);else break;D=n(u)}}function w(P){if(x=!1,y(P),!v)if(n(l)!==null)v=!0,A(_);else{var D=n(u);D!==null&&B(w,D.startTime-P)}}function _(P,D){v=!1,x&&(x=!1,g(k),k=-1),m=!0;var F=f;try{for(y(D),h=n(l);h!==null&&(!(h.expirationTime>D)||P&&!E());){var K=h.callback;if(typeof K=="function"){h.callback=null,f=h.priorityLevel;var Z=K(h.expirationTime<=D);D=t.unstable_now(),typeof Z=="function"?h.callback=Z:h===n(l)&&r(l),y(D)}else r(l);h=n(l)}if(h!==null)var bt=!0;else{var Te=n(u);Te!==null&&B(w,Te.startTime-D),bt=!1}return bt}finally{h=null,f=F,m=!1}}var S=!1,N=null,k=-1,T=5,M=-1;function E(){return!(t.unstable_now()-M<T)}function $(){if(N!==null){var P=t.unstable_now();M=P;var D=!0;try{D=N(!0,P)}finally{D?I():(S=!1,N=null)}}else S=!1}var I;if(typeof p=="function")I=function(){p($)};else if(typeof MessageChannel<"u"){var fe=new MessageChannel,R=fe.port2;fe.port1.onmessage=$,I=function(){R.postMessage(null)}}else I=function(){b($,0)};function A(P){N=P,S||(S=!0,I())}function B(P,D){k=b(function(){P(t.unstable_now())},D)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(P){P.callback=null},t.unstable_continueExecution=function(){v||m||(v=!0,A(_))},t.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<P?Math.floor(1e3/P):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(P){switch(f){case 1:case 2:case 3:var D=3;break;default:D=f}var F=f;f=D;try{return P()}finally{f=F}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(P,D){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var F=f;f=P;try{return D()}finally{f=F}},t.unstable_scheduleCallback=function(P,D,F){var K=t.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?K+F:K):F=K,P){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=F+Z,P={id:d++,callback:D,priorityLevel:P,startTime:F,expirationTime:Z,sortIndex:-1},F>K?(P.sortIndex=F,e(u,P),n(l)===null&&P===n(u)&&(x?(g(k),k=-1):x=!0,B(w,F-K))):(P.sortIndex=Z,e(l,P),v||m||(v=!0,A(_))),P},t.unstable_shouldYield=E,t.unstable_wrapCallback=function(P){var D=f;return function(){var F=f;f=D;try{return P.apply(this,arguments)}finally{f=F}}}})(ff);hf.exports=ff;var Zg=hf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jg=j,Ze=Zg;function C(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var mf=new Set,Ni={};function qn(t,e){Cr(t,e),Cr(t+"Capture",e)}function Cr(t,e){for(Ni[t]=e,t=0;t<e.length;t++)mf.add(e[t])}var Ht=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),el=Object.prototype.hasOwnProperty,e0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,yu={},bu={};function t0(t){return el.call(bu,t)?!0:el.call(yu,t)?!1:e0.test(t)?bu[t]=!0:(yu[t]=!0,!1)}function n0(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function r0(t,e,n,r){if(e===null||typeof e>"u"||n0(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function ze(t,e,n,r,i,s,o){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=s,this.removeEmptyString=o}var Se={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){Se[t]=new ze(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];Se[e]=new ze(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){Se[t]=new ze(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){Se[t]=new ze(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){Se[t]=new ze(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){Se[t]=new ze(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){Se[t]=new ze(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){Se[t]=new ze(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){Se[t]=new ze(t,5,!1,t.toLowerCase(),null,!1,!1)});var cc=/[\-:]([a-z])/g;function uc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(cc,uc);Se[e]=new ze(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(cc,uc);Se[e]=new ze(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(cc,uc);Se[e]=new ze(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){Se[t]=new ze(t,1,!1,t.toLowerCase(),null,!1,!1)});Se.xlinkHref=new ze("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){Se[t]=new ze(t,1,!1,t.toLowerCase(),null,!0,!0)});function dc(t,e,n,r){var i=Se.hasOwnProperty(e)?Se[e]:null;(i!==null?i.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(r0(e,n,i,r)&&(n=null),r||i===null?t0(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=n===null?i.type===3?!1:"":n:(e=i.attributeName,r=i.attributeNamespace,n===null?t.removeAttribute(e):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var Xt=Jg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ss=Symbol.for("react.element"),ir=Symbol.for("react.portal"),sr=Symbol.for("react.fragment"),hc=Symbol.for("react.strict_mode"),tl=Symbol.for("react.profiler"),pf=Symbol.for("react.provider"),gf=Symbol.for("react.context"),fc=Symbol.for("react.forward_ref"),nl=Symbol.for("react.suspense"),rl=Symbol.for("react.suspense_list"),mc=Symbol.for("react.memo"),qt=Symbol.for("react.lazy"),xf=Symbol.for("react.offscreen"),wu=Symbol.iterator;function Hr(t){return t===null||typeof t!="object"?null:(t=wu&&t[wu]||t["@@iterator"],typeof t=="function"?t:null)}var se=Object.assign,ha;function ri(t){if(ha===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ha=e&&e[1]||""}return`
`+ha+t}var fa=!1;function ma(t,e){if(!t||fa)return"";fa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=o&&0<=a);break}}}finally{fa=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?ri(t):""}function i0(t){switch(t.tag){case 5:return ri(t.type);case 16:return ri("Lazy");case 13:return ri("Suspense");case 19:return ri("SuspenseList");case 0:case 2:case 15:return t=ma(t.type,!1),t;case 11:return t=ma(t.type.render,!1),t;case 1:return t=ma(t.type,!0),t;default:return""}}function il(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case sr:return"Fragment";case ir:return"Portal";case tl:return"Profiler";case hc:return"StrictMode";case nl:return"Suspense";case rl:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case gf:return(t.displayName||"Context")+".Consumer";case pf:return(t._context.displayName||"Context")+".Provider";case fc:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case mc:return e=t.displayName||null,e!==null?e:il(t.type)||"Memo";case qt:e=t._payload,t=t._init;try{return il(t(e))}catch{}}return null}function s0(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return il(e);case 8:return e===hc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function bn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function vf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function o0(t){var e=vf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function os(t){t._valueTracker||(t._valueTracker=o0(t))}function yf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=vf(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function Js(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function sl(t,e){var n=e.checked;return se({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function Su(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=bn(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function bf(t,e){e=e.checked,e!=null&&dc(t,"checked",e,!1)}function ol(t,e){bf(t,e);var n=bn(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?al(t,e.type,n):e.hasOwnProperty("defaultValue")&&al(t,e.type,bn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function _u(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function al(t,e,n){(e!=="number"||Js(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var ii=Array.isArray;function gr(t,e,n,r){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&r&&(t[n].defaultSelected=!0)}else{for(n=""+bn(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,r&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function ll(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(C(91));return se({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Nu(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(C(92));if(ii(n)){if(1<n.length)throw Error(C(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:bn(n)}}function wf(t,e){var n=bn(e.value),r=bn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function ju(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function Sf(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function cl(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?Sf(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var as,_f=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,i){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,i)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(as=as||document.createElement("div"),as.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=as.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function ji(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var hi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},a0=["Webkit","ms","Moz","O"];Object.keys(hi).forEach(function(t){a0.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),hi[e]=hi[t]})});function Nf(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||hi.hasOwnProperty(t)&&hi[t]?(""+e).trim():e+"px"}function jf(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Nf(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,i):t[n]=i}}var l0=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ul(t,e){if(e){if(l0[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(C(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(C(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(C(61))}if(e.style!=null&&typeof e.style!="object")throw Error(C(62))}}function dl(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var hl=null;function pc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var fl=null,xr=null,vr=null;function ku(t){if(t=qi(t)){if(typeof fl!="function")throw Error(C(280));var e=t.stateNode;e&&(e=Vo(e),fl(t.stateNode,t.type,e))}}function kf(t){xr?vr?vr.push(t):vr=[t]:xr=t}function Cf(){if(xr){var t=xr,e=vr;if(vr=xr=null,ku(t),e)for(t=0;t<e.length;t++)ku(e[t])}}function Mf(t,e){return t(e)}function Pf(){}var pa=!1;function Ef(t,e,n){if(pa)return t(e,n);pa=!0;try{return Mf(t,e,n)}finally{pa=!1,(xr!==null||vr!==null)&&(Pf(),Cf())}}function ki(t,e){var n=t.stateNode;if(n===null)return null;var r=Vo(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(C(231,e,typeof n));return n}var ml=!1;if(Ht)try{var Wr={};Object.defineProperty(Wr,"passive",{get:function(){ml=!0}}),window.addEventListener("test",Wr,Wr),window.removeEventListener("test",Wr,Wr)}catch{ml=!1}function c0(t,e,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var fi=!1,eo=null,to=!1,pl=null,u0={onError:function(t){fi=!0,eo=t}};function d0(t,e,n,r,i,s,o,a,l){fi=!1,eo=null,c0.apply(u0,arguments)}function h0(t,e,n,r,i,s,o,a,l){if(d0.apply(this,arguments),fi){if(fi){var u=eo;fi=!1,eo=null}else throw Error(C(198));to||(to=!0,pl=u)}}function Zn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Tf(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Cu(t){if(Zn(t)!==t)throw Error(C(188))}function f0(t){var e=t.alternate;if(!e){if(e=Zn(t),e===null)throw Error(C(188));return e!==t?null:t}for(var n=t,r=e;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Cu(i),t;if(s===r)return Cu(i),e;s=s.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?t:e}function Rf(t){return t=f0(t),t!==null?Of(t):null}function Of(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Of(t);if(e!==null)return e;t=t.sibling}return null}var Df=Ze.unstable_scheduleCallback,Mu=Ze.unstable_cancelCallback,m0=Ze.unstable_shouldYield,p0=Ze.unstable_requestPaint,le=Ze.unstable_now,g0=Ze.unstable_getCurrentPriorityLevel,gc=Ze.unstable_ImmediatePriority,$f=Ze.unstable_UserBlockingPriority,no=Ze.unstable_NormalPriority,x0=Ze.unstable_LowPriority,Lf=Ze.unstable_IdlePriority,Uo=null,Ct=null;function v0(t){if(Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(Uo,t,void 0,(t.current.flags&128)===128)}catch{}}var xt=Math.clz32?Math.clz32:w0,y0=Math.log,b0=Math.LN2;function w0(t){return t>>>=0,t===0?32:31-(y0(t)/b0|0)|0}var ls=64,cs=4194304;function si(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function ro(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,i=t.suspendedLanes,s=t.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=si(a):(s&=o,s!==0&&(r=si(s)))}else o=n&~i,o!==0?r=si(o):s!==0&&(r=si(s));if(r===0)return 0;if(e!==0&&e!==r&&!(e&i)&&(i=r&-r,s=e&-e,i>=s||i===16&&(s&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-xt(e),i=1<<n,r|=t[n],e&=~i;return r}function S0(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function _0(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,i=t.expirationTimes,s=t.pendingLanes;0<s;){var o=31-xt(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=S0(a,e)):l<=e&&(t.expiredLanes|=a),s&=~a}}function gl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Ff(){var t=ls;return ls<<=1,!(ls&4194240)&&(ls=64),t}function ga(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ki(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-xt(e),t[e]=n}function N0(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var i=31-xt(n),s=1<<i;e[i]=0,r[i]=-1,t[i]=-1,n&=~s}}function xc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-xt(n),i=1<<r;i&e|t[r]&e&&(t[r]|=e),n&=~i}}var X=0;function zf(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Af,vc,If,Uf,Hf,xl=!1,us=[],ln=null,cn=null,un=null,Ci=new Map,Mi=new Map,Jt=[],j0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Pu(t,e){switch(t){case"focusin":case"focusout":ln=null;break;case"dragenter":case"dragleave":cn=null;break;case"mouseover":case"mouseout":un=null;break;case"pointerover":case"pointerout":Ci.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Mi.delete(e.pointerId)}}function Br(t,e,n,r,i,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},e!==null&&(e=qi(e),e!==null&&vc(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function k0(t,e,n,r,i){switch(e){case"focusin":return ln=Br(ln,t,e,n,r,i),!0;case"dragenter":return cn=Br(cn,t,e,n,r,i),!0;case"mouseover":return un=Br(un,t,e,n,r,i),!0;case"pointerover":var s=i.pointerId;return Ci.set(s,Br(Ci.get(s)||null,t,e,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Mi.set(s,Br(Mi.get(s)||null,t,e,n,r,i)),!0}return!1}function Wf(t){var e=Ln(t.target);if(e!==null){var n=Zn(e);if(n!==null){if(e=n.tag,e===13){if(e=Tf(n),e!==null){t.blockedOn=e,Hf(t.priority,function(){If(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ls(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=vl(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);hl=r,n.target.dispatchEvent(r),hl=null}else return e=qi(n),e!==null&&vc(e),t.blockedOn=n,!1;e.shift()}return!0}function Eu(t,e,n){Ls(t)&&n.delete(e)}function C0(){xl=!1,ln!==null&&Ls(ln)&&(ln=null),cn!==null&&Ls(cn)&&(cn=null),un!==null&&Ls(un)&&(un=null),Ci.forEach(Eu),Mi.forEach(Eu)}function Vr(t,e){t.blockedOn===e&&(t.blockedOn=null,xl||(xl=!0,Ze.unstable_scheduleCallback(Ze.unstable_NormalPriority,C0)))}function Pi(t){function e(i){return Vr(i,t)}if(0<us.length){Vr(us[0],t);for(var n=1;n<us.length;n++){var r=us[n];r.blockedOn===t&&(r.blockedOn=null)}}for(ln!==null&&Vr(ln,t),cn!==null&&Vr(cn,t),un!==null&&Vr(un,t),Ci.forEach(e),Mi.forEach(e),n=0;n<Jt.length;n++)r=Jt[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<Jt.length&&(n=Jt[0],n.blockedOn===null);)Wf(n),n.blockedOn===null&&Jt.shift()}var yr=Xt.ReactCurrentBatchConfig,io=!0;function M0(t,e,n,r){var i=X,s=yr.transition;yr.transition=null;try{X=1,yc(t,e,n,r)}finally{X=i,yr.transition=s}}function P0(t,e,n,r){var i=X,s=yr.transition;yr.transition=null;try{X=4,yc(t,e,n,r)}finally{X=i,yr.transition=s}}function yc(t,e,n,r){if(io){var i=vl(t,e,n,r);if(i===null)ka(t,e,r,so,n),Pu(t,r);else if(k0(i,t,e,n,r))r.stopPropagation();else if(Pu(t,r),e&4&&-1<j0.indexOf(t)){for(;i!==null;){var s=qi(i);if(s!==null&&Af(s),s=vl(t,e,n,r),s===null&&ka(t,e,r,so,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else ka(t,e,r,null,n)}}var so=null;function vl(t,e,n,r){if(so=null,t=pc(r),t=Ln(t),t!==null)if(e=Zn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=Tf(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return so=t,null}function Bf(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(g0()){case gc:return 1;case $f:return 4;case no:case x0:return 16;case Lf:return 536870912;default:return 16}default:return 16}}var tn=null,bc=null,Fs=null;function Vf(){if(Fs)return Fs;var t,e=bc,n=e.length,r,i="value"in tn?tn.value:tn.textContent,s=i.length;for(t=0;t<n&&e[t]===i[t];t++);var o=n-t;for(r=1;r<=o&&e[n-r]===i[s-r];r++);return Fs=i.slice(t,1<r?1-r:void 0)}function zs(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ds(){return!0}function Tu(){return!1}function et(t){function e(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ds:Tu,this.isPropagationStopped=Tu,this}return se(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ds)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ds)},persist:function(){},isPersistent:ds}),e}var Fr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wc=et(Fr),Gi=se({},Fr,{view:0,detail:0}),E0=et(Gi),xa,va,Yr,Ho=se({},Gi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Sc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Yr&&(Yr&&t.type==="mousemove"?(xa=t.screenX-Yr.screenX,va=t.screenY-Yr.screenY):va=xa=0,Yr=t),xa)},movementY:function(t){return"movementY"in t?t.movementY:va}}),Ru=et(Ho),T0=se({},Ho,{dataTransfer:0}),R0=et(T0),O0=se({},Gi,{relatedTarget:0}),ya=et(O0),D0=se({},Fr,{animationName:0,elapsedTime:0,pseudoElement:0}),$0=et(D0),L0=se({},Fr,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),F0=et(L0),z0=se({},Fr,{data:0}),Ou=et(z0),A0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},I0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},U0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function H0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=U0[t])?!!e[t]:!1}function Sc(){return H0}var W0=se({},Gi,{key:function(t){if(t.key){var e=A0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=zs(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?I0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Sc,charCode:function(t){return t.type==="keypress"?zs(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?zs(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),B0=et(W0),V0=se({},Ho,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Du=et(V0),Y0=se({},Gi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Sc}),X0=et(Y0),Q0=se({},Fr,{propertyName:0,elapsedTime:0,pseudoElement:0}),K0=et(Q0),G0=se({},Ho,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),q0=et(G0),Z0=[9,13,27,32],_c=Ht&&"CompositionEvent"in window,mi=null;Ht&&"documentMode"in document&&(mi=document.documentMode);var J0=Ht&&"TextEvent"in window&&!mi,Yf=Ht&&(!_c||mi&&8<mi&&11>=mi),$u=" ",Lu=!1;function Xf(t,e){switch(t){case"keyup":return Z0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Qf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var or=!1;function ex(t,e){switch(t){case"compositionend":return Qf(e);case"keypress":return e.which!==32?null:(Lu=!0,$u);case"textInput":return t=e.data,t===$u&&Lu?null:t;default:return null}}function tx(t,e){if(or)return t==="compositionend"||!_c&&Xf(t,e)?(t=Vf(),Fs=bc=tn=null,or=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Yf&&e.locale!=="ko"?null:e.data;default:return null}}var nx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!nx[t.type]:e==="textarea"}function Kf(t,e,n,r){kf(r),e=oo(e,"onChange"),0<e.length&&(n=new wc("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var pi=null,Ei=null;function rx(t){om(t,0)}function Wo(t){var e=cr(t);if(yf(e))return t}function ix(t,e){if(t==="change")return e}var Gf=!1;if(Ht){var ba;if(Ht){var wa="oninput"in document;if(!wa){var zu=document.createElement("div");zu.setAttribute("oninput","return;"),wa=typeof zu.oninput=="function"}ba=wa}else ba=!1;Gf=ba&&(!document.documentMode||9<document.documentMode)}function Au(){pi&&(pi.detachEvent("onpropertychange",qf),Ei=pi=null)}function qf(t){if(t.propertyName==="value"&&Wo(Ei)){var e=[];Kf(e,Ei,t,pc(t)),Ef(rx,e)}}function sx(t,e,n){t==="focusin"?(Au(),pi=e,Ei=n,pi.attachEvent("onpropertychange",qf)):t==="focusout"&&Au()}function ox(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Wo(Ei)}function ax(t,e){if(t==="click")return Wo(e)}function lx(t,e){if(t==="input"||t==="change")return Wo(e)}function cx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var yt=typeof Object.is=="function"?Object.is:cx;function Ti(t,e){if(yt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!el.call(e,i)||!yt(t[i],e[i]))return!1}return!0}function Iu(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Uu(t,e){var n=Iu(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Iu(n)}}function Zf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Zf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Jf(){for(var t=window,e=Js();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Js(t.document)}return e}function Nc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function ux(t){var e=Jf(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&Zf(n.ownerDocument.documentElement,n)){if(r!==null&&Nc(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!t.extend&&s>r&&(i=r,r=s,s=i),i=Uu(n,s);var o=Uu(n,r);i&&o&&(t.rangeCount!==1||t.anchorNode!==i.node||t.anchorOffset!==i.offset||t.focusNode!==o.node||t.focusOffset!==o.offset)&&(e=e.createRange(),e.setStart(i.node,i.offset),t.removeAllRanges(),s>r?(t.addRange(e),t.extend(o.node,o.offset)):(e.setEnd(o.node,o.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var dx=Ht&&"documentMode"in document&&11>=document.documentMode,ar=null,yl=null,gi=null,bl=!1;function Hu(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;bl||ar==null||ar!==Js(r)||(r=ar,"selectionStart"in r&&Nc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),gi&&Ti(gi,r)||(gi=r,r=oo(yl,"onSelect"),0<r.length&&(e=new wc("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=ar)))}function hs(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var lr={animationend:hs("Animation","AnimationEnd"),animationiteration:hs("Animation","AnimationIteration"),animationstart:hs("Animation","AnimationStart"),transitionend:hs("Transition","TransitionEnd")},Sa={},em={};Ht&&(em=document.createElement("div").style,"AnimationEvent"in window||(delete lr.animationend.animation,delete lr.animationiteration.animation,delete lr.animationstart.animation),"TransitionEvent"in window||delete lr.transitionend.transition);function Bo(t){if(Sa[t])return Sa[t];if(!lr[t])return t;var e=lr[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in em)return Sa[t]=e[n];return t}var tm=Bo("animationend"),nm=Bo("animationiteration"),rm=Bo("animationstart"),im=Bo("transitionend"),sm=new Map,Wu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _n(t,e){sm.set(t,e),qn(e,[t])}for(var _a=0;_a<Wu.length;_a++){var Na=Wu[_a],hx=Na.toLowerCase(),fx=Na[0].toUpperCase()+Na.slice(1);_n(hx,"on"+fx)}_n(tm,"onAnimationEnd");_n(nm,"onAnimationIteration");_n(rm,"onAnimationStart");_n("dblclick","onDoubleClick");_n("focusin","onFocus");_n("focusout","onBlur");_n(im,"onTransitionEnd");Cr("onMouseEnter",["mouseout","mouseover"]);Cr("onMouseLeave",["mouseout","mouseover"]);Cr("onPointerEnter",["pointerout","pointerover"]);Cr("onPointerLeave",["pointerout","pointerover"]);qn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));qn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));qn("onBeforeInput",["compositionend","keypress","textInput","paste"]);qn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var oi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),mx=new Set("cancel close invalid load scroll toggle".split(" ").concat(oi));function Bu(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,h0(r,e,void 0,t),t.currentTarget=null}function om(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],i=r.event;r=r.listeners;e:{var s=void 0;if(e)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;Bu(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;Bu(i,a,u),s=l}}}if(to)throw t=pl,to=!1,pl=null,t}function J(t,e){var n=e[jl];n===void 0&&(n=e[jl]=new Set);var r=t+"__bubble";n.has(r)||(am(e,t,2,!1),n.add(r))}function ja(t,e,n){var r=0;e&&(r|=4),am(n,t,r,e)}var fs="_reactListening"+Math.random().toString(36).slice(2);function Ri(t){if(!t[fs]){t[fs]=!0,mf.forEach(function(n){n!=="selectionchange"&&(mx.has(n)||ja(n,!1,t),ja(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[fs]||(e[fs]=!0,ja("selectionchange",!1,e))}}function am(t,e,n,r){switch(Bf(e)){case 1:var i=M0;break;case 4:i=P0;break;default:i=yc}n=i.bind(null,e,n,t),i=void 0,!ml||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),r?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function ka(t,e,n,r,i){var s=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=Ln(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}Ef(function(){var u=s,d=pc(n),h=[];e:{var f=sm.get(t);if(f!==void 0){var m=wc,v=t;switch(t){case"keypress":if(zs(n)===0)break e;case"keydown":case"keyup":m=B0;break;case"focusin":v="focus",m=ya;break;case"focusout":v="blur",m=ya;break;case"beforeblur":case"afterblur":m=ya;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Ru;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=R0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=X0;break;case tm:case nm:case rm:m=$0;break;case im:m=K0;break;case"scroll":m=E0;break;case"wheel":m=q0;break;case"copy":case"cut":case"paste":m=F0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Du}var x=(e&4)!==0,b=!x&&t==="scroll",g=x?f!==null?f+"Capture":null:f;x=[];for(var p=u,y;p!==null;){y=p;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,g!==null&&(w=ki(p,g),w!=null&&x.push(Oi(p,w,y)))),b)break;p=p.return}0<x.length&&(f=new m(f,v,null,n,d),h.push({event:f,listeners:x}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",f&&n!==hl&&(v=n.relatedTarget||n.fromElement)&&(Ln(v)||v[Wt]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(v=n.relatedTarget||n.toElement,m=u,v=v?Ln(v):null,v!==null&&(b=Zn(v),v!==b||v.tag!==5&&v.tag!==6)&&(v=null)):(m=null,v=u),m!==v)){if(x=Ru,w="onMouseLeave",g="onMouseEnter",p="mouse",(t==="pointerout"||t==="pointerover")&&(x=Du,w="onPointerLeave",g="onPointerEnter",p="pointer"),b=m==null?f:cr(m),y=v==null?f:cr(v),f=new x(w,p+"leave",m,n,d),f.target=b,f.relatedTarget=y,w=null,Ln(d)===u&&(x=new x(g,p+"enter",v,n,d),x.target=y,x.relatedTarget=b,w=x),b=w,m&&v)t:{for(x=m,g=v,p=0,y=x;y;y=er(y))p++;for(y=0,w=g;w;w=er(w))y++;for(;0<p-y;)x=er(x),p--;for(;0<y-p;)g=er(g),y--;for(;p--;){if(x===g||g!==null&&x===g.alternate)break t;x=er(x),g=er(g)}x=null}else x=null;m!==null&&Vu(h,f,m,x,!1),v!==null&&b!==null&&Vu(h,b,v,x,!0)}}e:{if(f=u?cr(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var _=ix;else if(Fu(f))if(Gf)_=lx;else{_=ox;var S=sx}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(_=ax);if(_&&(_=_(t,u))){Kf(h,_,n,d);break e}S&&S(t,f,u),t==="focusout"&&(S=f._wrapperState)&&S.controlled&&f.type==="number"&&al(f,"number",f.value)}switch(S=u?cr(u):window,t){case"focusin":(Fu(S)||S.contentEditable==="true")&&(ar=S,yl=u,gi=null);break;case"focusout":gi=yl=ar=null;break;case"mousedown":bl=!0;break;case"contextmenu":case"mouseup":case"dragend":bl=!1,Hu(h,n,d);break;case"selectionchange":if(dx)break;case"keydown":case"keyup":Hu(h,n,d)}var N;if(_c)e:{switch(t){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else or?Xf(t,n)&&(k="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(Yf&&n.locale!=="ko"&&(or||k!=="onCompositionStart"?k==="onCompositionEnd"&&or&&(N=Vf()):(tn=d,bc="value"in tn?tn.value:tn.textContent,or=!0)),S=oo(u,k),0<S.length&&(k=new Ou(k,t,null,n,d),h.push({event:k,listeners:S}),N?k.data=N:(N=Qf(n),N!==null&&(k.data=N)))),(N=J0?ex(t,n):tx(t,n))&&(u=oo(u,"onBeforeInput"),0<u.length&&(d=new Ou("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=N))}om(h,e)})}function Oi(t,e,n){return{instance:t,listener:e,currentTarget:n}}function oo(t,e){for(var n=e+"Capture",r=[];t!==null;){var i=t,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=ki(t,n),s!=null&&r.unshift(Oi(t,s,i)),s=ki(t,e),s!=null&&r.push(Oi(t,s,i))),t=t.return}return r}function er(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Vu(t,e,n,r,i){for(var s=e._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=ki(n,s),l!=null&&o.unshift(Oi(n,l,a))):i||(l=ki(n,s),l!=null&&o.push(Oi(n,l,a)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var px=/\r\n?/g,gx=/\u0000|\uFFFD/g;function Yu(t){return(typeof t=="string"?t:""+t).replace(px,`
`).replace(gx,"")}function ms(t,e,n){if(e=Yu(e),Yu(t)!==e&&n)throw Error(C(425))}function ao(){}var wl=null,Sl=null;function _l(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Nl=typeof setTimeout=="function"?setTimeout:void 0,xx=typeof clearTimeout=="function"?clearTimeout:void 0,Xu=typeof Promise=="function"?Promise:void 0,vx=typeof queueMicrotask=="function"?queueMicrotask:typeof Xu<"u"?function(t){return Xu.resolve(null).then(t).catch(yx)}:Nl;function yx(t){setTimeout(function(){throw t})}function Ca(t,e){var n=e,r=0;do{var i=n.nextSibling;if(t.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){t.removeChild(i),Pi(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Pi(e)}function dn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function Qu(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var zr=Math.random().toString(36).slice(2),kt="__reactFiber$"+zr,Di="__reactProps$"+zr,Wt="__reactContainer$"+zr,jl="__reactEvents$"+zr,bx="__reactListeners$"+zr,wx="__reactHandles$"+zr;function Ln(t){var e=t[kt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Wt]||n[kt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Qu(t);t!==null;){if(n=t[kt])return n;t=Qu(t)}return e}t=n,n=t.parentNode}return null}function qi(t){return t=t[kt]||t[Wt],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function cr(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(C(33))}function Vo(t){return t[Di]||null}var kl=[],ur=-1;function Nn(t){return{current:t}}function te(t){0>ur||(t.current=kl[ur],kl[ur]=null,ur--)}function G(t,e){ur++,kl[ur]=t.current,t.current=e}var wn={},Ee=Nn(wn),Ve=Nn(!1),Wn=wn;function Mr(t,e){var n=t.type.contextTypes;if(!n)return wn;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=e[s];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function Ye(t){return t=t.childContextTypes,t!=null}function lo(){te(Ve),te(Ee)}function Ku(t,e,n){if(Ee.current!==wn)throw Error(C(168));G(Ee,e),G(Ve,n)}function lm(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in e))throw Error(C(108,s0(t)||"Unknown",i));return se({},n,r)}function co(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||wn,Wn=Ee.current,G(Ee,t),G(Ve,Ve.current),!0}function Gu(t,e,n){var r=t.stateNode;if(!r)throw Error(C(169));n?(t=lm(t,e,Wn),r.__reactInternalMemoizedMergedChildContext=t,te(Ve),te(Ee),G(Ee,t)):te(Ve),G(Ve,n)}var $t=null,Yo=!1,Ma=!1;function cm(t){$t===null?$t=[t]:$t.push(t)}function Sx(t){Yo=!0,cm(t)}function jn(){if(!Ma&&$t!==null){Ma=!0;var t=0,e=X;try{var n=$t;for(X=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}$t=null,Yo=!1}catch(i){throw $t!==null&&($t=$t.slice(t+1)),Df(gc,jn),i}finally{X=e,Ma=!1}}return null}var dr=[],hr=0,uo=null,ho=0,nt=[],rt=0,Bn=null,zt=1,At="";function Tn(t,e){dr[hr++]=ho,dr[hr++]=uo,uo=t,ho=e}function um(t,e,n){nt[rt++]=zt,nt[rt++]=At,nt[rt++]=Bn,Bn=t;var r=zt;t=At;var i=32-xt(r)-1;r&=~(1<<i),n+=1;var s=32-xt(e)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,zt=1<<32-xt(e)+i|n<<i|r,At=s+t}else zt=1<<s|n<<i|r,At=t}function jc(t){t.return!==null&&(Tn(t,1),um(t,1,0))}function kc(t){for(;t===uo;)uo=dr[--hr],dr[hr]=null,ho=dr[--hr],dr[hr]=null;for(;t===Bn;)Bn=nt[--rt],nt[rt]=null,At=nt[--rt],nt[rt]=null,zt=nt[--rt],nt[rt]=null}var qe=null,Ge=null,ne=!1,mt=null;function dm(t,e){var n=it(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function qu(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,qe=t,Ge=dn(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,qe=t,Ge=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Bn!==null?{id:zt,overflow:At}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=it(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,qe=t,Ge=null,!0):!1;default:return!1}}function Cl(t){return(t.mode&1)!==0&&(t.flags&128)===0}function Ml(t){if(ne){var e=Ge;if(e){var n=e;if(!qu(t,e)){if(Cl(t))throw Error(C(418));e=dn(n.nextSibling);var r=qe;e&&qu(t,e)?dm(r,n):(t.flags=t.flags&-4097|2,ne=!1,qe=t)}}else{if(Cl(t))throw Error(C(418));t.flags=t.flags&-4097|2,ne=!1,qe=t}}}function Zu(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;qe=t}function ps(t){if(t!==qe)return!1;if(!ne)return Zu(t),ne=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!_l(t.type,t.memoizedProps)),e&&(e=Ge)){if(Cl(t))throw hm(),Error(C(418));for(;e;)dm(t,e),e=dn(e.nextSibling)}if(Zu(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(C(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Ge=dn(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Ge=null}}else Ge=qe?dn(t.stateNode.nextSibling):null;return!0}function hm(){for(var t=Ge;t;)t=dn(t.nextSibling)}function Pr(){Ge=qe=null,ne=!1}function Cc(t){mt===null?mt=[t]:mt.push(t)}var _x=Xt.ReactCurrentBatchConfig;function Xr(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,t));var i=r,s=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===s?e.ref:(e=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},e._stringRef=s,e)}if(typeof t!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,t))}return t}function gs(t,e){throw t=Object.prototype.toString.call(e),Error(C(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Ju(t){var e=t._init;return e(t._payload)}function fm(t){function e(g,p){if(t){var y=g.deletions;y===null?(g.deletions=[p],g.flags|=16):y.push(p)}}function n(g,p){if(!t)return null;for(;p!==null;)e(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function i(g,p){return g=pn(g,p),g.index=0,g.sibling=null,g}function s(g,p,y){return g.index=y,t?(y=g.alternate,y!==null?(y=y.index,y<p?(g.flags|=2,p):y):(g.flags|=2,p)):(g.flags|=1048576,p)}function o(g){return t&&g.alternate===null&&(g.flags|=2),g}function a(g,p,y,w){return p===null||p.tag!==6?(p=$a(y,g.mode,w),p.return=g,p):(p=i(p,y),p.return=g,p)}function l(g,p,y,w){var _=y.type;return _===sr?d(g,p,y.props.children,w,y.key):p!==null&&(p.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===qt&&Ju(_)===p.type)?(w=i(p,y.props),w.ref=Xr(g,p,y),w.return=g,w):(w=Vs(y.type,y.key,y.props,null,g.mode,w),w.ref=Xr(g,p,y),w.return=g,w)}function u(g,p,y,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==y.containerInfo||p.stateNode.implementation!==y.implementation?(p=La(y,g.mode,w),p.return=g,p):(p=i(p,y.children||[]),p.return=g,p)}function d(g,p,y,w,_){return p===null||p.tag!==7?(p=Un(y,g.mode,w,_),p.return=g,p):(p=i(p,y),p.return=g,p)}function h(g,p,y){if(typeof p=="string"&&p!==""||typeof p=="number")return p=$a(""+p,g.mode,y),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ss:return y=Vs(p.type,p.key,p.props,null,g.mode,y),y.ref=Xr(g,null,p),y.return=g,y;case ir:return p=La(p,g.mode,y),p.return=g,p;case qt:var w=p._init;return h(g,w(p._payload),y)}if(ii(p)||Hr(p))return p=Un(p,g.mode,y,null),p.return=g,p;gs(g,p)}return null}function f(g,p,y,w){var _=p!==null?p.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return _!==null?null:a(g,p,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ss:return y.key===_?l(g,p,y,w):null;case ir:return y.key===_?u(g,p,y,w):null;case qt:return _=y._init,f(g,p,_(y._payload),w)}if(ii(y)||Hr(y))return _!==null?null:d(g,p,y,w,null);gs(g,y)}return null}function m(g,p,y,w,_){if(typeof w=="string"&&w!==""||typeof w=="number")return g=g.get(y)||null,a(p,g,""+w,_);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ss:return g=g.get(w.key===null?y:w.key)||null,l(p,g,w,_);case ir:return g=g.get(w.key===null?y:w.key)||null,u(p,g,w,_);case qt:var S=w._init;return m(g,p,y,S(w._payload),_)}if(ii(w)||Hr(w))return g=g.get(y)||null,d(p,g,w,_,null);gs(p,w)}return null}function v(g,p,y,w){for(var _=null,S=null,N=p,k=p=0,T=null;N!==null&&k<y.length;k++){N.index>k?(T=N,N=null):T=N.sibling;var M=f(g,N,y[k],w);if(M===null){N===null&&(N=T);break}t&&N&&M.alternate===null&&e(g,N),p=s(M,p,k),S===null?_=M:S.sibling=M,S=M,N=T}if(k===y.length)return n(g,N),ne&&Tn(g,k),_;if(N===null){for(;k<y.length;k++)N=h(g,y[k],w),N!==null&&(p=s(N,p,k),S===null?_=N:S.sibling=N,S=N);return ne&&Tn(g,k),_}for(N=r(g,N);k<y.length;k++)T=m(N,g,k,y[k],w),T!==null&&(t&&T.alternate!==null&&N.delete(T.key===null?k:T.key),p=s(T,p,k),S===null?_=T:S.sibling=T,S=T);return t&&N.forEach(function(E){return e(g,E)}),ne&&Tn(g,k),_}function x(g,p,y,w){var _=Hr(y);if(typeof _!="function")throw Error(C(150));if(y=_.call(y),y==null)throw Error(C(151));for(var S=_=null,N=p,k=p=0,T=null,M=y.next();N!==null&&!M.done;k++,M=y.next()){N.index>k?(T=N,N=null):T=N.sibling;var E=f(g,N,M.value,w);if(E===null){N===null&&(N=T);break}t&&N&&E.alternate===null&&e(g,N),p=s(E,p,k),S===null?_=E:S.sibling=E,S=E,N=T}if(M.done)return n(g,N),ne&&Tn(g,k),_;if(N===null){for(;!M.done;k++,M=y.next())M=h(g,M.value,w),M!==null&&(p=s(M,p,k),S===null?_=M:S.sibling=M,S=M);return ne&&Tn(g,k),_}for(N=r(g,N);!M.done;k++,M=y.next())M=m(N,g,k,M.value,w),M!==null&&(t&&M.alternate!==null&&N.delete(M.key===null?k:M.key),p=s(M,p,k),S===null?_=M:S.sibling=M,S=M);return t&&N.forEach(function($){return e(g,$)}),ne&&Tn(g,k),_}function b(g,p,y,w){if(typeof y=="object"&&y!==null&&y.type===sr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ss:e:{for(var _=y.key,S=p;S!==null;){if(S.key===_){if(_=y.type,_===sr){if(S.tag===7){n(g,S.sibling),p=i(S,y.props.children),p.return=g,g=p;break e}}else if(S.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===qt&&Ju(_)===S.type){n(g,S.sibling),p=i(S,y.props),p.ref=Xr(g,S,y),p.return=g,g=p;break e}n(g,S);break}else e(g,S);S=S.sibling}y.type===sr?(p=Un(y.props.children,g.mode,w,y.key),p.return=g,g=p):(w=Vs(y.type,y.key,y.props,null,g.mode,w),w.ref=Xr(g,p,y),w.return=g,g=w)}return o(g);case ir:e:{for(S=y.key;p!==null;){if(p.key===S)if(p.tag===4&&p.stateNode.containerInfo===y.containerInfo&&p.stateNode.implementation===y.implementation){n(g,p.sibling),p=i(p,y.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else e(g,p);p=p.sibling}p=La(y,g.mode,w),p.return=g,g=p}return o(g);case qt:return S=y._init,b(g,p,S(y._payload),w)}if(ii(y))return v(g,p,y,w);if(Hr(y))return x(g,p,y,w);gs(g,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,p!==null&&p.tag===6?(n(g,p.sibling),p=i(p,y),p.return=g,g=p):(n(g,p),p=$a(y,g.mode,w),p.return=g,g=p),o(g)):n(g,p)}return b}var Er=fm(!0),mm=fm(!1),fo=Nn(null),mo=null,fr=null,Mc=null;function Pc(){Mc=fr=mo=null}function Ec(t){var e=fo.current;te(fo),t._currentValue=e}function Pl(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function br(t,e){mo=t,Mc=fr=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(We=!0),t.firstContext=null)}function at(t){var e=t._currentValue;if(Mc!==t)if(t={context:t,memoizedValue:e,next:null},fr===null){if(mo===null)throw Error(C(308));fr=t,mo.dependencies={lanes:0,firstContext:t}}else fr=fr.next=t;return e}var Fn=null;function Tc(t){Fn===null?Fn=[t]:Fn.push(t)}function pm(t,e,n,r){var i=e.interleaved;return i===null?(n.next=n,Tc(e)):(n.next=i.next,i.next=n),e.interleaved=n,Bt(t,r)}function Bt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Zt=!1;function Rc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function gm(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Ut(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function hn(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var i=r.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),r.pending=e,Bt(t,n)}return i=r.interleaved,i===null?(e.next=e,Tc(r)):(e.next=i.next,i.next=e),r.interleaved=e,Bt(t,n)}function As(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,xc(t,n)}}function ed(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=e:s=s.next=e}else i=s=e;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function po(t,e,n,r){var i=t.updateQueue;Zt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var d=t.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(s!==null){var h=i.baseState;o=0,d=u=l=null,a=s;do{var f=a.lane,m=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=t,x=a;switch(f=e,m=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){h=v.call(m,h,f);break e}h=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(m,h,f):v,f==null)break e;h=se({},h,f);break e;case 2:Zt=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else m={eventTime:m,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=m,l=h):d=d.next=m,o|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(d===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,e=i.shared.interleaved,e!==null){i=e;do o|=i.lane,i=i.next;while(i!==e)}else s===null&&(i.shared.lanes=0);Yn|=o,t.lanes=o,t.memoizedState=h}}function td(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var Zi={},Mt=Nn(Zi),$i=Nn(Zi),Li=Nn(Zi);function zn(t){if(t===Zi)throw Error(C(174));return t}function Oc(t,e){switch(G(Li,e),G($i,t),G(Mt,Zi),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:cl(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=cl(e,t)}te(Mt),G(Mt,e)}function Tr(){te(Mt),te($i),te(Li)}function xm(t){zn(Li.current);var e=zn(Mt.current),n=cl(e,t.type);e!==n&&(G($i,t),G(Mt,n))}function Dc(t){$i.current===t&&(te(Mt),te($i))}var re=Nn(0);function go(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var Pa=[];function $c(){for(var t=0;t<Pa.length;t++)Pa[t]._workInProgressVersionPrimary=null;Pa.length=0}var Is=Xt.ReactCurrentDispatcher,Ea=Xt.ReactCurrentBatchConfig,Vn=0,ie=null,me=null,ve=null,xo=!1,xi=!1,Fi=0,Nx=0;function Ne(){throw Error(C(321))}function Lc(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!yt(t[n],e[n]))return!1;return!0}function Fc(t,e,n,r,i,s){if(Vn=s,ie=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Is.current=t===null||t.memoizedState===null?Mx:Px,t=n(r,i),xi){s=0;do{if(xi=!1,Fi=0,25<=s)throw Error(C(301));s+=1,ve=me=null,e.updateQueue=null,Is.current=Ex,t=n(r,i)}while(xi)}if(Is.current=vo,e=me!==null&&me.next!==null,Vn=0,ve=me=ie=null,xo=!1,e)throw Error(C(300));return t}function zc(){var t=Fi!==0;return Fi=0,t}function Nt(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ve===null?ie.memoizedState=ve=t:ve=ve.next=t,ve}function lt(){if(me===null){var t=ie.alternate;t=t!==null?t.memoizedState:null}else t=me.next;var e=ve===null?ie.memoizedState:ve.next;if(e!==null)ve=e,me=t;else{if(t===null)throw Error(C(310));me=t,t={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ve===null?ie.memoizedState=ve=t:ve=ve.next=t}return ve}function zi(t,e){return typeof e=="function"?e(t):e}function Ta(t){var e=lt(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=me,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var d=u.lane;if((Vn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,o=r):l=l.next=h,ie.lanes|=d,Yn|=d}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,yt(r,e.memoizedState)||(We=!0),e.memoizedState=r,e.baseState=o,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){i=t;do s=i.lane,ie.lanes|=s,Yn|=s,i=i.next;while(i!==t)}else i===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function Ra(t){var e=lt(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=n.dispatch,i=n.pending,s=e.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=t(s,o.action),o=o.next;while(o!==i);yt(s,e.memoizedState)||(We=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),n.lastRenderedState=s}return[s,r]}function vm(){}function ym(t,e){var n=ie,r=lt(),i=e(),s=!yt(r.memoizedState,i);if(s&&(r.memoizedState=i,We=!0),r=r.queue,Ac(Sm.bind(null,n,r,t),[t]),r.getSnapshot!==e||s||ve!==null&&ve.memoizedState.tag&1){if(n.flags|=2048,Ai(9,wm.bind(null,n,r,i,e),void 0,null),ye===null)throw Error(C(349));Vn&30||bm(n,e,i)}return i}function bm(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ie.updateQueue,e===null?(e={lastEffect:null,stores:null},ie.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function wm(t,e,n,r){e.value=n,e.getSnapshot=r,_m(e)&&Nm(t)}function Sm(t,e,n){return n(function(){_m(e)&&Nm(t)})}function _m(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!yt(t,n)}catch{return!0}}function Nm(t){var e=Bt(t,1);e!==null&&vt(e,t,1,-1)}function nd(t){var e=Nt();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:zi,lastRenderedState:t},e.queue=t,t=t.dispatch=Cx.bind(null,ie,t),[e.memoizedState,t]}function Ai(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=ie.updateQueue,e===null?(e={lastEffect:null,stores:null},ie.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function jm(){return lt().memoizedState}function Us(t,e,n,r){var i=Nt();ie.flags|=t,i.memoizedState=Ai(1|e,n,void 0,r===void 0?null:r)}function Xo(t,e,n,r){var i=lt();r=r===void 0?null:r;var s=void 0;if(me!==null){var o=me.memoizedState;if(s=o.destroy,r!==null&&Lc(r,o.deps)){i.memoizedState=Ai(e,n,s,r);return}}ie.flags|=t,i.memoizedState=Ai(1|e,n,s,r)}function rd(t,e){return Us(8390656,8,t,e)}function Ac(t,e){return Xo(2048,8,t,e)}function km(t,e){return Xo(4,2,t,e)}function Cm(t,e){return Xo(4,4,t,e)}function Mm(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Pm(t,e,n){return n=n!=null?n.concat([t]):null,Xo(4,4,Mm.bind(null,e,t),n)}function Ic(){}function Em(t,e){var n=lt();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Lc(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function Tm(t,e){var n=lt();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Lc(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function Rm(t,e,n){return Vn&21?(yt(n,e)||(n=Ff(),ie.lanes|=n,Yn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,We=!0),t.memoizedState=n)}function jx(t,e){var n=X;X=n!==0&&4>n?n:4,t(!0);var r=Ea.transition;Ea.transition={};try{t(!1),e()}finally{X=n,Ea.transition=r}}function Om(){return lt().memoizedState}function kx(t,e,n){var r=mn(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Dm(t))$m(e,n);else if(n=pm(t,e,n,r),n!==null){var i=Le();vt(n,t,r,i),Lm(n,e,r)}}function Cx(t,e,n){var r=mn(t),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Dm(t))$m(e,i);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var o=e.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,yt(a,o)){var l=e.interleaved;l===null?(i.next=i,Tc(e)):(i.next=l.next,l.next=i),e.interleaved=i;return}}catch{}finally{}n=pm(t,e,i,r),n!==null&&(i=Le(),vt(n,t,r,i),Lm(n,e,r))}}function Dm(t){var e=t.alternate;return t===ie||e!==null&&e===ie}function $m(t,e){xi=xo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Lm(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,xc(t,n)}}var vo={readContext:at,useCallback:Ne,useContext:Ne,useEffect:Ne,useImperativeHandle:Ne,useInsertionEffect:Ne,useLayoutEffect:Ne,useMemo:Ne,useReducer:Ne,useRef:Ne,useState:Ne,useDebugValue:Ne,useDeferredValue:Ne,useTransition:Ne,useMutableSource:Ne,useSyncExternalStore:Ne,useId:Ne,unstable_isNewReconciler:!1},Mx={readContext:at,useCallback:function(t,e){return Nt().memoizedState=[t,e===void 0?null:e],t},useContext:at,useEffect:rd,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Us(4194308,4,Mm.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Us(4194308,4,t,e)},useInsertionEffect:function(t,e){return Us(4,2,t,e)},useMemo:function(t,e){var n=Nt();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=Nt();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=kx.bind(null,ie,t),[r.memoizedState,t]},useRef:function(t){var e=Nt();return t={current:t},e.memoizedState=t},useState:nd,useDebugValue:Ic,useDeferredValue:function(t){return Nt().memoizedState=t},useTransition:function(){var t=nd(!1),e=t[0];return t=jx.bind(null,t[1]),Nt().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=ie,i=Nt();if(ne){if(n===void 0)throw Error(C(407));n=n()}else{if(n=e(),ye===null)throw Error(C(349));Vn&30||bm(r,e,n)}i.memoizedState=n;var s={value:n,getSnapshot:e};return i.queue=s,rd(Sm.bind(null,r,s,t),[t]),r.flags|=2048,Ai(9,wm.bind(null,r,s,n,e),void 0,null),n},useId:function(){var t=Nt(),e=ye.identifierPrefix;if(ne){var n=At,r=zt;n=(r&~(1<<32-xt(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=Fi++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Nx++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Px={readContext:at,useCallback:Em,useContext:at,useEffect:Ac,useImperativeHandle:Pm,useInsertionEffect:km,useLayoutEffect:Cm,useMemo:Tm,useReducer:Ta,useRef:jm,useState:function(){return Ta(zi)},useDebugValue:Ic,useDeferredValue:function(t){var e=lt();return Rm(e,me.memoizedState,t)},useTransition:function(){var t=Ta(zi)[0],e=lt().memoizedState;return[t,e]},useMutableSource:vm,useSyncExternalStore:ym,useId:Om,unstable_isNewReconciler:!1},Ex={readContext:at,useCallback:Em,useContext:at,useEffect:Ac,useImperativeHandle:Pm,useInsertionEffect:km,useLayoutEffect:Cm,useMemo:Tm,useReducer:Ra,useRef:jm,useState:function(){return Ra(zi)},useDebugValue:Ic,useDeferredValue:function(t){var e=lt();return me===null?e.memoizedState=t:Rm(e,me.memoizedState,t)},useTransition:function(){var t=Ra(zi)[0],e=lt().memoizedState;return[t,e]},useMutableSource:vm,useSyncExternalStore:ym,useId:Om,unstable_isNewReconciler:!1};function ht(t,e){if(t&&t.defaultProps){e=se({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function El(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:se({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Qo={isMounted:function(t){return(t=t._reactInternals)?Zn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=Le(),i=mn(t),s=Ut(r,i);s.payload=e,n!=null&&(s.callback=n),e=hn(t,s,i),e!==null&&(vt(e,t,i,r),As(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=Le(),i=mn(t),s=Ut(r,i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=hn(t,s,i),e!==null&&(vt(e,t,i,r),As(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Le(),r=mn(t),i=Ut(n,r);i.tag=2,e!=null&&(i.callback=e),e=hn(t,i,r),e!==null&&(vt(e,t,r,n),As(e,t,r))}};function id(t,e,n,r,i,s,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,s,o):e.prototype&&e.prototype.isPureReactComponent?!Ti(n,r)||!Ti(i,s):!0}function Fm(t,e,n){var r=!1,i=wn,s=e.contextType;return typeof s=="object"&&s!==null?s=at(s):(i=Ye(e)?Wn:Ee.current,r=e.contextTypes,s=(r=r!=null)?Mr(t,i):wn),e=new e(n,s),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Qo,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=s),e}function sd(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Qo.enqueueReplaceState(e,e.state,null)}function Tl(t,e,n,r){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs={},Rc(t);var s=e.contextType;typeof s=="object"&&s!==null?i.context=at(s):(s=Ye(e)?Wn:Ee.current,i.context=Mr(t,s)),i.state=t.memoizedState,s=e.getDerivedStateFromProps,typeof s=="function"&&(El(t,e,s,n),i.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(e=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),e!==i.state&&Qo.enqueueReplaceState(i,i.state,null),po(t,n,i,r),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308)}function Rr(t,e){try{var n="",r=e;do n+=i0(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:t,source:e,stack:i,digest:null}}function Oa(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Rl(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Tx=typeof WeakMap=="function"?WeakMap:Map;function zm(t,e,n){n=Ut(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){bo||(bo=!0,Hl=r),Rl(t,e)},n}function Am(t,e,n){n=Ut(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var i=e.value;n.payload=function(){return r(i)},n.callback=function(){Rl(t,e)}}var s=t.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Rl(t,e),typeof r!="function"&&(fn===null?fn=new Set([this]):fn.add(this));var o=e.stack;this.componentDidCatch(e.value,{componentStack:o!==null?o:""})}),n}function od(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Tx;var i=new Set;r.set(e,i)}else i=r.get(e),i===void 0&&(i=new Set,r.set(e,i));i.has(n)||(i.add(n),t=Vx.bind(null,t,e,n),e.then(t,t))}function ad(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function ld(t,e,n,r,i){return t.mode&1?(t.flags|=65536,t.lanes=i,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=Ut(-1,1),e.tag=2,hn(n,e,1))),n.lanes|=1),t)}var Rx=Xt.ReactCurrentOwner,We=!1;function Oe(t,e,n,r){e.child=t===null?mm(e,null,n,r):Er(e,t.child,n,r)}function cd(t,e,n,r,i){n=n.render;var s=e.ref;return br(e,i),r=Fc(t,e,n,r,s,i),n=zc(),t!==null&&!We?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Vt(t,e,i)):(ne&&n&&jc(e),e.flags|=1,Oe(t,e,r,i),e.child)}function ud(t,e,n,r,i){if(t===null){var s=n.type;return typeof s=="function"&&!Qc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=s,Im(t,e,s,r,i)):(t=Vs(n.type,null,r,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!(t.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Ti,n(o,r)&&t.ref===e.ref)return Vt(t,e,i)}return e.flags|=1,t=pn(s,r),t.ref=e.ref,t.return=e,e.child=t}function Im(t,e,n,r,i){if(t!==null){var s=t.memoizedProps;if(Ti(s,r)&&t.ref===e.ref)if(We=!1,e.pendingProps=r=s,(t.lanes&i)!==0)t.flags&131072&&(We=!0);else return e.lanes=t.lanes,Vt(t,e,i)}return Ol(t,e,n,r,i)}function Um(t,e,n){var r=e.pendingProps,i=r.children,s=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},G(pr,Ke),Ke|=n;else{if(!(n&1073741824))return t=s!==null?s.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,G(pr,Ke),Ke|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,G(pr,Ke),Ke|=r}else s!==null?(r=s.baseLanes|n,e.memoizedState=null):r=n,G(pr,Ke),Ke|=r;return Oe(t,e,i,n),e.child}function Hm(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function Ol(t,e,n,r,i){var s=Ye(n)?Wn:Ee.current;return s=Mr(e,s),br(e,i),n=Fc(t,e,n,r,s,i),r=zc(),t!==null&&!We?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Vt(t,e,i)):(ne&&r&&jc(e),e.flags|=1,Oe(t,e,n,i),e.child)}function dd(t,e,n,r,i){if(Ye(n)){var s=!0;co(e)}else s=!1;if(br(e,i),e.stateNode===null)Hs(t,e),Fm(e,n,r),Tl(e,n,r,i),r=!0;else if(t===null){var o=e.stateNode,a=e.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=at(u):(u=Ye(n)?Wn:Ee.current,u=Mr(e,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&sd(e,o,r,u),Zt=!1;var f=e.memoizedState;o.state=f,po(e,r,o,i),l=e.memoizedState,a!==r||f!==l||Ve.current||Zt?(typeof d=="function"&&(El(e,n,d,r),l=e.memoizedState),(a=Zt||id(e,n,a,r,f,l,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{o=e.stateNode,gm(t,e),a=e.memoizedProps,u=e.type===e.elementType?a:ht(e.type,a),o.props=u,h=e.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=at(l):(l=Ye(n)?Wn:Ee.current,l=Mr(e,l));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==l)&&sd(e,o,r,l),Zt=!1,f=e.memoizedState,o.state=f,po(e,r,o,i);var v=e.memoizedState;a!==h||f!==v||Ve.current||Zt?(typeof m=="function"&&(El(e,n,m,r),v=e.memoizedState),(u=Zt||id(e,n,u,r,f,v,l)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),r=!1)}return Dl(t,e,n,r,s,i)}function Dl(t,e,n,r,i,s){Hm(t,e);var o=(e.flags&128)!==0;if(!r&&!o)return i&&Gu(e,n,!1),Vt(t,e,s);r=e.stateNode,Rx.current=e;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&o?(e.child=Er(e,t.child,null,s),e.child=Er(e,null,a,s)):Oe(t,e,a,s),e.memoizedState=r.state,i&&Gu(e,n,!0),e.child}function Wm(t){var e=t.stateNode;e.pendingContext?Ku(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Ku(t,e.context,!1),Oc(t,e.containerInfo)}function hd(t,e,n,r,i){return Pr(),Cc(i),e.flags|=256,Oe(t,e,n,r),e.child}var $l={dehydrated:null,treeContext:null,retryLane:0};function Ll(t){return{baseLanes:t,cachePool:null,transitions:null}}function Bm(t,e,n){var r=e.pendingProps,i=re.current,s=!1,o=(e.flags&128)!==0,a;if((a=o)||(a=t!==null&&t.memoizedState===null?!1:(i&2)!==0),a?(s=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(i|=1),G(re,i&1),t===null)return Ml(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(o=r.children,t=r.fallback,s?(r=e.mode,s=e.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=qo(o,r,0,null),t=Un(t,r,n,null),s.return=e,t.return=e,s.sibling=t,e.child=s,e.child.memoizedState=Ll(n),e.memoizedState=$l,t):Uc(e,o));if(i=t.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Ox(t,e,o,r,a,i,n);if(s){s=r.fallback,o=e.mode,i=t.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&e.child!==i?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=pn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=pn(a,s):(s=Un(s,o,n,null),s.flags|=2),s.return=e,r.return=e,r.sibling=s,e.child=r,r=s,s=e.child,o=t.child.memoizedState,o=o===null?Ll(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=t.childLanes&~n,e.memoizedState=$l,r}return s=t.child,t=s.sibling,r=pn(s,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function Uc(t,e){return e=qo({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function xs(t,e,n,r){return r!==null&&Cc(r),Er(e,t.child,null,n),t=Uc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Ox(t,e,n,r,i,s,o){if(n)return e.flags&256?(e.flags&=-257,r=Oa(Error(C(422))),xs(t,e,o,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(s=r.fallback,i=e.mode,r=qo({mode:"visible",children:r.children},i,0,null),s=Un(s,i,o,null),s.flags|=2,r.return=e,s.return=e,r.sibling=s,e.child=r,e.mode&1&&Er(e,t.child,null,o),e.child.memoizedState=Ll(o),e.memoizedState=$l,s);if(!(e.mode&1))return xs(t,e,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(C(419)),r=Oa(s,r,void 0),xs(t,e,o,r)}if(a=(o&t.childLanes)!==0,We||a){if(r=ye,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Bt(t,i),vt(r,t,i,-1))}return Xc(),r=Oa(Error(C(421))),xs(t,e,o,r)}return i.data==="$?"?(e.flags|=128,e.child=t.child,e=Yx.bind(null,t),i._reactRetry=e,null):(t=s.treeContext,Ge=dn(i.nextSibling),qe=e,ne=!0,mt=null,t!==null&&(nt[rt++]=zt,nt[rt++]=At,nt[rt++]=Bn,zt=t.id,At=t.overflow,Bn=e),e=Uc(e,r.children),e.flags|=4096,e)}function fd(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Pl(t.return,e,n)}function Da(t,e,n,r,i){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Vm(t,e,n){var r=e.pendingProps,i=r.revealOrder,s=r.tail;if(Oe(t,e,r.children,n),r=re.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&fd(t,n,e);else if(t.tag===19)fd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(G(re,r),!(e.mode&1))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&go(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),Da(e,!1,i,n,s);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&go(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}Da(e,!0,n,null,s);break;case"together":Da(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Hs(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Vt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Yn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(C(153));if(e.child!==null){for(t=e.child,n=pn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=pn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Dx(t,e,n){switch(e.tag){case 3:Wm(e),Pr();break;case 5:xm(e);break;case 1:Ye(e.type)&&co(e);break;case 4:Oc(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,i=e.memoizedProps.value;G(fo,r._currentValue),r._currentValue=i;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(G(re,re.current&1),e.flags|=128,null):n&e.child.childLanes?Bm(t,e,n):(G(re,re.current&1),t=Vt(t,e,n),t!==null?t.sibling:null);G(re,re.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return Vm(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),G(re,re.current),r)break;return null;case 22:case 23:return e.lanes=0,Um(t,e,n)}return Vt(t,e,n)}var Ym,Fl,Xm,Qm;Ym=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Fl=function(){};Xm=function(t,e,n,r){var i=t.memoizedProps;if(i!==r){t=e.stateNode,zn(Mt.current);var s=null;switch(n){case"input":i=sl(t,i),r=sl(t,r),s=[];break;case"select":i=se({},i,{value:void 0}),r=se({},r,{value:void 0}),s=[];break;case"textarea":i=ll(t,i),r=ll(t,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=ao)}ul(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ni.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ni.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&J("scroll",t),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(e.updateQueue=u)&&(e.flags|=4)}};Qm=function(t,e,n,r){n!==r&&(e.flags|=4)};function Qr(t,e){if(!ne)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function je(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function $x(t,e,n){var r=e.pendingProps;switch(kc(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return je(e),null;case 1:return Ye(e.type)&&lo(),je(e),null;case 3:return r=e.stateNode,Tr(),te(Ve),te(Ee),$c(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(ps(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,mt!==null&&(Vl(mt),mt=null))),Fl(t,e),je(e),null;case 5:Dc(e);var i=zn(Li.current);if(n=e.type,t!==null&&e.stateNode!=null)Xm(t,e,n,r,i),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(C(166));return je(e),null}if(t=zn(Mt.current),ps(e)){r=e.stateNode,n=e.type;var s=e.memoizedProps;switch(r[kt]=e,r[Di]=s,t=(e.mode&1)!==0,n){case"dialog":J("cancel",r),J("close",r);break;case"iframe":case"object":case"embed":J("load",r);break;case"video":case"audio":for(i=0;i<oi.length;i++)J(oi[i],r);break;case"source":J("error",r);break;case"img":case"image":case"link":J("error",r),J("load",r);break;case"details":J("toggle",r);break;case"input":Su(r,s),J("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},J("invalid",r);break;case"textarea":Nu(r,s),J("invalid",r)}ul(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&ms(r.textContent,a,t),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&ms(r.textContent,a,t),i=["children",""+a]):Ni.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&J("scroll",r)}switch(n){case"input":os(r),_u(r,s,!0);break;case"textarea":os(r),ju(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ao)}r=i,e.updateQueue=r,r!==null&&(e.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Sf(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=o.createElement(n,{is:r.is}):(t=o.createElement(n),n==="select"&&(o=t,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):t=o.createElementNS(t,n),t[kt]=e,t[Di]=r,Ym(t,e,!1,!1),e.stateNode=t;e:{switch(o=dl(n,r),n){case"dialog":J("cancel",t),J("close",t),i=r;break;case"iframe":case"object":case"embed":J("load",t),i=r;break;case"video":case"audio":for(i=0;i<oi.length;i++)J(oi[i],t);i=r;break;case"source":J("error",t),i=r;break;case"img":case"image":case"link":J("error",t),J("load",t),i=r;break;case"details":J("toggle",t),i=r;break;case"input":Su(t,r),i=sl(t,r),J("invalid",t);break;case"option":i=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},i=se({},r,{value:void 0}),J("invalid",t);break;case"textarea":Nu(t,r),i=ll(t,r),J("invalid",t);break;default:i=r}ul(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?jf(t,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&_f(t,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ji(t,l):typeof l=="number"&&ji(t,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ni.hasOwnProperty(s)?l!=null&&s==="onScroll"&&J("scroll",t):l!=null&&dc(t,s,l,o))}switch(n){case"input":os(t),_u(t,r,!1);break;case"textarea":os(t),ju(t);break;case"option":r.value!=null&&t.setAttribute("value",""+bn(r.value));break;case"select":t.multiple=!!r.multiple,s=r.value,s!=null?gr(t,!!r.multiple,s,!1):r.defaultValue!=null&&gr(t,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(t.onclick=ao)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return je(e),null;case 6:if(t&&e.stateNode!=null)Qm(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(C(166));if(n=zn(Li.current),zn(Mt.current),ps(e)){if(r=e.stateNode,n=e.memoizedProps,r[kt]=e,(s=r.nodeValue!==n)&&(t=qe,t!==null))switch(t.tag){case 3:ms(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&ms(r.nodeValue,n,(t.mode&1)!==0)}s&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[kt]=e,e.stateNode=r}return je(e),null;case 13:if(te(re),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(ne&&Ge!==null&&e.mode&1&&!(e.flags&128))hm(),Pr(),e.flags|=98560,s=!1;else if(s=ps(e),r!==null&&r.dehydrated!==null){if(t===null){if(!s)throw Error(C(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(C(317));s[kt]=e}else Pr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;je(e),s=!1}else mt!==null&&(Vl(mt),mt=null),s=!0;if(!s)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||re.current&1?ge===0&&(ge=3):Xc())),e.updateQueue!==null&&(e.flags|=4),je(e),null);case 4:return Tr(),Fl(t,e),t===null&&Ri(e.stateNode.containerInfo),je(e),null;case 10:return Ec(e.type._context),je(e),null;case 17:return Ye(e.type)&&lo(),je(e),null;case 19:if(te(re),s=e.memoizedState,s===null)return je(e),null;if(r=(e.flags&128)!==0,o=s.rendering,o===null)if(r)Qr(s,!1);else{if(ge!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(o=go(t),o!==null){for(e.flags|=128,Qr(s,!1),r=o.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)s=n,t=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=t,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,t=o.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return G(re,re.current&1|2),e.child}t=t.sibling}s.tail!==null&&le()>Or&&(e.flags|=128,r=!0,Qr(s,!1),e.lanes=4194304)}else{if(!r)if(t=go(o),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),Qr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!ne)return je(e),null}else 2*le()-s.renderingStartTime>Or&&n!==1073741824&&(e.flags|=128,r=!0,Qr(s,!1),e.lanes=4194304);s.isBackwards?(o.sibling=e.child,e.child=o):(n=s.last,n!==null?n.sibling=o:e.child=o,s.last=o)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=le(),e.sibling=null,n=re.current,G(re,r?n&1|2:n&1),e):(je(e),null);case 22:case 23:return Yc(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?Ke&1073741824&&(je(e),e.subtreeFlags&6&&(e.flags|=8192)):je(e),null;case 24:return null;case 25:return null}throw Error(C(156,e.tag))}function Lx(t,e){switch(kc(e),e.tag){case 1:return Ye(e.type)&&lo(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Tr(),te(Ve),te(Ee),$c(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return Dc(e),null;case 13:if(te(re),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(C(340));Pr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return te(re),null;case 4:return Tr(),null;case 10:return Ec(e.type._context),null;case 22:case 23:return Yc(),null;case 24:return null;default:return null}}var vs=!1,Ce=!1,Fx=typeof WeakSet=="function"?WeakSet:Set,O=null;function mr(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(t,e,r)}else n.current=null}function zl(t,e,n){try{n()}catch(r){oe(t,e,r)}}var md=!1;function zx(t,e){if(wl=io,t=Jf(),Nc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,d=0,h=t,f=null;t:for(;;){for(var m;h!==n||i!==0&&h.nodeType!==3||(a=o+i),h!==s||r!==0&&h.nodeType!==3||(l=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(m=h.firstChild)!==null;)f=h,h=m;for(;;){if(h===t)break t;if(f===n&&++u===i&&(a=o),f===s&&++d===r&&(l=o),(m=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Sl={focusedElem:t,selectionRange:n},io=!1,O=e;O!==null;)if(e=O,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,O=t;else for(;O!==null;){e=O;try{var v=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,b=v.memoizedState,g=e.stateNode,p=g.getSnapshotBeforeUpdate(e.elementType===e.type?x:ht(e.type,x),b);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var y=e.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){oe(e,e.return,w)}if(t=e.sibling,t!==null){t.return=e.return,O=t;break}O=e.return}return v=md,md=!1,v}function vi(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&t)===t){var s=i.destroy;i.destroy=void 0,s!==void 0&&zl(e,n,s)}i=i.next}while(i!==r)}}function Ko(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function Al(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function Km(t){var e=t.alternate;e!==null&&(t.alternate=null,Km(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[kt],delete e[Di],delete e[jl],delete e[bx],delete e[wx])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Gm(t){return t.tag===5||t.tag===3||t.tag===4}function pd(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Gm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Il(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=ao));else if(r!==4&&(t=t.child,t!==null))for(Il(t,e,n),t=t.sibling;t!==null;)Il(t,e,n),t=t.sibling}function Ul(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(Ul(t,e,n),t=t.sibling;t!==null;)Ul(t,e,n),t=t.sibling}var be=null,ft=!1;function Qt(t,e,n){for(n=n.child;n!==null;)qm(t,e,n),n=n.sibling}function qm(t,e,n){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(Uo,n)}catch{}switch(n.tag){case 5:Ce||mr(n,e);case 6:var r=be,i=ft;be=null,Qt(t,e,n),be=r,ft=i,be!==null&&(ft?(t=be,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):be.removeChild(n.stateNode));break;case 18:be!==null&&(ft?(t=be,n=n.stateNode,t.nodeType===8?Ca(t.parentNode,n):t.nodeType===1&&Ca(t,n),Pi(t)):Ca(be,n.stateNode));break;case 4:r=be,i=ft,be=n.stateNode.containerInfo,ft=!0,Qt(t,e,n),be=r,ft=i;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&zl(n,e,o),i=i.next}while(i!==r)}Qt(t,e,n);break;case 1:if(!Ce&&(mr(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){oe(n,e,a)}Qt(t,e,n);break;case 21:Qt(t,e,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,Qt(t,e,n),Ce=r):Qt(t,e,n);break;default:Qt(t,e,n)}}function gd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Fx),e.forEach(function(r){var i=Xx.bind(null,t,r);n.has(r)||(n.add(r),r.then(i,i))})}}function dt(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=t,o=e,a=o;e:for(;a!==null;){switch(a.tag){case 5:be=a.stateNode,ft=!1;break e;case 3:be=a.stateNode.containerInfo,ft=!0;break e;case 4:be=a.stateNode.containerInfo,ft=!0;break e}a=a.return}if(be===null)throw Error(C(160));qm(s,o,i),be=null,ft=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){oe(i,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)Zm(e,t),e=e.sibling}function Zm(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(dt(e,t),wt(t),r&4){try{vi(3,t,t.return),Ko(3,t)}catch(x){oe(t,t.return,x)}try{vi(5,t,t.return)}catch(x){oe(t,t.return,x)}}break;case 1:dt(e,t),wt(t),r&512&&n!==null&&mr(n,n.return);break;case 5:if(dt(e,t),wt(t),r&512&&n!==null&&mr(n,n.return),t.flags&32){var i=t.stateNode;try{ji(i,"")}catch(x){oe(t,t.return,x)}}if(r&4&&(i=t.stateNode,i!=null)){var s=t.memoizedProps,o=n!==null?n.memoizedProps:s,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&bf(i,s),dl(a,o);var u=dl(a,s);for(o=0;o<l.length;o+=2){var d=l[o],h=l[o+1];d==="style"?jf(i,h):d==="dangerouslySetInnerHTML"?_f(i,h):d==="children"?ji(i,h):dc(i,d,h,u)}switch(a){case"input":ol(i,s);break;case"textarea":wf(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var m=s.value;m!=null?gr(i,!!s.multiple,m,!1):f!==!!s.multiple&&(s.defaultValue!=null?gr(i,!!s.multiple,s.defaultValue,!0):gr(i,!!s.multiple,s.multiple?[]:"",!1))}i[Di]=s}catch(x){oe(t,t.return,x)}}break;case 6:if(dt(e,t),wt(t),r&4){if(t.stateNode===null)throw Error(C(162));i=t.stateNode,s=t.memoizedProps;try{i.nodeValue=s}catch(x){oe(t,t.return,x)}}break;case 3:if(dt(e,t),wt(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Pi(e.containerInfo)}catch(x){oe(t,t.return,x)}break;case 4:dt(e,t),wt(t);break;case 13:dt(e,t),wt(t),i=t.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Bc=le())),r&4&&gd(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(Ce=(u=Ce)||d,dt(e,t),Ce=u):dt(e,t),wt(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(O=t,d=t.child;d!==null;){for(h=O=d;O!==null;){switch(f=O,m=f.child,f.tag){case 0:case 11:case 14:case 15:vi(4,f,f.return);break;case 1:mr(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{e=r,v.props=e.memoizedProps,v.state=e.memoizedState,v.componentWillUnmount()}catch(x){oe(r,n,x)}}break;case 5:mr(f,f.return);break;case 22:if(f.memoizedState!==null){vd(h);continue}}m!==null?(m.return=f,O=m):vd(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Nf("display",o))}catch(x){oe(t,t.return,x)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(x){oe(t,t.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:dt(e,t),wt(t),r&4&&gd(t);break;case 21:break;default:dt(e,t),wt(t)}}function wt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(Gm(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(ji(i,""),r.flags&=-33);var s=pd(t);Ul(t,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=pd(t);Il(t,a,o);break;default:throw Error(C(161))}}catch(l){oe(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ax(t,e,n){O=t,Jm(t)}function Jm(t,e,n){for(var r=(t.mode&1)!==0;O!==null;){var i=O,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||vs;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Ce;a=vs;var u=Ce;if(vs=o,(Ce=l)&&!u)for(O=i;O!==null;)o=O,l=o.child,o.tag===22&&o.memoizedState!==null?yd(i):l!==null?(l.return=o,O=l):yd(i);for(;s!==null;)O=s,Jm(s),s=s.sibling;O=i,vs=a,Ce=u}xd(t)}else i.subtreeFlags&8772&&s!==null?(s.return=i,O=s):xd(t)}}function xd(t){for(;O!==null;){var e=O;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Ce||Ko(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var i=e.elementType===e.type?n.memoizedProps:ht(e.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=e.updateQueue;s!==null&&td(e,s,r);break;case 3:var o=e.updateQueue;if(o!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}td(e,o,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Pi(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}Ce||e.flags&512&&Al(e)}catch(f){oe(e,e.return,f)}}if(e===t){O=null;break}if(n=e.sibling,n!==null){n.return=e.return,O=n;break}O=e.return}}function vd(t){for(;O!==null;){var e=O;if(e===t){O=null;break}var n=e.sibling;if(n!==null){n.return=e.return,O=n;break}O=e.return}}function yd(t){for(;O!==null;){var e=O;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Ko(4,e)}catch(l){oe(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var i=e.return;try{r.componentDidMount()}catch(l){oe(e,i,l)}}var s=e.return;try{Al(e)}catch(l){oe(e,s,l)}break;case 5:var o=e.return;try{Al(e)}catch(l){oe(e,o,l)}}}catch(l){oe(e,e.return,l)}if(e===t){O=null;break}var a=e.sibling;if(a!==null){a.return=e.return,O=a;break}O=e.return}}var Ix=Math.ceil,yo=Xt.ReactCurrentDispatcher,Hc=Xt.ReactCurrentOwner,ot=Xt.ReactCurrentBatchConfig,U=0,ye=null,ue=null,we=0,Ke=0,pr=Nn(0),ge=0,Ii=null,Yn=0,Go=0,Wc=0,yi=null,Ue=null,Bc=0,Or=1/0,Dt=null,bo=!1,Hl=null,fn=null,ys=!1,nn=null,wo=0,bi=0,Wl=null,Ws=-1,Bs=0;function Le(){return U&6?le():Ws!==-1?Ws:Ws=le()}function mn(t){return t.mode&1?U&2&&we!==0?we&-we:_x.transition!==null?(Bs===0&&(Bs=Ff()),Bs):(t=X,t!==0||(t=window.event,t=t===void 0?16:Bf(t.type)),t):1}function vt(t,e,n,r){if(50<bi)throw bi=0,Wl=null,Error(C(185));Ki(t,n,r),(!(U&2)||t!==ye)&&(t===ye&&(!(U&2)&&(Go|=n),ge===4&&en(t,we)),Xe(t,r),n===1&&U===0&&!(e.mode&1)&&(Or=le()+500,Yo&&jn()))}function Xe(t,e){var n=t.callbackNode;_0(t,e);var r=ro(t,t===ye?we:0);if(r===0)n!==null&&Mu(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&Mu(n),e===1)t.tag===0?Sx(bd.bind(null,t)):cm(bd.bind(null,t)),vx(function(){!(U&6)&&jn()}),n=null;else{switch(zf(r)){case 1:n=gc;break;case 4:n=$f;break;case 16:n=no;break;case 536870912:n=Lf;break;default:n=no}n=ap(n,ep.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function ep(t,e){if(Ws=-1,Bs=0,U&6)throw Error(C(327));var n=t.callbackNode;if(wr()&&t.callbackNode!==n)return null;var r=ro(t,t===ye?we:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=So(t,r);else{e=r;var i=U;U|=2;var s=np();(ye!==t||we!==e)&&(Dt=null,Or=le()+500,In(t,e));do try{Wx();break}catch(a){tp(t,a)}while(!0);Pc(),yo.current=s,U=i,ue!==null?e=0:(ye=null,we=0,e=ge)}if(e!==0){if(e===2&&(i=gl(t),i!==0&&(r=i,e=Bl(t,i))),e===1)throw n=Ii,In(t,0),en(t,r),Xe(t,le()),n;if(e===6)en(t,r);else{if(i=t.current.alternate,!(r&30)&&!Ux(i)&&(e=So(t,r),e===2&&(s=gl(t),s!==0&&(r=s,e=Bl(t,s))),e===1))throw n=Ii,In(t,0),en(t,r),Xe(t,le()),n;switch(t.finishedWork=i,t.finishedLanes=r,e){case 0:case 1:throw Error(C(345));case 2:Rn(t,Ue,Dt);break;case 3:if(en(t,r),(r&130023424)===r&&(e=Bc+500-le(),10<e)){if(ro(t,0)!==0)break;if(i=t.suspendedLanes,(i&r)!==r){Le(),t.pingedLanes|=t.suspendedLanes&i;break}t.timeoutHandle=Nl(Rn.bind(null,t,Ue,Dt),e);break}Rn(t,Ue,Dt);break;case 4:if(en(t,r),(r&4194240)===r)break;for(e=t.eventTimes,i=-1;0<r;){var o=31-xt(r);s=1<<o,o=e[o],o>i&&(i=o),r&=~s}if(r=i,r=le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ix(r/1960))-r,10<r){t.timeoutHandle=Nl(Rn.bind(null,t,Ue,Dt),r);break}Rn(t,Ue,Dt);break;case 5:Rn(t,Ue,Dt);break;default:throw Error(C(329))}}}return Xe(t,le()),t.callbackNode===n?ep.bind(null,t):null}function Bl(t,e){var n=yi;return t.current.memoizedState.isDehydrated&&(In(t,e).flags|=256),t=So(t,e),t!==2&&(e=Ue,Ue=n,e!==null&&Vl(e)),t}function Vl(t){Ue===null?Ue=t:Ue.push.apply(Ue,t)}function Ux(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!yt(s(),i))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function en(t,e){for(e&=~Wc,e&=~Go,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-xt(e),r=1<<n;t[n]=-1,e&=~r}}function bd(t){if(U&6)throw Error(C(327));wr();var e=ro(t,0);if(!(e&1))return Xe(t,le()),null;var n=So(t,e);if(t.tag!==0&&n===2){var r=gl(t);r!==0&&(e=r,n=Bl(t,r))}if(n===1)throw n=Ii,In(t,0),en(t,e),Xe(t,le()),n;if(n===6)throw Error(C(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,Rn(t,Ue,Dt),Xe(t,le()),null}function Vc(t,e){var n=U;U|=1;try{return t(e)}finally{U=n,U===0&&(Or=le()+500,Yo&&jn())}}function Xn(t){nn!==null&&nn.tag===0&&!(U&6)&&wr();var e=U;U|=1;var n=ot.transition,r=X;try{if(ot.transition=null,X=1,t)return t()}finally{X=r,ot.transition=n,U=e,!(U&6)&&jn()}}function Yc(){Ke=pr.current,te(pr)}function In(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,xx(n)),ue!==null)for(n=ue.return;n!==null;){var r=n;switch(kc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&lo();break;case 3:Tr(),te(Ve),te(Ee),$c();break;case 5:Dc(r);break;case 4:Tr();break;case 13:te(re);break;case 19:te(re);break;case 10:Ec(r.type._context);break;case 22:case 23:Yc()}n=n.return}if(ye=t,ue=t=pn(t.current,null),we=Ke=e,ge=0,Ii=null,Wc=Go=Yn=0,Ue=yi=null,Fn!==null){for(e=0;e<Fn.length;e++)if(n=Fn[e],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Fn=null}return t}function tp(t,e){do{var n=ue;try{if(Pc(),Is.current=vo,xo){for(var r=ie.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}xo=!1}if(Vn=0,ve=me=ie=null,xi=!1,Fi=0,Hc.current=null,n===null||n.return===null){ge=1,Ii=e,ue=null;break}e:{var s=t,o=n.return,a=n,l=e;if(e=we,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=ad(o);if(m!==null){m.flags&=-257,ld(m,o,a,s,e),m.mode&1&&od(s,u,e),e=m,l=u;var v=e.updateQueue;if(v===null){var x=new Set;x.add(l),e.updateQueue=x}else v.add(l);break e}else{if(!(e&1)){od(s,u,e),Xc();break e}l=Error(C(426))}}else if(ne&&a.mode&1){var b=ad(o);if(b!==null){!(b.flags&65536)&&(b.flags|=256),ld(b,o,a,s,e),Cc(Rr(l,a));break e}}s=l=Rr(l,a),ge!==4&&(ge=2),yi===null?yi=[s]:yi.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,e&=-e,s.lanes|=e;var g=zm(s,l,e);ed(s,g);break e;case 1:a=l;var p=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(fn===null||!fn.has(y)))){s.flags|=65536,e&=-e,s.lanes|=e;var w=Am(s,a,e);ed(s,w);break e}}s=s.return}while(s!==null)}ip(n)}catch(_){e=_,ue===n&&n!==null&&(ue=n=n.return);continue}break}while(!0)}function np(){var t=yo.current;return yo.current=vo,t===null?vo:t}function Xc(){(ge===0||ge===3||ge===2)&&(ge=4),ye===null||!(Yn&268435455)&&!(Go&268435455)||en(ye,we)}function So(t,e){var n=U;U|=2;var r=np();(ye!==t||we!==e)&&(Dt=null,In(t,e));do try{Hx();break}catch(i){tp(t,i)}while(!0);if(Pc(),U=n,yo.current=r,ue!==null)throw Error(C(261));return ye=null,we=0,ge}function Hx(){for(;ue!==null;)rp(ue)}function Wx(){for(;ue!==null&&!m0();)rp(ue)}function rp(t){var e=op(t.alternate,t,Ke);t.memoizedProps=t.pendingProps,e===null?ip(t):ue=e,Hc.current=null}function ip(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Lx(n,e),n!==null){n.flags&=32767,ue=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{ge=6,ue=null;return}}else if(n=$x(n,e,Ke),n!==null){ue=n;return}if(e=e.sibling,e!==null){ue=e;return}ue=e=t}while(e!==null);ge===0&&(ge=5)}function Rn(t,e,n){var r=X,i=ot.transition;try{ot.transition=null,X=1,Bx(t,e,n,r)}finally{ot.transition=i,X=r}return null}function Bx(t,e,n,r){do wr();while(nn!==null);if(U&6)throw Error(C(327));n=t.finishedWork;var i=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(C(177));t.callbackNode=null,t.callbackPriority=0;var s=n.lanes|n.childLanes;if(N0(t,s),t===ye&&(ue=ye=null,we=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ys||(ys=!0,ap(no,function(){return wr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=ot.transition,ot.transition=null;var o=X;X=1;var a=U;U|=4,Hc.current=null,zx(t,n),Zm(n,t),ux(Sl),io=!!wl,Sl=wl=null,t.current=n,Ax(n),p0(),U=a,X=o,ot.transition=s}else t.current=n;if(ys&&(ys=!1,nn=t,wo=i),s=t.pendingLanes,s===0&&(fn=null),v0(n.stateNode),Xe(t,le()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)i=e[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(bo)throw bo=!1,t=Hl,Hl=null,t;return wo&1&&t.tag!==0&&wr(),s=t.pendingLanes,s&1?t===Wl?bi++:(bi=0,Wl=t):bi=0,jn(),null}function wr(){if(nn!==null){var t=zf(wo),e=ot.transition,n=X;try{if(ot.transition=null,X=16>t?16:t,nn===null)var r=!1;else{if(t=nn,nn=null,wo=0,U&6)throw Error(C(331));var i=U;for(U|=4,O=t.current;O!==null;){var s=O,o=s.child;if(O.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(O=u;O!==null;){var d=O;switch(d.tag){case 0:case 11:case 15:vi(8,d,s)}var h=d.child;if(h!==null)h.return=d,O=h;else for(;O!==null;){d=O;var f=d.sibling,m=d.return;if(Km(d),d===u){O=null;break}if(f!==null){f.return=m,O=f;break}O=m}}}var v=s.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var b=x.sibling;x.sibling=null,x=b}while(x!==null)}}O=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,O=o;else e:for(;O!==null;){if(s=O,s.flags&2048)switch(s.tag){case 0:case 11:case 15:vi(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,O=g;break e}O=s.return}}var p=t.current;for(O=p;O!==null;){o=O;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,O=y;else e:for(o=p;O!==null;){if(a=O,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ko(9,a)}}catch(_){oe(a,a.return,_)}if(a===o){O=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,O=w;break e}O=a.return}}if(U=i,jn(),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(Uo,t)}catch{}r=!0}return r}finally{X=n,ot.transition=e}}return!1}function wd(t,e,n){e=Rr(n,e),e=zm(t,e,1),t=hn(t,e,1),e=Le(),t!==null&&(Ki(t,1,e),Xe(t,e))}function oe(t,e,n){if(t.tag===3)wd(t,t,n);else for(;e!==null;){if(e.tag===3){wd(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(fn===null||!fn.has(r))){t=Rr(n,t),t=Am(e,t,1),e=hn(e,t,1),t=Le(),e!==null&&(Ki(e,1,t),Xe(e,t));break}}e=e.return}}function Vx(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=Le(),t.pingedLanes|=t.suspendedLanes&n,ye===t&&(we&n)===n&&(ge===4||ge===3&&(we&130023424)===we&&500>le()-Bc?In(t,0):Wc|=n),Xe(t,e)}function sp(t,e){e===0&&(t.mode&1?(e=cs,cs<<=1,!(cs&130023424)&&(cs=4194304)):e=1);var n=Le();t=Bt(t,e),t!==null&&(Ki(t,e,n),Xe(t,n))}function Yx(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),sp(t,n)}function Xx(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(e),sp(t,n)}var op;op=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Ve.current)We=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return We=!1,Dx(t,e,n);We=!!(t.flags&131072)}else We=!1,ne&&e.flags&1048576&&um(e,ho,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;Hs(t,e),t=e.pendingProps;var i=Mr(e,Ee.current);br(e,n),i=Fc(null,e,r,t,i,n);var s=zc();return e.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ye(r)?(s=!0,co(e)):s=!1,e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Rc(e),i.updater=Qo,e.stateNode=i,i._reactInternals=e,Tl(e,r,t,n),e=Dl(null,e,r,!0,s,n)):(e.tag=0,ne&&s&&jc(e),Oe(null,e,i,n),e=e.child),e;case 16:r=e.elementType;e:{switch(Hs(t,e),t=e.pendingProps,i=r._init,r=i(r._payload),e.type=r,i=e.tag=Kx(r),t=ht(r,t),i){case 0:e=Ol(null,e,r,t,n);break e;case 1:e=dd(null,e,r,t,n);break e;case 11:e=cd(null,e,r,t,n);break e;case 14:e=ud(null,e,r,ht(r.type,t),n);break e}throw Error(C(306,r,""))}return e;case 0:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:ht(r,i),Ol(t,e,r,i,n);case 1:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:ht(r,i),dd(t,e,r,i,n);case 3:e:{if(Wm(e),t===null)throw Error(C(387));r=e.pendingProps,s=e.memoizedState,i=s.element,gm(t,e),po(e,r,null,n);var o=e.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){i=Rr(Error(C(423)),e),e=hd(t,e,r,n,i);break e}else if(r!==i){i=Rr(Error(C(424)),e),e=hd(t,e,r,n,i);break e}else for(Ge=dn(e.stateNode.containerInfo.firstChild),qe=e,ne=!0,mt=null,n=mm(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Pr(),r===i){e=Vt(t,e,n);break e}Oe(t,e,r,n)}e=e.child}return e;case 5:return xm(e),t===null&&Ml(e),r=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,o=i.children,_l(r,i)?o=null:s!==null&&_l(r,s)&&(e.flags|=32),Hm(t,e),Oe(t,e,o,n),e.child;case 6:return t===null&&Ml(e),null;case 13:return Bm(t,e,n);case 4:return Oc(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Er(e,null,r,n):Oe(t,e,r,n),e.child;case 11:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:ht(r,i),cd(t,e,r,i,n);case 7:return Oe(t,e,e.pendingProps,n),e.child;case 8:return Oe(t,e,e.pendingProps.children,n),e.child;case 12:return Oe(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,i=e.pendingProps,s=e.memoizedProps,o=i.value,G(fo,r._currentValue),r._currentValue=o,s!==null)if(yt(s.value,o)){if(s.children===i.children&&!Ve.current){e=Vt(t,e,n);break e}}else for(s=e.child,s!==null&&(s.return=e);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Ut(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Pl(s.return,n,e),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===e.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(C(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Pl(o,n,e),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===e){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Oe(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,r=e.pendingProps.children,br(e,n),i=at(i),r=r(i),e.flags|=1,Oe(t,e,r,n),e.child;case 14:return r=e.type,i=ht(r,e.pendingProps),i=ht(r.type,i),ud(t,e,r,i,n);case 15:return Im(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:ht(r,i),Hs(t,e),e.tag=1,Ye(r)?(t=!0,co(e)):t=!1,br(e,n),Fm(e,r,i),Tl(e,r,i,n),Dl(null,e,r,!0,t,n);case 19:return Vm(t,e,n);case 22:return Um(t,e,n)}throw Error(C(156,e.tag))};function ap(t,e){return Df(t,e)}function Qx(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function it(t,e,n,r){return new Qx(t,e,n,r)}function Qc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Kx(t){if(typeof t=="function")return Qc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===fc)return 11;if(t===mc)return 14}return 2}function pn(t,e){var n=t.alternate;return n===null?(n=it(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Vs(t,e,n,r,i,s){var o=2;if(r=t,typeof t=="function")Qc(t)&&(o=1);else if(typeof t=="string")o=5;else e:switch(t){case sr:return Un(n.children,i,s,e);case hc:o=8,i|=8;break;case tl:return t=it(12,n,e,i|2),t.elementType=tl,t.lanes=s,t;case nl:return t=it(13,n,e,i),t.elementType=nl,t.lanes=s,t;case rl:return t=it(19,n,e,i),t.elementType=rl,t.lanes=s,t;case xf:return qo(n,i,s,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case pf:o=10;break e;case gf:o=9;break e;case fc:o=11;break e;case mc:o=14;break e;case qt:o=16,r=null;break e}throw Error(C(130,t==null?t:typeof t,""))}return e=it(o,n,e,i),e.elementType=t,e.type=r,e.lanes=s,e}function Un(t,e,n,r){return t=it(7,t,r,e),t.lanes=n,t}function qo(t,e,n,r){return t=it(22,t,r,e),t.elementType=xf,t.lanes=n,t.stateNode={isHidden:!1},t}function $a(t,e,n){return t=it(6,t,null,e),t.lanes=n,t}function La(t,e,n){return e=it(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function Gx(t,e,n,r,i){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ga(0),this.expirationTimes=ga(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ga(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Kc(t,e,n,r,i,s,o,a,l){return t=new Gx(t,e,n,a,l),e===1?(e=1,s===!0&&(e|=8)):e=0,s=it(3,null,null,e),t.current=s,s.stateNode=t,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Rc(s),t}function qx(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ir,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function lp(t){if(!t)return wn;t=t._reactInternals;e:{if(Zn(t)!==t||t.tag!==1)throw Error(C(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Ye(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(C(171))}if(t.tag===1){var n=t.type;if(Ye(n))return lm(t,n,e)}return e}function cp(t,e,n,r,i,s,o,a,l){return t=Kc(n,r,!0,t,i,s,o,a,l),t.context=lp(null),n=t.current,r=Le(),i=mn(n),s=Ut(r,i),s.callback=e??null,hn(n,s,i),t.current.lanes=i,Ki(t,i,r),Xe(t,r),t}function Zo(t,e,n,r){var i=e.current,s=Le(),o=mn(i);return n=lp(n),e.context===null?e.context=n:e.pendingContext=n,e=Ut(s,o),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=hn(i,e,o),t!==null&&(vt(t,i,o,s),As(t,i,o)),o}function _o(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Sd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Gc(t,e){Sd(t,e),(t=t.alternate)&&Sd(t,e)}function Zx(){return null}var up=typeof reportError=="function"?reportError:function(t){console.error(t)};function qc(t){this._internalRoot=t}Jo.prototype.render=qc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(C(409));Zo(t,e,null,null)};Jo.prototype.unmount=qc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Xn(function(){Zo(null,t,null,null)}),e[Wt]=null}};function Jo(t){this._internalRoot=t}Jo.prototype.unstable_scheduleHydration=function(t){if(t){var e=Uf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Jt.length&&e!==0&&e<Jt[n].priority;n++);Jt.splice(n,0,t),n===0&&Wf(t)}};function Zc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function ea(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function _d(){}function Jx(t,e,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=_o(o);s.call(u)}}var o=cp(e,r,t,0,null,!1,!1,"",_d);return t._reactRootContainer=o,t[Wt]=o.current,Ri(t.nodeType===8?t.parentNode:t),Xn(),o}for(;i=t.lastChild;)t.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=_o(l);a.call(u)}}var l=Kc(t,0,!1,null,null,!1,!1,"",_d);return t._reactRootContainer=l,t[Wt]=l.current,Ri(t.nodeType===8?t.parentNode:t),Xn(function(){Zo(e,l,n,r)}),l}function ta(t,e,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=_o(o);a.call(l)}}Zo(e,o,t,i)}else o=Jx(n,e,t,i,r);return _o(o)}Af=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=si(e.pendingLanes);n!==0&&(xc(e,n|1),Xe(e,le()),!(U&6)&&(Or=le()+500,jn()))}break;case 13:Xn(function(){var r=Bt(t,1);if(r!==null){var i=Le();vt(r,t,1,i)}}),Gc(t,1)}};vc=function(t){if(t.tag===13){var e=Bt(t,134217728);if(e!==null){var n=Le();vt(e,t,134217728,n)}Gc(t,134217728)}};If=function(t){if(t.tag===13){var e=mn(t),n=Bt(t,e);if(n!==null){var r=Le();vt(n,t,e,r)}Gc(t,e)}};Uf=function(){return X};Hf=function(t,e){var n=X;try{return X=t,e()}finally{X=n}};fl=function(t,e,n){switch(e){case"input":if(ol(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var i=Vo(r);if(!i)throw Error(C(90));yf(r),ol(r,i)}}}break;case"textarea":wf(t,n);break;case"select":e=n.value,e!=null&&gr(t,!!n.multiple,e,!1)}};Mf=Vc;Pf=Xn;var ev={usingClientEntryPoint:!1,Events:[qi,cr,Vo,kf,Cf,Vc]},Kr={findFiberByHostInstance:Ln,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},tv={bundleType:Kr.bundleType,version:Kr.version,rendererPackageName:Kr.rendererPackageName,rendererConfig:Kr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Rf(t),t===null?null:t.stateNode},findFiberByHostInstance:Kr.findFiberByHostInstance||Zx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bs.isDisabled&&bs.supportsFiber)try{Uo=bs.inject(tv),Ct=bs}catch{}}Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ev;Je.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Zc(e))throw Error(C(200));return qx(t,e,null,n)};Je.createRoot=function(t,e){if(!Zc(t))throw Error(C(299));var n=!1,r="",i=up;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(i=e.onRecoverableError)),e=Kc(t,1,!1,null,null,n,!1,r,i),t[Wt]=e.current,Ri(t.nodeType===8?t.parentNode:t),new qc(e)};Je.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(C(188)):(t=Object.keys(t).join(","),Error(C(268,t)));return t=Rf(e),t=t===null?null:t.stateNode,t};Je.flushSync=function(t){return Xn(t)};Je.hydrate=function(t,e,n){if(!ea(e))throw Error(C(200));return ta(null,t,e,!0,n)};Je.hydrateRoot=function(t,e,n){if(!Zc(t))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=up;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),e=cp(e,null,t,1,n??null,i,!1,s,o),t[Wt]=e.current,Ri(t),r)for(t=0;t<r.length;t++)n=r[t],i=n._getVersion,i=i(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,i]:e.mutableSourceEagerHydrationData.push(n,i);return new Jo(e)};Je.render=function(t,e,n){if(!ea(e))throw Error(C(200));return ta(null,t,e,!1,n)};Je.unmountComponentAtNode=function(t){if(!ea(t))throw Error(C(40));return t._reactRootContainer?(Xn(function(){ta(null,null,t,!1,function(){t._reactRootContainer=null,t[Wt]=null})}),!0):!1};Je.unstable_batchedUpdates=Vc;Je.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!ea(n))throw Error(C(200));if(t==null||t._reactInternals===void 0)throw Error(C(38));return ta(t,e,n,!1,r)};Je.version="18.3.1-next-f1338f8080-20240426";function dp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(dp)}catch(t){console.error(t)}}dp(),df.exports=Je;var nv=df.exports,Nd=nv;Ja.createRoot=Nd.createRoot,Ja.hydrateRoot=Nd.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ui(){return Ui=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ui.apply(this,arguments)}var rn;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(rn||(rn={}));const jd="popstate";function rv(t){t===void 0&&(t={});function e(r,i){let{pathname:s,search:o,hash:a}=r.location;return Yl("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:No(i)}return sv(e,n,null,t)}function ae(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function hp(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function iv(){return Math.random().toString(36).substr(2,8)}function kd(t,e){return{usr:t.state,key:t.key,idx:e}}function Yl(t,e,n,r){return n===void 0&&(n=null),Ui({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Ar(e):e,{state:n,key:e&&e.key||r||iv()})}function No(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ar(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function sv(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a=rn.Pop,l=null,u=d();u==null&&(u=0,o.replaceState(Ui({},o.state,{idx:u}),""));function d(){return(o.state||{idx:null}).idx}function h(){a=rn.Pop;let b=d(),g=b==null?null:b-u;u=b,l&&l({action:a,location:x.location,delta:g})}function f(b,g){a=rn.Push;let p=Yl(x.location,b,g);u=d()+1;let y=kd(p,u),w=x.createHref(p);try{o.pushState(y,"",w)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;i.location.assign(w)}s&&l&&l({action:a,location:x.location,delta:1})}function m(b,g){a=rn.Replace;let p=Yl(x.location,b,g);u=d();let y=kd(p,u),w=x.createHref(p);o.replaceState(y,"",w),s&&l&&l({action:a,location:x.location,delta:0})}function v(b){let g=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof b=="string"?b:No(b);return p=p.replace(/ $/,"%20"),ae(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let x={get action(){return a},get location(){return t(i,o)},listen(b){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(jd,h),l=b,()=>{i.removeEventListener(jd,h),l=null}},createHref(b){return e(i,b)},createURL:v,encodeLocation(b){let g=v(b);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:m,go(b){return o.go(b)}};return x}var Cd;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(Cd||(Cd={}));function ov(t,e,n){return n===void 0&&(n="/"),av(t,e,n)}function av(t,e,n,r){let i=typeof e=="string"?Ar(e):e,s=Dr(i.pathname||"/",n);if(s==null)return null;let o=fp(t);lv(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=yv(s);a=xv(o[l],u)}return a}function fp(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(ae(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=gn([r,l.relativePath]),d=n.concat(l);s.children&&s.children.length>0&&(ae(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),fp(s.children,e,d,u)),!(s.path==null&&!s.index)&&e.push({path:u,score:pv(u,s.index),routesMeta:d})};return t.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of mp(s.path))i(s,o,l)}),e}function mp(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=mp(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>t.startsWith("/")&&l===""?"/":l)}function lv(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:gv(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const cv=/^:[\w-]+$/,uv=3,dv=2,hv=1,fv=10,mv=-2,Md=t=>t==="*";function pv(t,e){let n=t.split("/"),r=n.length;return n.some(Md)&&(r+=mv),e&&(r+=dv),n.filter(i=>!Md(i)).reduce((i,s)=>i+(cv.test(s)?uv:s===""?hv:fv),r)}function gv(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function xv(t,e,n){let{routesMeta:r}=t,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,d=s==="/"?e:e.slice(s.length)||"/",h=Xl({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!h)return null;Object.assign(i,h.params),o.push({params:i,pathname:gn([s,h.pathname]),pathnameBase:_v(gn([s,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(s=gn([s,h.pathnameBase]))}return o}function Xl(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=vv(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:m}=d;if(f==="*"){let x=a[h]||"";o=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const v=a[h];return m&&!v?u[f]=void 0:u[f]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:t}}function vv(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),hp(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function yv(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return hp(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Dr(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function bv(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?Ar(t):t;return{pathname:n?n.startsWith("/")?n:wv(n,e):e,search:Nv(r),hash:jv(i)}}function wv(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Fa(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Sv(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function pp(t,e){let n=Sv(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function gp(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=Ar(t):(i=Ui({},t),ae(!i.pathname||!i.pathname.includes("?"),Fa("?","pathname","search",i)),ae(!i.pathname||!i.pathname.includes("#"),Fa("#","pathname","hash",i)),ae(!i.search||!i.search.includes("#"),Fa("#","search","hash",i)));let s=t===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let h=e.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}a=h>=0?e[h]:"/"}let l=bv(i,a),u=o&&o!=="/"&&o.endsWith("/"),d=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const gn=t=>t.join("/").replace(/\/\/+/g,"/"),_v=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Nv=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,jv=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function kv(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const xp=["post","put","patch","delete"];new Set(xp);const Cv=["get",...xp];new Set(Cv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Hi(){return Hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Hi.apply(this,arguments)}const na=j.createContext(null),vp=j.createContext(null),kn=j.createContext(null),ra=j.createContext(null),Jn=j.createContext({outlet:null,matches:[],isDataRoute:!1}),yp=j.createContext(null);function Mv(t,e){let{relative:n}=e===void 0?{}:e;Ji()||ae(!1);let{basename:r,navigator:i}=j.useContext(kn),{hash:s,pathname:o,search:a}=sa(t,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:gn([r,o])),i.createHref({pathname:l,search:a,hash:s})}function Ji(){return j.useContext(ra)!=null}function es(){return Ji()||ae(!1),j.useContext(ra).location}function bp(t){j.useContext(kn).static||j.useLayoutEffect(t)}function ia(){let{isDataRoute:t}=j.useContext(Jn);return t?Uv():Pv()}function Pv(){Ji()||ae(!1);let t=j.useContext(na),{basename:e,future:n,navigator:r}=j.useContext(kn),{matches:i}=j.useContext(Jn),{pathname:s}=es(),o=JSON.stringify(pp(i,n.v7_relativeSplatPath)),a=j.useRef(!1);return bp(()=>{a.current=!0}),j.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let h=gp(u,JSON.parse(o),s,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:gn([e,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[e,r,o,s,t])}function sa(t,e){let{relative:n}=e===void 0?{}:e,{future:r}=j.useContext(kn),{matches:i}=j.useContext(Jn),{pathname:s}=es(),o=JSON.stringify(pp(i,r.v7_relativeSplatPath));return j.useMemo(()=>gp(t,JSON.parse(o),s,n==="path"),[t,o,s,n])}function Ev(t,e){return Tv(t,e)}function Tv(t,e,n,r){Ji()||ae(!1);let{navigator:i}=j.useContext(kn),{matches:s}=j.useContext(Jn),o=s[s.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=es(),d;if(e){var h;let b=typeof e=="string"?Ar(e):e;l==="/"||(h=b.pathname)!=null&&h.startsWith(l)||ae(!1),d=b}else d=u;let f=d.pathname||"/",m=f;if(l!=="/"){let b=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(b.length).join("/")}let v=ov(t,{pathname:m}),x=Lv(v&&v.map(b=>Object.assign({},b,{params:Object.assign({},a,b.params),pathname:gn([l,i.encodeLocation?i.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?l:gn([l,i.encodeLocation?i.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),s,n,r);return e&&x?j.createElement(ra.Provider,{value:{location:Hi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:rn.Pop}},x):x}function Rv(){let t=Iv(),e=kv(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return j.createElement(j.Fragment,null,j.createElement("h2",null,"Unexpected Application Error!"),j.createElement("h3",{style:{fontStyle:"italic"}},e),n?j.createElement("pre",{style:i},n):null,null)}const Ov=j.createElement(Rv,null);class Dv extends j.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?j.createElement(Jn.Provider,{value:this.props.routeContext},j.createElement(yp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function $v(t){let{routeContext:e,match:n,children:r}=t,i=j.useContext(na);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),j.createElement(Jn.Provider,{value:e},r)}function Lv(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var s;if(!n)return null;if(n.errors)t=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let o=t,a=(i=n)==null?void 0:i.errors;if(a!=null){let d=o.findIndex(h=>h.route.id&&(a==null?void 0:a[h.route.id])!==void 0);d>=0||ae(!1),o=o.slice(0,Math.min(o.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<o.length;d++){let h=o[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:m}=n,v=h.route.loader&&f[h.route.id]===void 0&&(!m||m[h.route.id]===void 0);if(h.route.lazy||v){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((d,h,f)=>{let m,v=!1,x=null,b=null;n&&(m=a&&h.route.id?a[h.route.id]:void 0,x=h.route.errorElement||Ov,l&&(u<0&&f===0?(Hv("route-fallback"),v=!0,b=null):u===f&&(v=!0,b=h.route.hydrateFallbackElement||null)));let g=e.concat(o.slice(0,f+1)),p=()=>{let y;return m?y=x:v?y=b:h.route.Component?y=j.createElement(h.route.Component,null):h.route.element?y=h.route.element:y=d,j.createElement($v,{match:h,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:y})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?j.createElement(Dv,{location:n.location,revalidation:n.revalidation,component:x,error:m,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var wp=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(wp||{}),Sp=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(Sp||{});function Fv(t){let e=j.useContext(na);return e||ae(!1),e}function zv(t){let e=j.useContext(vp);return e||ae(!1),e}function Av(t){let e=j.useContext(Jn);return e||ae(!1),e}function _p(t){let e=Av(),n=e.matches[e.matches.length-1];return n.route.id||ae(!1),n.route.id}function Iv(){var t;let e=j.useContext(yp),n=zv(),r=_p();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function Uv(){let{router:t}=Fv(wp.UseNavigateStable),e=_p(Sp.UseNavigateStable),n=j.useRef(!1);return bp(()=>{n.current=!0}),j.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Hi({fromRouteId:e},s)))},[t,e])}const Pd={};function Hv(t,e,n){Pd[t]||(Pd[t]=!0)}function Wv(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function On(t){ae(!1)}function Bv(t){let{basename:e="/",children:n=null,location:r,navigationType:i=rn.Pop,navigator:s,static:o=!1,future:a}=t;Ji()&&ae(!1);let l=e.replace(/^\/*/,"/"),u=j.useMemo(()=>({basename:l,navigator:s,static:o,future:Hi({v7_relativeSplatPath:!1},a)}),[l,a,s,o]);typeof r=="string"&&(r=Ar(r));let{pathname:d="/",search:h="",hash:f="",state:m=null,key:v="default"}=r,x=j.useMemo(()=>{let b=Dr(d,l);return b==null?null:{location:{pathname:b,search:h,hash:f,state:m,key:v},navigationType:i}},[l,d,h,f,m,v,i]);return x==null?null:j.createElement(kn.Provider,{value:u},j.createElement(ra.Provider,{children:n,value:x}))}function Vv(t){let{children:e,location:n}=t;return Ev(Ql(e),n)}new Promise(()=>{});function Ql(t,e){e===void 0&&(e=[]);let n=[];return j.Children.forEach(t,(r,i)=>{if(!j.isValidElement(r))return;let s=[...e,i];if(r.type===j.Fragment){n.push.apply(n,Ql(r.props.children,s));return}r.type!==On&&ae(!1),!r.props.index||!r.props.children||ae(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Ql(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function jo(){return jo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},jo.apply(this,arguments)}function Np(t,e){if(t==null)return{};var n={},r=Object.keys(t),i,s;for(s=0;s<r.length;s++)i=r[s],!(e.indexOf(i)>=0)&&(n[i]=t[i]);return n}function Yv(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function Xv(t,e){return t.button===0&&(!e||e==="_self")&&!Yv(t)}const Qv=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Kv=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],Gv="6";try{window.__reactRouterVersion=Gv}catch{}const qv=j.createContext({isTransitioning:!1}),Zv="startTransition",Ed=Vg[Zv];function Jv(t){let{basename:e,children:n,future:r,window:i}=t,s=j.useRef();s.current==null&&(s.current=rv({window:i,v5Compat:!0}));let o=s.current,[a,l]=j.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},d=j.useCallback(h=>{u&&Ed?Ed(()=>l(h)):l(h)},[l,u]);return j.useLayoutEffect(()=>o.listen(d),[o,d]),j.useEffect(()=>Wv(r),[r]),j.createElement(Bv,{basename:e,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const ey=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ty=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ny=j.forwardRef(function(e,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:d,viewTransition:h}=e,f=Np(e,Qv),{basename:m}=j.useContext(kn),v,x=!1;if(typeof u=="string"&&ty.test(u)&&(v=u,ey))try{let y=new URL(window.location.href),w=u.startsWith("//")?new URL(y.protocol+u):new URL(u),_=Dr(w.pathname,m);w.origin===y.origin&&_!=null?u=_+w.search+w.hash:x=!0}catch{}let b=Mv(u,{relative:i}),g=iy(u,{replace:o,state:a,target:l,preventScrollReset:d,relative:i,viewTransition:h});function p(y){r&&r(y),y.defaultPrevented||g(y)}return j.createElement("a",jo({},f,{href:v||b,onClick:x||s?r:p,ref:n,target:l}))}),Td=j.forwardRef(function(e,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:o=!1,style:a,to:l,viewTransition:u,children:d}=e,h=Np(e,Kv),f=sa(l,{relative:h.relative}),m=es(),v=j.useContext(vp),{navigator:x,basename:b}=j.useContext(kn),g=v!=null&&sy(f)&&u===!0,p=x.encodeLocation?x.encodeLocation(f).pathname:f.pathname,y=m.pathname,w=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;i||(y=y.toLowerCase(),w=w?w.toLowerCase():null,p=p.toLowerCase()),w&&b&&(w=Dr(w,b)||w);const _=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let S=y===p||!o&&y.startsWith(p)&&y.charAt(_)==="/",N=w!=null&&(w===p||!o&&w.startsWith(p)&&w.charAt(p.length)==="/"),k={isActive:S,isPending:N,isTransitioning:g},T=S?r:void 0,M;typeof s=="function"?M=s(k):M=[s,S?"active":null,N?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let E=typeof a=="function"?a(k):a;return j.createElement(ny,jo({},h,{"aria-current":T,className:M,ref:n,style:E,to:l,viewTransition:u}),typeof d=="function"?d(k):d)});var Kl;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Kl||(Kl={}));var Rd;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Rd||(Rd={}));function ry(t){let e=j.useContext(na);return e||ae(!1),e}function iy(t,e){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:a}=e===void 0?{}:e,l=ia(),u=es(),d=sa(t,{relative:o});return j.useCallback(h=>{if(Xv(h,n)){h.preventDefault();let f=r!==void 0?r:No(u)===No(d);l(t,{replace:f,state:i,preventScrollReset:s,relative:o,viewTransition:a})}},[u,l,d,r,i,n,t,s,o,a])}function sy(t,e){e===void 0&&(e={});let n=j.useContext(qv);n==null&&ae(!1);let{basename:r}=ry(Kl.useViewTransitionState),i=sa(t,{relative:e.relative});if(!n.isTransitioning)return!1;let s=Dr(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=Dr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Xl(i.pathname,o)!=null||Xl(i.pathname,s)!=null}function ko(t){"@babel/helpers - typeof";return ko=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ko(t)}function Qn(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function Ae(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function Pt(t){Ae(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||ko(t)==="object"&&e==="[object Date]"?new Date(t.getTime()):typeof t=="number"||e==="[object Number]"?new Date(t):((typeof t=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function oy(t,e){Ae(2,arguments);var n=Pt(t).getTime(),r=Qn(e);return new Date(n+r)}var ay={};function oa(){return ay}function ly(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}function cy(t){return Ae(1,arguments),t instanceof Date||ko(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function uy(t){if(Ae(1,arguments),!cy(t)&&typeof t!="number")return!1;var e=Pt(t);return!isNaN(Number(e))}function dy(t,e){Ae(2,arguments);var n=Qn(e);return oy(t,-n)}var hy=864e5;function fy(t){Ae(1,arguments);var e=Pt(t),n=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var r=e.getTime(),i=n-r;return Math.floor(i/hy)+1}function Co(t){Ae(1,arguments);var e=1,n=Pt(t),r=n.getUTCDay(),i=(r<e?7:0)+r-e;return n.setUTCDate(n.getUTCDate()-i),n.setUTCHours(0,0,0,0),n}function jp(t){Ae(1,arguments);var e=Pt(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var i=Co(r),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var o=Co(s);return e.getTime()>=i.getTime()?n+1:e.getTime()>=o.getTime()?n:n-1}function my(t){Ae(1,arguments);var e=jp(t),n=new Date(0);n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0);var r=Co(n);return r}var py=6048e5;function gy(t){Ae(1,arguments);var e=Pt(t),n=Co(e).getTime()-my(e).getTime();return Math.round(n/py)+1}function Mo(t,e){var n,r,i,s,o,a,l,u;Ae(1,arguments);var d=oa(),h=Qn((n=(r=(i=(s=e==null?void 0:e.weekStartsOn)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&n!==void 0?n:0);if(!(h>=0&&h<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var f=Pt(t),m=f.getUTCDay(),v=(m<h?7:0)+m-h;return f.setUTCDate(f.getUTCDate()-v),f.setUTCHours(0,0,0,0),f}function kp(t,e){var n,r,i,s,o,a,l,u;Ae(1,arguments);var d=Pt(t),h=d.getUTCFullYear(),f=oa(),m=Qn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&i!==void 0?i:f.firstWeekContainsDate)!==null&&r!==void 0?r:(l=f.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(m>=1&&m<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var v=new Date(0);v.setUTCFullYear(h+1,0,m),v.setUTCHours(0,0,0,0);var x=Mo(v,e),b=new Date(0);b.setUTCFullYear(h,0,m),b.setUTCHours(0,0,0,0);var g=Mo(b,e);return d.getTime()>=x.getTime()?h+1:d.getTime()>=g.getTime()?h:h-1}function xy(t,e){var n,r,i,s,o,a,l,u;Ae(1,arguments);var d=oa(),h=Qn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1),f=kp(t,e),m=new Date(0);m.setUTCFullYear(f,0,h),m.setUTCHours(0,0,0,0);var v=Mo(m,e);return v}var vy=6048e5;function yy(t,e){Ae(1,arguments);var n=Pt(t),r=Mo(n,e).getTime()-xy(n,e).getTime();return Math.round(r/vy)+1}function V(t,e){for(var n=t<0?"-":"",r=Math.abs(t).toString();r.length<e;)r="0"+r;return n+r}var Kt={y:function(e,n){var r=e.getUTCFullYear(),i=r>0?r:1-r;return V(n==="yy"?i%100:i,n.length)},M:function(e,n){var r=e.getUTCMonth();return n==="M"?String(r+1):V(r+1,2)},d:function(e,n){return V(e.getUTCDate(),n.length)},a:function(e,n){var r=e.getUTCHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(e,n){return V(e.getUTCHours()%12||12,n.length)},H:function(e,n){return V(e.getUTCHours(),n.length)},m:function(e,n){return V(e.getUTCMinutes(),n.length)},s:function(e,n){return V(e.getUTCSeconds(),n.length)},S:function(e,n){var r=n.length,i=e.getUTCMilliseconds(),s=Math.floor(i*Math.pow(10,r-3));return V(s,n.length)}},tr={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},by={G:function(e,n,r){var i=e.getUTCFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(e,n,r){if(n==="yo"){var i=e.getUTCFullYear(),s=i>0?i:1-i;return r.ordinalNumber(s,{unit:"year"})}return Kt.y(e,n)},Y:function(e,n,r,i){var s=kp(e,i),o=s>0?s:1-s;if(n==="YY"){var a=o%100;return V(a,2)}return n==="Yo"?r.ordinalNumber(o,{unit:"year"}):V(o,n.length)},R:function(e,n){var r=jp(e);return V(r,n.length)},u:function(e,n){var r=e.getUTCFullYear();return V(r,n.length)},Q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"Q":return String(i);case"QQ":return V(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"q":return String(i);case"qq":return V(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(e,n,r){var i=e.getUTCMonth();switch(n){case"M":case"MM":return Kt.M(e,n);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(e,n,r){var i=e.getUTCMonth();switch(n){case"L":return String(i+1);case"LL":return V(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(e,n,r,i){var s=yy(e,i);return n==="wo"?r.ordinalNumber(s,{unit:"week"}):V(s,n.length)},I:function(e,n,r){var i=gy(e);return n==="Io"?r.ordinalNumber(i,{unit:"week"}):V(i,n.length)},d:function(e,n,r){return n==="do"?r.ordinalNumber(e.getUTCDate(),{unit:"date"}):Kt.d(e,n)},D:function(e,n,r){var i=fy(e);return n==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):V(i,n.length)},E:function(e,n,r){var i=e.getUTCDay();switch(n){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(e,n,r,i){var s=e.getUTCDay(),o=(s-i.weekStartsOn+8)%7||7;switch(n){case"e":return String(o);case"ee":return V(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(s,{width:"short",context:"formatting"});case"eeee":default:return r.day(s,{width:"wide",context:"formatting"})}},c:function(e,n,r,i){var s=e.getUTCDay(),o=(s-i.weekStartsOn+8)%7||7;switch(n){case"c":return String(o);case"cc":return V(o,n.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(s,{width:"narrow",context:"standalone"});case"cccccc":return r.day(s,{width:"short",context:"standalone"});case"cccc":default:return r.day(s,{width:"wide",context:"standalone"})}},i:function(e,n,r){var i=e.getUTCDay(),s=i===0?7:i;switch(n){case"i":return String(s);case"ii":return V(s,n.length);case"io":return r.ordinalNumber(s,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(e,n,r){var i=e.getUTCHours(),s=i/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,n,r){var i=e.getUTCHours(),s;switch(i===12?s=tr.noon:i===0?s=tr.midnight:s=i/12>=1?"pm":"am",n){case"b":case"bb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,n,r){var i=e.getUTCHours(),s;switch(i>=17?s=tr.evening:i>=12?s=tr.afternoon:i>=4?s=tr.morning:s=tr.night,n){case"B":case"BB":case"BBB":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,n,r){if(n==="ho"){var i=e.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return Kt.h(e,n)},H:function(e,n,r){return n==="Ho"?r.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Kt.H(e,n)},K:function(e,n,r){var i=e.getUTCHours()%12;return n==="Ko"?r.ordinalNumber(i,{unit:"hour"}):V(i,n.length)},k:function(e,n,r){var i=e.getUTCHours();return i===0&&(i=24),n==="ko"?r.ordinalNumber(i,{unit:"hour"}):V(i,n.length)},m:function(e,n,r){return n==="mo"?r.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):Kt.m(e,n)},s:function(e,n,r){return n==="so"?r.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):Kt.s(e,n)},S:function(e,n){return Kt.S(e,n)},X:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();if(o===0)return"Z";switch(n){case"X":return Dd(o);case"XXXX":case"XX":return Dn(o);case"XXXXX":case"XXX":default:return Dn(o,":")}},x:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"x":return Dd(o);case"xxxx":case"xx":return Dn(o);case"xxxxx":case"xxx":default:return Dn(o,":")}},O:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Od(o,":");case"OOOO":default:return"GMT"+Dn(o,":")}},z:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Od(o,":");case"zzzz":default:return"GMT"+Dn(o,":")}},t:function(e,n,r,i){var s=i._originalDate||e,o=Math.floor(s.getTime()/1e3);return V(o,n.length)},T:function(e,n,r,i){var s=i._originalDate||e,o=s.getTime();return V(o,n.length)}};function Od(t,e){var n=t>0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=r%60;if(s===0)return n+String(i);var o=e;return n+String(i)+o+V(s,2)}function Dd(t,e){if(t%60===0){var n=t>0?"-":"+";return n+V(Math.abs(t)/60,2)}return Dn(t,e)}function Dn(t,e){var n=e||"",r=t>0?"-":"+",i=Math.abs(t),s=V(Math.floor(i/60),2),o=V(i%60,2);return r+s+n+o}var $d=function(e,n){switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},Cp=function(e,n){switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},wy=function(e,n){var r=e.match(/(P+)(p+)?/)||[],i=r[1],s=r[2];if(!s)return $d(e,n);var o;switch(i){case"P":o=n.dateTime({width:"short"});break;case"PP":o=n.dateTime({width:"medium"});break;case"PPP":o=n.dateTime({width:"long"});break;case"PPPP":default:o=n.dateTime({width:"full"});break}return o.replace("{{date}}",$d(i,n)).replace("{{time}}",Cp(s,n))},Sy={p:Cp,P:wy},_y=["D","DD"],Ny=["YY","YYYY"];function jy(t){return _y.indexOf(t)!==-1}function ky(t){return Ny.indexOf(t)!==-1}function Ld(t,e,n){if(t==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Cy={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},My=function(e,n,r){var i,s=Cy[e];return typeof s=="string"?i=s:n===1?i=s.one:i=s.other.replace("{{count}}",n.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};function za(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth,r=t.formats[n]||t.formats[t.defaultWidth];return r}}var Py={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Ey={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Ty={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ry={date:za({formats:Py,defaultWidth:"full"}),time:za({formats:Ey,defaultWidth:"full"}),dateTime:za({formats:Ty,defaultWidth:"full"})},Oy={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Dy=function(e,n,r,i){return Oy[e]};function Gr(t){return function(e,n){var r=n!=null&&n.context?String(n.context):"standalone",i;if(r==="formatting"&&t.formattingValues){var s=t.defaultFormattingWidth||t.defaultWidth,o=n!=null&&n.width?String(n.width):s;i=t.formattingValues[o]||t.formattingValues[s]}else{var a=t.defaultWidth,l=n!=null&&n.width?String(n.width):t.defaultWidth;i=t.values[l]||t.values[a]}var u=t.argumentCallback?t.argumentCallback(e):e;return i[u]}}var $y={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Ly={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Fy={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},zy={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Ay={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Iy={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Uy=function(e,n){var r=Number(e),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},Hy={ordinalNumber:Uy,era:Gr({values:$y,defaultWidth:"wide"}),quarter:Gr({values:Ly,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:Gr({values:Fy,defaultWidth:"wide"}),day:Gr({values:zy,defaultWidth:"wide"}),dayPeriod:Gr({values:Ay,defaultWidth:"wide",formattingValues:Iy,defaultFormattingWidth:"wide"})};function qr(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(i);if(!s)return null;var o=s[0],a=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(a)?By(a,function(h){return h.test(o)}):Wy(a,function(h){return h.test(o)}),u;u=t.valueCallback?t.valueCallback(l):l,u=n.valueCallback?n.valueCallback(u):u;var d=e.slice(o.length);return{value:u,rest:d}}}function Wy(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}function By(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}function Vy(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=e.match(t.matchPattern);if(!r)return null;var i=r[0],s=e.match(t.parsePattern);if(!s)return null;var o=t.valueCallback?t.valueCallback(s[0]):s[0];o=n.valueCallback?n.valueCallback(o):o;var a=e.slice(i.length);return{value:o,rest:a}}}var Yy=/^(\d+)(th|st|nd|rd)?/i,Xy=/\d+/i,Qy={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Ky={any:[/^b/i,/^(a|c)/i]},Gy={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},qy={any:[/1/i,/2/i,/3/i,/4/i]},Zy={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Jy={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},e1={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},t1={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},n1={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},r1={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},i1={ordinalNumber:Vy({matchPattern:Yy,parsePattern:Xy,valueCallback:function(e){return parseInt(e,10)}}),era:qr({matchPatterns:Qy,defaultMatchWidth:"wide",parsePatterns:Ky,defaultParseWidth:"any"}),quarter:qr({matchPatterns:Gy,defaultMatchWidth:"wide",parsePatterns:qy,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:qr({matchPatterns:Zy,defaultMatchWidth:"wide",parsePatterns:Jy,defaultParseWidth:"any"}),day:qr({matchPatterns:e1,defaultMatchWidth:"wide",parsePatterns:t1,defaultParseWidth:"any"}),dayPeriod:qr({matchPatterns:n1,defaultMatchWidth:"any",parsePatterns:r1,defaultParseWidth:"any"})},s1={code:"en-US",formatDistance:My,formatLong:Ry,formatRelative:Dy,localize:Hy,match:i1,options:{weekStartsOn:0,firstWeekContainsDate:1}},o1=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,a1=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,l1=/^'([^]*?)'?$/,c1=/''/g,u1=/[a-zA-Z]/;function Fd(t,e,n){var r,i,s,o,a,l,u,d,h,f,m,v,x,b;Ae(2,arguments);var g=String(e),p=oa(),y=(r=(i=void 0)!==null&&i!==void 0?i:p.locale)!==null&&r!==void 0?r:s1,w=Qn((s=(o=(a=(l=void 0)!==null&&l!==void 0?l:void 0)!==null&&a!==void 0?a:p.firstWeekContainsDate)!==null&&o!==void 0?o:(u=p.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&s!==void 0?s:1);if(!(w>=1&&w<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var _=Qn((h=(f=(m=(v=void 0)!==null&&v!==void 0?v:void 0)!==null&&m!==void 0?m:p.weekStartsOn)!==null&&f!==void 0?f:(x=p.locale)===null||x===void 0||(b=x.options)===null||b===void 0?void 0:b.weekStartsOn)!==null&&h!==void 0?h:0);if(!(_>=0&&_<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!y.localize)throw new RangeError("locale must contain localize property");if(!y.formatLong)throw new RangeError("locale must contain formatLong property");var S=Pt(t);if(!uy(S))throw new RangeError("Invalid time value");var N=ly(S),k=dy(S,N),T={firstWeekContainsDate:w,weekStartsOn:_,locale:y,_originalDate:S},M=g.match(a1).map(function(E){var $=E[0];if($==="p"||$==="P"){var I=Sy[$];return I(E,y.formatLong)}return E}).join("").match(o1).map(function(E){if(E==="''")return"'";var $=E[0];if($==="'")return d1(E);var I=by[$];if(I)return ky(E)&&Ld(E,e,String(t)),jy(E)&&Ld(E,e,String(t)),I(k,E,y.localize,T);if($.match(u1))throw new RangeError("Format string contains an unescaped latin alphabet character `"+$+"`");return E}).join("");return M}function d1(t){var e=t.match(l1);return e?e[1].replace(c1,"'"):t}const Mp=j.createContext(),Gl={version:"1.1.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",customCurrencyName:"",customCurrencySymbol:"",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null,notificationsEnabled:!1,notificationTime:"18:00",lastNotificationDate:null};function h1(t,e){switch(e.type){case"INITIALIZE_APP":return{...t,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,unitCost:e.payload.unitCost,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:t.unitCost,timestamp:Fd(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,purchases:[n,...t.purchases],currentUnits:t.currentUnits+e.payload.units};case"UPDATE_USAGE":const r={id:Date.now(),date:new Date().toISOString(),previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usage:t.currentUnits-e.payload.currentUnits,timestamp:Fd(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[r,...t.usageHistory]};case"UPDATE_SETTINGS":return{...t,...e.payload};case"FACTORY_RESET":return{...Gl};case"DASHBOARD_RESET":return{...t,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...t,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...t,...e.payload};default:return t}}function f1({children:t}){const[e,n]=j.useReducer(h1,Gl);j.useEffect(()=>{const S="prepaid-meter-app-v1.1",N=localStorage.getItem(S);if(localStorage.removeItem("prepaid-meter-app"),N)try{const k=JSON.parse(N);if(!k.version||k.version!==Gl.version){console.log("Version mismatch detected, clearing old data"),localStorage.removeItem(S);return}n({type:"LOAD_STATE",payload:k})}catch(k){console.error("Error loading saved state:",k),localStorage.removeItem(S)}},[]),j.useEffect(()=>{localStorage.setItem("prepaid-meter-app-v1.1",JSON.stringify(e))},[e]),j.useEffect(()=>{const S=new Date,N=S.getMonth(),k=S.getFullYear();if(e.lastMonthlyReset){const T=new Date(e.lastMonthlyReset),M=T.getMonth(),E=T.getFullYear();(k>E||k===E&&N>M)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:S.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]),j.useEffect(()=>{if(!e.notificationsEnabled||!e.isInitialized)return;const S=()=>{const k=new Date,[T,M]=e.notificationTime.split(":").map(Number),E=new Date;E.setHours(T,M,0,0);const $=k.toDateString(),I=e.lastNotificationDate?new Date(e.lastNotificationDate).toDateString():null;k>=E&&I!==$&&("Notification"in window&&Notification.permission==="default"&&Notification.requestPermission(),"Notification"in window&&Notification.permission==="granted"&&(new Notification("⚡ Prepaid Meter Reminder",{body:"Don't forget to record your electricity usage today!",icon:"/favicon.ico",badge:"/favicon.ico"}),n({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:k.toISOString()}})))};S();const N=setInterval(S,6e4);return()=>clearInterval(N)},[e.notificationsEnabled,e.notificationTime,e.lastNotificationDate,e.isInitialized]);const r=e.previousUnits-e.currentUnits,i=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,s=e.purchases.reduce((S,N)=>S+N.currency,0),o=e.usageHistory.reduce((S,N)=>S+N.usage,0),a=new Date,l=new Date(a.getFullYear(),a.getMonth(),a.getDate()-a.getDay()),u=new Date(a.getFullYear(),a.getMonth(),1),d=e.purchases.filter(S=>new Date(S.date)>=l),h=e.purchases.filter(S=>new Date(S.date)>=u),f=e.usageHistory.filter(S=>new Date(S.date)>=l),m=e.usageHistory.filter(S=>new Date(S.date)>=u),v=d.reduce((S,N)=>S+N.currency,0),x=h.reduce((S,N)=>S+N.currency,0),b=f.reduce((S,N)=>S+N.usage,0),g=m.reduce((S,N)=>S+N.usage,0),_={state:e,dispatch:n,initializeApp:(S,N)=>{n({type:"INITIALIZE_APP",payload:{initialUnits:S,unitCost:N}})},addPurchase:(S,N)=>{n({type:"ADD_PURCHASE",payload:{currency:S,units:N}})},updateUsage:S=>{n({type:"UPDATE_USAGE",payload:{currentUnits:S}})},updateSettings:S=>{n({type:"UPDATE_SETTINGS",payload:S})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:r,isThresholdExceeded:i,totalPurchases:s,totalUnitsUsed:o,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName,getDisplayCurrencySymbol:()=>e.currency==="CUSTOM"?e.customCurrencySymbol||"C":e.currencySymbol||"R",getDisplayCurrencyName:()=>{var N;return e.currency==="CUSTOM"?e.customCurrencyName||"Custom Currency":((N=[{code:"ZAR",name:"South African Rand"},{code:"USD",name:"US Dollar"},{code:"EUR",name:"Euro"},{code:"GBP",name:"British Pound"},{code:"JPY",name:"Japanese Yen"}].find(k=>k.code===e.currency))==null?void 0:N.name)||"Unknown Currency"},weeklyPurchaseTotal:v,monthlyPurchaseTotal:x,weeklyUsageTotal:b,monthlyUsageTotal:g,weeklyPurchases:d,monthlyPurchases:h,weeklyUsage:f,monthlyUsage:m};return c.jsx(Mp.Provider,{value:_,children:t})}function Qe(){const t=j.useContext(Mp);if(!t)throw new Error("useApp must be used within an AppProvider");return t}const Pp=j.createContext(),pt={electric:{name:"Electric Blue",primary:"bg-blue-600",secondary:"bg-blue-100",accent:"bg-yellow-400",background:"bg-gray-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-gray-200",card:"bg-white",gradient:"from-blue-500 to-blue-700"},dark:{name:"Dark Mode",primary:"bg-gray-800",secondary:"bg-gray-700",accent:"bg-blue-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-700 to-gray-900"},green:{name:"Eco Green",primary:"bg-green-600",secondary:"bg-green-100",accent:"bg-lime-400",background:"bg-green-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-green-200",card:"bg-white",gradient:"from-green-500 to-green-700"},purple:{name:"Royal Purple",primary:"bg-purple-600",secondary:"bg-purple-100",accent:"bg-pink-400",background:"bg-purple-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-purple-200",card:"bg-white",gradient:"from-purple-500 to-purple-700"},orange:{name:"Sunset Orange",primary:"bg-orange-600",secondary:"bg-orange-100",accent:"bg-yellow-400",background:"bg-orange-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-orange-200",card:"bg-white",gradient:"from-orange-500 to-red-600"},teal:{name:"Ocean Teal",primary:"bg-teal-600",secondary:"bg-teal-100",accent:"bg-cyan-400",background:"bg-teal-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-teal-200",card:"bg-white",gradient:"from-teal-500 to-blue-600"},red:{name:"Fire Red",primary:"bg-red-600",secondary:"bg-red-100",accent:"bg-orange-400",background:"bg-red-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-red-200",card:"bg-white",gradient:"from-red-500 to-red-700"},indigo:{name:"Deep Indigo",primary:"bg-indigo-600",secondary:"bg-indigo-100",accent:"bg-purple-400",background:"bg-indigo-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-indigo-200",card:"bg-white",gradient:"from-indigo-500 to-purple-600"},pink:{name:"Rose Pink",primary:"bg-pink-600",secondary:"bg-pink-100",accent:"bg-rose-400",background:"bg-pink-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-pink-200",card:"bg-white",gradient:"from-pink-500 to-rose-600"},slate:{name:"Modern Slate",primary:"bg-slate-600",secondary:"bg-slate-100",accent:"bg-blue-400",background:"bg-slate-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-slate-200",card:"bg-white",gradient:"from-slate-500 to-slate-700"}},Lt={inter:{name:"Inter",class:"font-inter",import:'@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");',fallback:"Inter, system-ui, -apple-system, sans-serif"},roboto:{name:"Roboto",class:"font-roboto",import:'@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");',fallback:"Roboto, system-ui, sans-serif"},opensans:{name:"Open Sans",class:"font-opensans",import:'@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Open Sans, system-ui, sans-serif"},lato:{name:"Lato",class:"font-lato",import:'@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap");',fallback:"Lato, system-ui, sans-serif"},poppins:{name:"Poppins",class:"font-poppins",import:'@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");',fallback:"Poppins, system-ui, sans-serif"},nunito:{name:"Nunito",class:"font-nunito",import:'@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700&display=swap");',fallback:"Nunito, system-ui, sans-serif"},sourcesans:{name:"Source Sans Pro",class:"font-sourcesans",import:'@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap");',fallback:"Source Sans Pro, system-ui, sans-serif"},ubuntu:{name:"Ubuntu",class:"font-ubuntu",import:'@import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap");',fallback:"Ubuntu, system-ui, sans-serif"},raleway:{name:"Raleway",class:"font-raleway",import:'@import url("https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap");',fallback:"Raleway, system-ui, sans-serif"},montserrat:{name:"Montserrat",class:"font-montserrat",import:'@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap");',fallback:"Montserrat, system-ui, sans-serif"},worksans:{name:"Work Sans",class:"font-worksans",import:'@import url("https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Work Sans, system-ui, sans-serif"},firasans:{name:"Fira Sans",class:"font-firasans",import:'@import url("https://fonts.googleapis.com/css2?family=Fira+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Fira Sans, system-ui, sans-serif"},dmsans:{name:"DM Sans",class:"font-dmsans",import:'@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");',fallback:"DM Sans, system-ui, sans-serif"},lexend:{name:"Lexend",class:"font-lexend",import:'@import url("https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap");',fallback:"Lexend, system-ui, sans-serif"},karla:{name:"Karla",class:"font-karla",import:'@import url("https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700&display=swap");',fallback:"Karla, system-ui, sans-serif"},rubik:{name:"Rubik",class:"font-rubik",import:'@import url("https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&display=swap");',fallback:"Rubik, system-ui, sans-serif"},manrope:{name:"Manrope",class:"font-manrope",import:'@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap");',fallback:"Manrope, system-ui, sans-serif"},plusjakarta:{name:"Plus Jakarta Sans",class:"font-plusjakarta",import:'@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Plus Jakarta Sans, system-ui, sans-serif"},outfit:{name:"Outfit",class:"font-outfit",import:'@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap");',fallback:"Outfit, system-ui, sans-serif"},system:{name:"System Default",class:"font-system",import:"",fallback:"system-ui, -apple-system, BlinkMacSystemFont, sans-serif"}},sn={xs:{name:"Extra Small",class:"text-xs",size:"12px"},sm:{name:"Small",class:"text-sm",size:"14px"},base:{name:"Medium",class:"text-base",size:"16px"},lg:{name:"Large",class:"text-lg",size:"18px"},xl:{name:"Extra Large",class:"text-xl",size:"20px"},"2xl":{name:"XXL",class:"text-2xl",size:"24px"}};function m1({children:t}){const[e,n]=j.useState("electric"),[r,i]=j.useState("base"),[s,o]=j.useState("inter"),[a,l]=j.useState({});j.useEffect(()=>{const d=localStorage.getItem("prepaid-meter-theme"),h=localStorage.getItem("prepaid-meter-font-size"),f=localStorage.getItem("prepaid-meter-font-family"),m=localStorage.getItem("prepaid-meter-custom-colors");if(d&&pt[d]&&n(d),h&&sn[h]&&i(h),f&&Lt[f]&&o(f),m)try{l(JSON.parse(m))}catch(v){console.error("Error loading custom colors:",v)}},[]),j.useEffect(()=>{localStorage.setItem("prepaid-meter-theme",e)},[e]),j.useEffect(()=>{localStorage.setItem("prepaid-meter-font-size",r)},[r]),j.useEffect(()=>{localStorage.setItem("prepaid-meter-font-family",s)},[s]),j.useEffect(()=>{localStorage.setItem("prepaid-meter-custom-colors",JSON.stringify(a))},[a]),j.useEffect(()=>{const d=Lt[s];if(d&&d.import){document.querySelectorAll("link[data-font-import]").forEach(m=>m.remove());const f=document.createElement("link");f.rel="stylesheet",f.href=d.import.replace('@import url("',"").replace('");',""),f.setAttribute("data-font-import","true"),document.head.appendChild(f)}},[s]);const u={currentTheme:e,setCurrentTheme:n,fontSize:r,setFontSize:i,fontFamily:s,setFontFamily:o,customColors:a,setCustomColors:l,theme:pt[e],themes:pt,fontSizes:sn,fontFamilies:Lt,currentFontSize:sn[r],currentFontFamily:Lt[s]};return c.jsx(Pp.Provider,{value:u,children:c.jsx("div",{className:`${pt[e].background} ${pt[e].text} ${Lt[s].class} ${sn[r].class} min-h-screen transition-all duration-300`,style:{fontFamily:Lt[s].fallback},children:t})})}function _e(){const t=j.useContext(Pp);if(!t)throw new Error("useTheme must be used within a ThemeProvider");return t}var Ep={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},zd=gt.createContext&&gt.createContext(Ep),xn=function(){return xn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},xn.apply(this,arguments)},p1=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n};function Tp(t){return t&&t.map(function(e,n){return gt.createElement(e.tag,xn({key:n},e.attr),Tp(e.child))})}function q(t){return function(e){return gt.createElement(g1,xn({attr:xn({},t.attr)},e),Tp(t.child))}}function g1(t){var e=function(n){var r=t.attr,i=t.size,s=t.title,o=p1(t,["attr","size","title"]),a=i||n.size||"1em",l;return n.className&&(l=n.className),t.className&&(l=(l?l+" ":"")+t.className),gt.createElement("svg",xn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,o,{className:l,style:xn(xn({color:t.color||n.color},n.style),t.style),height:a,width:a,xmlns:"http://www.w3.org/2000/svg"}),s&&gt.createElement("title",null,s),t.children)};return zd!==void 0?gt.createElement(zd.Consumer,null,function(n){return e(n)}):e(Ep)}function x1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function v1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Rp(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z",clipRule:"evenodd"}}]})(t)}function Sr(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(t)}function Jc(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(t)}function Aa(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function Ia(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function Ys(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",clipRule:"evenodd"}}]})(t)}function Ad(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(t)}function Xs(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(t)}function Op(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z",clipRule:"evenodd"}}]})(t)}function Me(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(t)}function _r(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function y1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(t)}function b1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z",clipRule:"evenodd"}}]})(t)}function w1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"}}]})(t)}function $e(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(t)}function S1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(t)}function _1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}}]})(t)}function Dp(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(t)}function N1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"}}]})(t)}function j1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function k1(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(t)}function De(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(t)}function $p(t){return q({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function C1(t){return q({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"}}]})(t)}function Po({size:t="md",animated:e=!0,showText:n=!0}){const r={sm:{container:"w-8 h-8",icon:"h-5 w-5",text:"text-sm"},md:{container:"w-12 h-12",icon:"h-7 w-7",text:"text-lg"},lg:{container:"w-16 h-16",icon:"h-10 w-10",text:"text-xl"},xl:{container:"w-20 h-20",icon:"h-12 w-12",text:"text-2xl"}},i=r[t]||r.md;return c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsxs("div",{className:`${i.container} rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 shadow-lg flex items-center justify-center relative overflow-hidden ${e?"animate-pulse":""}`,children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-yellow-400/20 via-orange-400/20 to-red-400/20 animate-pulse"}),c.jsx($e,{className:`${i.icon} text-white relative z-10 ${e?"animate-bounce":""}`}),c.jsx("div",{className:"absolute top-1 right-1 w-1 h-1 bg-yellow-300 rounded-full animate-ping"}),c.jsx("div",{className:"absolute bottom-1 left-1 w-1 h-1 bg-blue-300 rounded-full animate-ping delay-300"})]}),n&&c.jsxs("div",{className:"flex flex-col",children:[c.jsx("h1",{className:`${i.text} font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 bg-clip-text text-transparent leading-tight`,children:"PREPAID USER"}),c.jsx("p",{className:"text-xs font-semibold text-gray-500 tracking-wider uppercase leading-tight",children:"ELECTRICITY"})]})]})}function M1({onMenuClick:t}){const{theme:e}=_e(),{state:n}=Qe();return c.jsxs("header",{className:`${e.card} ${e.border} border-b px-4 py-3 flex items-center justify-between shadow-sm`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("button",{onClick:t,className:`lg:hidden p-2 rounded-md ${e.primary} text-white hover:opacity-80 transition-opacity`,children:c.jsx(S1,{className:"h-6 w-6"})}),c.jsx("div",{className:"flex items-center",children:c.jsx(Po,{size:"md",animated:!0,showText:!0})})]}),c.jsxs("div",{className:`hidden sm:flex items-center space-x-4 ${e.card} rounded-lg px-4 py-2 border ${e.border}`,children:[c.jsxs("div",{className:"text-right",children:[c.jsx("p",{className:`text-sm ${e.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-lg font-bold ${e.text}`,children:n.currentUnits.toFixed(2)})]}),c.jsx("div",{className:`w-3 h-3 rounded-full ${n.currentUnits>n.thresholdLimit?"bg-red-500":"bg-green-500"} pulse-glow`})]})]})}const Id=[{name:"Dashboard",href:"/dashboard",icon:w1},{name:"Purchases",href:"/purchases",icon:N1},{name:"Usage",href:"/usage",icon:Jc},{name:"History",href:"/history",icon:Ys},{name:"Settings",href:"/settings",icon:Xs}];function P1({isOpen:t,onClose:e}){const{theme:n}=_e();return c.jsxs(c.Fragment,{children:[c.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${n.card} ${n.border} border-r`,children:c.jsxs("div",{className:"flex flex-col w-full",children:[c.jsxs("div",{className:`flex items-center justify-center h-16 px-4 ${n.primary} text-white`,children:[c.jsx(Po,{size:"md"}),c.jsx("span",{className:"ml-3 text-lg font-semibold",children:"Prepaid Meter"})]}),c.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2",children:Id.map(r=>c.jsxs(Td,{to:r.href,className:({isActive:i})=>`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${i?`${n.primary} text-white`:`${n.text} hover:${n.secondary}`}`,children:[c.jsx(r.icon,{className:"mr-3 h-5 w-5"}),r.name]},r.name))})]})}),c.jsx("div",{className:`lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${n.card} transform transition-transform duration-300 ease-in-out ${t?"translate-x-0":"-translate-x-full"}`,children:c.jsxs("div",{className:"flex flex-col h-full",children:[c.jsxs("div",{className:`flex items-center justify-between h-16 px-4 ${n.primary} text-white`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Po,{size:"md"}),c.jsx("span",{className:"ml-3 text-lg font-semibold",children:"Prepaid Meter"})]}),c.jsx("button",{onClick:e,className:"p-2 rounded-md hover:bg-white hover:bg-opacity-20 transition-colors",children:c.jsx($p,{className:"h-6 w-6"})})]}),c.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2",children:Id.map(r=>c.jsxs(Td,{to:r.href,onClick:e,className:({isActive:i})=>`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${i?`${n.primary} text-white`:`${n.text} hover:${n.secondary}`}`,children:[c.jsx(r.icon,{className:"mr-3 h-5 w-5"}),r.name]},r.name))})]})})]})}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function ts(t){return t+.5|0}const on=(t,e,n)=>Math.max(Math.min(t,n),e);function ai(t){return on(ts(t*2.55),0,255)}function vn(t){return on(ts(t*255),0,255)}function Ft(t){return on(ts(t/2.55)/100,0,1)}function Ud(t){return on(ts(t*100),0,100)}const tt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ql=[..."0123456789ABCDEF"],E1=t=>ql[t&15],T1=t=>ql[(t&240)>>4]+ql[t&15],ws=t=>(t&240)>>4===(t&15),R1=t=>ws(t.r)&&ws(t.g)&&ws(t.b)&&ws(t.a);function O1(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&tt[t[1]]*17,g:255&tt[t[2]]*17,b:255&tt[t[3]]*17,a:e===5?tt[t[4]]*17:255}:(e===7||e===9)&&(n={r:tt[t[1]]<<4|tt[t[2]],g:tt[t[3]]<<4|tt[t[4]],b:tt[t[5]]<<4|tt[t[6]],a:e===9?tt[t[7]]<<4|tt[t[8]]:255})),n}const D1=(t,e)=>t<255?e(t):"";function $1(t){var e=R1(t)?E1:T1;return t?"#"+e(t.r)+e(t.g)+e(t.b)+D1(t.a,e):void 0}const L1=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Lp(t,e,n){const r=e*Math.min(n,1-n),i=(s,o=(s+t/30)%12)=>n-r*Math.max(Math.min(o-3,9-o,1),-1);return[i(0),i(8),i(4)]}function F1(t,e,n){const r=(i,s=(i+t/60)%6)=>n-n*e*Math.max(Math.min(s,4-s,1),0);return[r(5),r(3),r(1)]}function z1(t,e,n){const r=Lp(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)r[i]*=1-e-n,r[i]+=e;return r}function A1(t,e,n,r,i){return t===i?(e-n)/r+(e<n?6:0):e===i?(n-t)/r+2:(t-e)/r+4}function eu(t){const n=t.r/255,r=t.g/255,i=t.b/255,s=Math.max(n,r,i),o=Math.min(n,r,i),a=(s+o)/2;let l,u,d;return s!==o&&(d=s-o,u=a>.5?d/(2-s-o):d/(s+o),l=A1(n,r,i,d,s),l=l*60+.5),[l|0,u||0,a]}function tu(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(vn)}function nu(t,e,n){return tu(Lp,t,e,n)}function I1(t,e,n){return tu(z1,t,e,n)}function U1(t,e,n){return tu(F1,t,e,n)}function Fp(t){return(t%360+360)%360}function H1(t){const e=L1.exec(t);let n=255,r;if(!e)return;e[5]!==r&&(n=e[6]?ai(+e[5]):vn(+e[5]));const i=Fp(+e[2]),s=+e[3]/100,o=+e[4]/100;return e[1]==="hwb"?r=I1(i,s,o):e[1]==="hsv"?r=U1(i,s,o):r=nu(i,s,o),{r:r[0],g:r[1],b:r[2],a:n}}function W1(t,e){var n=eu(t);n[0]=Fp(n[0]+e),n=nu(n),t.r=n[0],t.g=n[1],t.b=n[2]}function B1(t){if(!t)return;const e=eu(t),n=e[0],r=Ud(e[1]),i=Ud(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${i}%, ${Ft(t.a)})`:`hsl(${n}, ${r}%, ${i}%)`}const Hd={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Wd={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function V1(){const t={},e=Object.keys(Wd),n=Object.keys(Hd);let r,i,s,o,a;for(r=0;r<e.length;r++){for(o=a=e[r],i=0;i<n.length;i++)s=n[i],a=a.replace(s,Hd[s]);s=parseInt(Wd[o],16),t[a]=[s>>16&255,s>>8&255,s&255]}return t}let Ss;function Y1(t){Ss||(Ss=V1(),Ss.transparent=[0,0,0,0]);const e=Ss[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const X1=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Q1(t){const e=X1.exec(t);let n=255,r,i,s;if(e){if(e[7]!==r){const o=+e[7];n=e[8]?ai(o):on(o*255,0,255)}return r=+e[1],i=+e[3],s=+e[5],r=255&(e[2]?ai(r):on(r,0,255)),i=255&(e[4]?ai(i):on(i,0,255)),s=255&(e[6]?ai(s):on(s,0,255)),{r,g:i,b:s,a:n}}}function K1(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Ft(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Ua=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,nr=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function G1(t,e,n){const r=nr(Ft(t.r)),i=nr(Ft(t.g)),s=nr(Ft(t.b));return{r:vn(Ua(r+n*(nr(Ft(e.r))-r))),g:vn(Ua(i+n*(nr(Ft(e.g))-i))),b:vn(Ua(s+n*(nr(Ft(e.b))-s))),a:t.a+n*(e.a-t.a)}}function _s(t,e,n){if(t){let r=eu(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,e===0?360:1)),r=nu(r),t.r=r[0],t.g=r[1],t.b=r[2]}}function zp(t,e){return t&&Object.assign(e||{},t)}function Bd(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=vn(t[3]))):(e=zp(t,{r:0,g:0,b:0,a:1}),e.a=vn(e.a)),e}function q1(t){return t.charAt(0)==="r"?Q1(t):H1(t)}class Wi{constructor(e){if(e instanceof Wi)return e;const n=typeof e;let r;n==="object"?r=Bd(e):n==="string"&&(r=O1(e)||Y1(e)||q1(e)),this._rgb=r,this._valid=!!r}get valid(){return this._valid}get rgb(){var e=zp(this._rgb);return e&&(e.a=Ft(e.a)),e}set rgb(e){this._rgb=Bd(e)}rgbString(){return this._valid?K1(this._rgb):void 0}hexString(){return this._valid?$1(this._rgb):void 0}hslString(){return this._valid?B1(this._rgb):void 0}mix(e,n){if(e){const r=this.rgb,i=e.rgb;let s;const o=n===s?.5:n,a=2*o-1,l=r.a-i.a,u=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;s=1-u,r.r=255&u*r.r+s*i.r+.5,r.g=255&u*r.g+s*i.g+.5,r.b=255&u*r.b+s*i.b+.5,r.a=o*r.a+(1-o)*i.a,this.rgb=r}return this}interpolate(e,n){return e&&(this._rgb=G1(this._rgb,e._rgb,n)),this}clone(){return new Wi(this.rgb)}alpha(e){return this._rgb.a=vn(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=ts(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return _s(this._rgb,2,e),this}darken(e){return _s(this._rgb,2,-e),this}saturate(e){return _s(this._rgb,1,e),this}desaturate(e){return _s(this._rgb,1,-e),this}rotate(e){return W1(this._rgb,e),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Tt(){}const Z1=(()=>{let t=0;return()=>t++})();function Q(t){return t==null}function pe(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function H(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function ct(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function St(t,e){return ct(t)?t:e}function W(t,e){return typeof t>"u"?e:t}const J1=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,Ap=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function ee(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function Y(t,e,n,r){let i,s,o;if(pe(t))for(s=t.length,i=0;i<s;i++)e.call(n,t[i],i);else if(H(t))for(o=Object.keys(t),s=o.length,i=0;i<s;i++)e.call(n,t[o[i]],o[i])}function Eo(t,e){let n,r,i,s;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(i=t[n],s=e[n],i.datasetIndex!==s.datasetIndex||i.index!==s.index)return!1;return!0}function To(t){if(pe(t))return t.map(To);if(H(t)){const e=Object.create(null),n=Object.keys(t),r=n.length;let i=0;for(;i<r;++i)e[n[i]]=To(t[n[i]]);return e}return t}function Ip(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function eb(t,e,n,r){if(!Ip(t))return;const i=e[t],s=n[t];H(i)&&H(s)?Bi(i,s,r):e[t]=To(s)}function Bi(t,e,n){const r=pe(e)?e:[e],i=r.length;if(!H(t))return t;n=n||{};const s=n.merger||eb;let o;for(let a=0;a<i;++a){if(o=r[a],!H(o))continue;const l=Object.keys(o);for(let u=0,d=l.length;u<d;++u)s(l[u],t,o,n)}return t}function wi(t,e){return Bi(t,e,{merger:tb})}function tb(t,e,n){if(!Ip(t))return;const r=e[t],i=n[t];H(r)&&H(i)?wi(r,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=To(i))}const Vd={"":t=>t,x:t=>t.x,y:t=>t.y};function nb(t){const e=t.split("."),n=[];let r="";for(const i of e)r+=i,r.endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}function rb(t){const e=nb(t);return n=>{for(const r of e){if(r==="")break;n=n&&n[r]}return n}}function Kn(t,e){return(Vd[e]||(Vd[e]=rb(e)))(t)}function ru(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Vi=t=>typeof t<"u",Sn=t=>typeof t=="function",Yd=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function ib(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const he=Math.PI,de=2*he,Ro=Number.POSITIVE_INFINITY,sb=he/180,xe=he/2,Cn=he/4,Xd=he*2/3,Up=Math.log10,yn=Math.sign;function Qs(t,e,n){return Math.abs(t-e)<n}function Qd(t){const e=Math.round(t);t=Qs(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(Up(t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function ob(t){const e=[],n=Math.sqrt(t);let r;for(r=1;r<n;r++)t%r===0&&(e.push(r),e.push(t/r));return n===(n|0)&&e.push(n),e.sort((i,s)=>i-s).pop(),e}function ab(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Oo(t){return!ab(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function lb(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function cb(t,e,n){let r,i,s;for(r=0,i=t.length;r<i;r++)s=t[r][n],isNaN(s)||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function It(t){return t*(he/180)}function ub(t){return t*(180/he)}function Kd(t){if(!ct(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Hp(t,e){const n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r);let s=Math.atan2(r,n);return s<-.5*he&&(s+=de),{angle:s,distance:i}}function db(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Mn(t){return(t%de+de)%de}function Do(t,e,n,r){const i=Mn(t),s=Mn(e),o=Mn(n),a=Mn(s-i),l=Mn(o-i),u=Mn(i-s),d=Mn(i-o);return i===s||i===o||r&&s===o||a>l&&u<d}function Be(t,e,n){return Math.max(e,Math.min(n,t))}function hb(t){return Be(t,-32768,32767)}function An(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function iu(t,e,n){n=n||(o=>t[o]<e);let r=t.length-1,i=0,s;for(;r-i>1;)s=i+r>>1,n(s)?i=s:r=s;return{lo:i,hi:r}}const Zl=(t,e,n,r)=>iu(t,n,r?i=>{const s=t[i][e];return s<n||s===n&&t[i+1][e]===n}:i=>t[i][e]<n),fb=(t,e,n)=>iu(t,n,r=>t[r][e]>=n);function mb(t,e,n){let r=0,i=t.length;for(;r<i&&t[r]<e;)r++;for(;i>r&&t[i-1]>n;)i--;return r>0||i<t.length?t.slice(r,i):t}const Wp=["push","pop","shift","splice","unshift"];function pb(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),Wp.forEach(n=>{const r="_onData"+ru(n),i=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...s){const o=i.apply(this,s);return t._chartjs.listeners.forEach(a=>{typeof a[r]=="function"&&a[r](...s)}),o}})})}function Gd(t,e){const n=t._chartjs;if(!n)return;const r=n.listeners,i=r.indexOf(e);i!==-1&&r.splice(i,1),!(r.length>0)&&(Wp.forEach(s=>{delete t[s]}),delete t._chartjs)}function Bp(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Vp=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function Yp(t,e){let n=[],r=!1;return function(...i){n=i,r||(r=!0,Vp.call(window,()=>{r=!1,t.apply(e,n)}))}}function gb(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}const su=t=>t==="start"?"left":t==="end"?"right":"center",ke=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,xb=(t,e,n,r)=>t===(r?"left":"right")?n:t==="center"?(e+n)/2:e,Ns=t=>t===0||t===1,qd=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*de/n)),Zd=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*de/n)+1,Si={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*xe)+1,easeOutSine:t=>Math.sin(t*xe),easeInOutSine:t=>-.5*(Math.cos(he*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>Ns(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Ns(t)?t:qd(t,.075,.3),easeOutElastic:t=>Ns(t)?t:Zd(t,.075,.3),easeInOutElastic(t){return Ns(t)?t:t<.5?.5*qd(t*2,.1125,.45):.5+.5*Zd(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-Si.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?Si.easeInBounce(t*2)*.5:Si.easeOutBounce(t*2-1)*.5+.5};function Xp(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function Jd(t){return Xp(t)?t:new Wi(t)}function Ha(t){return Xp(t)?t:new Wi(t).saturate(.5).darken(.1).hexString()}const vb=["x","y","borderWidth","radius","tension"],yb=["color","borderColor","backgroundColor"];function bb(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:yb},numbers:{type:"number",properties:vb}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function wb(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const eh=new Map;function Sb(t,e){e=e||{};const n=t+JSON.stringify(e);let r=eh.get(n);return r||(r=new Intl.NumberFormat(t,e),eh.set(n,r)),r}function ou(t,e,n){return Sb(e,n).format(t)}const _b={values(t){return pe(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const r=this.chart.options.locale;let i,s=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(i="scientific"),s=Nb(t,n)}const o=Up(Math.abs(s)),a=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ou(t,r,l)}};function Nb(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var Qp={formatters:_b};function jb(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Qp.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Gn=Object.create(null),Jl=Object.create(null);function _i(t,e){if(!e)return t;const n=e.split(".");for(let r=0,i=n.length;r<i;++r){const s=n[r];t=t[s]||(t[s]=Object.create(null))}return t}function Wa(t,e,n){return typeof e=="string"?Bi(_i(t,e),n):Bi(_i(t,""),e)}class kb{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=r=>r.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(r,i)=>Ha(i.backgroundColor),this.hoverBorderColor=(r,i)=>Ha(i.borderColor),this.hoverColor=(r,i)=>Ha(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return Wa(this,e,n)}get(e){return _i(this,e)}describe(e,n){return Wa(Jl,e,n)}override(e,n){return Wa(Gn,e,n)}route(e,n,r,i){const s=_i(this,e),o=_i(this,r),a="_"+n;Object.defineProperties(s,{[a]:{value:s[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],u=o[i];return H(l)?Object.assign({},u,l):W(l,u)},set(l){this[a]=l}}})}apply(e){e.forEach(n=>n(this))}}var ce=new kb({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[bb,wb,jb]);function Cb(t){return!t||Q(t.size)||Q(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function th(t,e,n,r,i){let s=e[i];return s||(s=e[i]=t.measureText(i).width,n.push(i)),s>r&&(r=s),r}function Pn(t,e,n){const r=t.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((e-i)*r)/r+i}function nh(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function rh(t,e,n,r){Kp(t,e,n,r,null)}function Kp(t,e,n,r,i){let s,o,a,l,u,d,h,f;const m=e.pointStyle,v=e.rotation,x=e.radius;let b=(v||0)*sb;if(m&&typeof m=="object"&&(s=m.toString(),s==="[object HTMLImageElement]"||s==="[object HTMLCanvasElement]")){t.save(),t.translate(n,r),t.rotate(b),t.drawImage(m,-m.width/2,-m.height/2,m.width,m.height),t.restore();return}if(!(isNaN(x)||x<=0)){switch(t.beginPath(),m){default:i?t.ellipse(n,r,i/2,x,0,0,de):t.arc(n,r,x,0,de),t.closePath();break;case"triangle":d=i?i/2:x,t.moveTo(n+Math.sin(b)*d,r-Math.cos(b)*x),b+=Xd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*x),b+=Xd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*x),t.closePath();break;case"rectRounded":u=x*.516,l=x-u,o=Math.cos(b+Cn)*l,h=Math.cos(b+Cn)*(i?i/2-u:l),a=Math.sin(b+Cn)*l,f=Math.sin(b+Cn)*(i?i/2-u:l),t.arc(n-h,r-a,u,b-he,b-xe),t.arc(n+f,r-o,u,b-xe,b),t.arc(n+h,r+a,u,b,b+xe),t.arc(n-f,r+o,u,b+xe,b+he),t.closePath();break;case"rect":if(!v){l=Math.SQRT1_2*x,d=i?i/2:l,t.rect(n-d,r-l,2*d,2*l);break}b+=Cn;case"rectRot":h=Math.cos(b)*(i?i/2:x),o=Math.cos(b)*x,a=Math.sin(b)*x,f=Math.sin(b)*(i?i/2:x),t.moveTo(n-h,r-a),t.lineTo(n+f,r-o),t.lineTo(n+h,r+a),t.lineTo(n-f,r+o),t.closePath();break;case"crossRot":b+=Cn;case"cross":h=Math.cos(b)*(i?i/2:x),o=Math.cos(b)*x,a=Math.sin(b)*x,f=Math.sin(b)*(i?i/2:x),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o);break;case"star":h=Math.cos(b)*(i?i/2:x),o=Math.cos(b)*x,a=Math.sin(b)*x,f=Math.sin(b)*(i?i/2:x),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o),b+=Cn,h=Math.cos(b)*(i?i/2:x),o=Math.cos(b)*x,a=Math.sin(b)*x,f=Math.sin(b)*(i?i/2:x),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o);break;case"line":o=i?i/2:Math.cos(b)*x,a=Math.sin(b)*x,t.moveTo(n-o,r-a),t.lineTo(n+o,r+a);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(b)*(i?i/2:x),r+Math.sin(b)*x);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function Gp(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function au(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function lu(t){t.restore()}function Mb(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),Q(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function Pb(t,e,n,r,i){if(i.strikethrough||i.underline){const s=t.measureText(r),o=e-s.actualBoundingBoxLeft,a=e+s.actualBoundingBoxRight,l=n-s.actualBoundingBoxAscent,u=n+s.actualBoundingBoxDescent,d=i.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(o,d),t.lineTo(a,d),t.stroke()}}function Eb(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Yi(t,e,n,r,i,s={}){const o=pe(e)?e:[e],a=s.strokeWidth>0&&s.strokeColor!=="";let l,u;for(t.save(),t.font=i.string,Mb(t,s),l=0;l<o.length;++l)u=o[l],s.backdrop&&Eb(t,s.backdrop),a&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),Q(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(u,n,r,s.maxWidth)),t.fillText(u,n,r,s.maxWidth),Pb(t,n,r,u,s),r+=Number(i.lineHeight);t.restore()}function $o(t,e){const{x:n,y:r,w:i,h:s,radius:o}=e;t.arc(n+o.topLeft,r+o.topLeft,o.topLeft,1.5*he,he,!0),t.lineTo(n,r+s-o.bottomLeft),t.arc(n+o.bottomLeft,r+s-o.bottomLeft,o.bottomLeft,he,xe,!0),t.lineTo(n+i-o.bottomRight,r+s),t.arc(n+i-o.bottomRight,r+s-o.bottomRight,o.bottomRight,xe,0,!0),t.lineTo(n+i,r+o.topRight),t.arc(n+i-o.topRight,r+o.topRight,o.topRight,0,-xe,!0),t.lineTo(n+o.topLeft,r)}const Tb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Rb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Ob(t,e){const n=(""+t).match(Tb);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const Db=t=>+t||0;function cu(t,e){const n={},r=H(e),i=r?Object.keys(e):e,s=H(t)?r?o=>W(t[o],t[e[o]]):o=>t[o]:()=>t;for(const o of i)n[o]=Db(s(o));return n}function qp(t){return cu(t,{top:"y",right:"x",bottom:"y",left:"x"})}function Nr(t){return cu(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ut(t){const e=qp(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Pe(t,e){t=t||{},e=e||ce.font;let n=W(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let r=W(t.style,e.style);r&&!(""+r).match(Rb)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);const i={family:W(t.family,e.family),lineHeight:Ob(W(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:W(t.weight,e.weight),string:""};return i.string=Cb(i),i}function js(t,e,n,r){let i,s,o;for(i=0,s=t.length;i<s;++i)if(o=t[i],o!==void 0&&o!==void 0)return o}function $b(t,e,n){const{min:r,max:i}=t,s=Ap(e,(i-r)/2),o=(a,l)=>n&&a===0?0:a+l;return{min:o(r,-Math.abs(s)),max:o(i,s)}}function Ir(t,e){return Object.assign(Object.create(t),e)}function uu(t,e=[""],n,r,i=()=>t[0]){const s=n||t;typeof r>"u"&&(r=tg("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:r,_getTarget:i,override:a=>uu([a,...t],e,s,r)};return new Proxy(o,{deleteProperty(a,l){return delete a[l],delete a._keys,delete t[0][l],!0},get(a,l){return Jp(a,l,()=>Wb(l,e,t,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(a,l){return sh(a).includes(l)},ownKeys(a){return sh(a)},set(a,l,u){const d=a._storage||(a._storage=i());return a[l]=d[l]=u,delete a._keys,!0}})}function $r(t,e,n,r){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:Zp(t,r),setContext:s=>$r(t,s,n,r),override:s=>$r(t.override(s),e,n,r)};return new Proxy(i,{deleteProperty(s,o){return delete s[o],delete t[o],!0},get(s,o,a){return Jp(s,o,()=>Fb(s,o,a))},getOwnPropertyDescriptor(s,o){return s._descriptors.allKeys?Reflect.has(t,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,o)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(s,o){return Reflect.has(t,o)},ownKeys(){return Reflect.ownKeys(t)},set(s,o,a){return t[o]=a,delete s[o],!0}})}function Zp(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:r,isScriptable:Sn(n)?n:()=>n,isIndexable:Sn(r)?r:()=>r}}const Lb=(t,e)=>t?t+ru(e):e,du=(t,e)=>H(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function Jp(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const r=n();return t[e]=r,r}function Fb(t,e,n){const{_proxy:r,_context:i,_subProxy:s,_descriptors:o}=t;let a=r[e];return Sn(a)&&o.isScriptable(e)&&(a=zb(e,a,t,n)),pe(a)&&a.length&&(a=Ab(e,a,t,o.isIndexable)),du(e,a)&&(a=$r(a,i,s&&s[e],o)),a}function zb(t,e,n,r){const{_proxy:i,_context:s,_subProxy:o,_stack:a}=n;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(s,o||r);return a.delete(t),du(t,l)&&(l=hu(i._scopes,i,t,l)),l}function Ab(t,e,n,r){const{_proxy:i,_context:s,_subProxy:o,_descriptors:a}=n;if(typeof s.index<"u"&&r(t))return e[s.index%e.length];if(H(e[0])){const l=e,u=i._scopes.filter(d=>d!==l);e=[];for(const d of l){const h=hu(u,i,t,d);e.push($r(h,s,o&&o[t],a))}}return e}function eg(t,e,n){return Sn(t)?t(e,n):t}const Ib=(t,e)=>t===!0?e:typeof t=="string"?Kn(e,t):void 0;function Ub(t,e,n,r,i){for(const s of e){const o=Ib(n,s);if(o){t.add(o);const a=eg(o._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==r)return a}else if(o===!1&&typeof r<"u"&&n!==r)return null}return!1}function hu(t,e,n,r){const i=e._rootScopes,s=eg(e._fallback,n,r),o=[...t,...i],a=new Set;a.add(r);let l=ih(a,o,n,s||n,r);return l===null||typeof s<"u"&&s!==n&&(l=ih(a,o,s,l,r),l===null)?!1:uu(Array.from(a),[""],i,s,()=>Hb(e,n,r))}function ih(t,e,n,r,i){for(;n;)n=Ub(t,e,n,r,i);return n}function Hb(t,e,n){const r=t._getTarget();e in r||(r[e]={});const i=r[e];return pe(i)&&H(n)?n:i||{}}function Wb(t,e,n,r){let i;for(const s of e)if(i=tg(Lb(s,t),n),typeof i<"u")return du(t,i)?hu(n,r,t,i):i}function tg(t,e){for(const n of e){if(!n)continue;const r=n[t];if(typeof r<"u")return r}}function sh(t){let e=t._keys;return e||(e=t._keys=Bb(t._scopes)),e}function Bb(t){const e=new Set;for(const n of t)for(const r of Object.keys(n).filter(i=>!i.startsWith("_")))e.add(r);return Array.from(e)}function fu(){return typeof window<"u"&&typeof document<"u"}function mu(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Lo(t,e,n){let r;return typeof t=="string"?(r=parseInt(t,10),t.indexOf("%")!==-1&&(r=r/100*e.parentNode[n])):r=t,r}const aa=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function Vb(t,e){return aa(t).getPropertyValue(e)}const Yb=["top","right","bottom","left"];function Hn(t,e,n){const r={};n=n?"-"+n:"";for(let i=0;i<4;i++){const s=Yb[i];r[s]=parseFloat(t[e+"-"+s+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}const Xb=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function Qb(t,e){const n=t.touches,r=n&&n.length?n[0]:t,{offsetX:i,offsetY:s}=r;let o=!1,a,l;if(Xb(i,s,t.target))a=i,l=s;else{const u=e.getBoundingClientRect();a=r.clientX-u.left,l=r.clientY-u.top,o=!0}return{x:a,y:l,box:o}}function $n(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:r}=e,i=aa(n),s=i.boxSizing==="border-box",o=Hn(i,"padding"),a=Hn(i,"border","width"),{x:l,y:u,box:d}=Qb(t,n),h=o.left+(d&&a.left),f=o.top+(d&&a.top);let{width:m,height:v}=e;return s&&(m-=o.width+a.width,v-=o.height+a.height),{x:Math.round((l-h)/m*n.width/r),y:Math.round((u-f)/v*n.height/r)}}function Kb(t,e,n){let r,i;if(e===void 0||n===void 0){const s=t&&mu(t);if(!s)e=t.clientWidth,n=t.clientHeight;else{const o=s.getBoundingClientRect(),a=aa(s),l=Hn(a,"border","width"),u=Hn(a,"padding");e=o.width-u.width-l.width,n=o.height-u.height-l.height,r=Lo(a.maxWidth,s,"clientWidth"),i=Lo(a.maxHeight,s,"clientHeight")}}return{width:e,height:n,maxWidth:r||Ro,maxHeight:i||Ro}}const ks=t=>Math.round(t*10)/10;function Gb(t,e,n,r){const i=aa(t),s=Hn(i,"margin"),o=Lo(i.maxWidth,t,"clientWidth")||Ro,a=Lo(i.maxHeight,t,"clientHeight")||Ro,l=Kb(t,e,n);let{width:u,height:d}=l;if(i.boxSizing==="content-box"){const f=Hn(i,"border","width"),m=Hn(i,"padding");u-=m.width+f.width,d-=m.height+f.height}return u=Math.max(0,u-s.width),d=Math.max(0,r?u/r:d-s.height),u=ks(Math.min(u,o,l.maxWidth)),d=ks(Math.min(d,a,l.maxHeight)),u&&!d&&(d=ks(u/2)),(e!==void 0||n!==void 0)&&r&&l.height&&d>l.height&&(d=l.height,u=ks(Math.floor(d*r))),{width:u,height:d}}function oh(t,e,n){const r=e||1,i=Math.floor(t.height*r),s=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const o=t.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),t.currentDevicePixelRatio!==r||o.height!==i||o.width!==s?(t.currentDevicePixelRatio=r,o.height=i,o.width=s,t.ctx.setTransform(r,0,0,r,0,0),!0):!1}const qb=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};fu()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function ah(t,e){const n=Vb(t,e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}const Zb=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,r){return n-r},leftForLtr(n,r){return n-r}}},Jb=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function jr(t,e,n){return t?Zb(e,n):Jb()}function ng(t,e){let n,r;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,r=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function rg(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Cs(t,e,n){return t.options.clip?t[n]:e[n]}function ew(t,e){const{xScale:n,yScale:r}=t;return n&&r?{left:Cs(n,e,"left"),right:Cs(n,e,"right"),top:Cs(r,e,"top"),bottom:Cs(r,e,"bottom")}:e}function tw(t,e){const n=e._clip;if(n.disabled)return!1;const r=ew(e,t.chartArea);return{left:n.left===!1?0:r.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:r.right+(n.right===!0?0:n.right),top:n.top===!1?0:r.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:r.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class nw{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,r,i){const s=n.listeners[i],o=n.duration;s.forEach(a=>a({chart:e,initial:n.initial,numSteps:o,currentStep:Math.min(r-n.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=Vp.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((r,i)=>{if(!r.running||!r.items.length)return;const s=r.items;let o=s.length-1,a=!1,l;for(;o>=0;--o)l=s[o],l._active?(l._total>r.duration&&(r.duration=l._total),l.tick(e),a=!0):(s[o]=s[s.length-1],s.pop());a&&(i.draw(),this._notify(i,r,e,"progress")),s.length||(r.running=!1,this._notify(i,r,e,"complete"),r.initial=!1),n+=s.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let r=n.get(e);return r||(r={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,r)),r}listen(e,n,r){this._getAnims(e).listeners[n].push(r)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((r,i)=>Math.max(r,i._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const r=n.items;let i=r.length-1;for(;i>=0;--i)r[i].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Rt=new nw;const lh="transparent",rw={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const r=Jd(t||lh),i=r.valid&&Jd(e||lh);return i&&i.valid?i.mix(r,n).hexString():e},number(t,e,n){return t+(e-t)*n}};class iw{constructor(e,n,r,i){const s=n[r];i=js([e.to,i,s,e.from]);const o=js([e.from,s,i]);this._active=!0,this._fn=e.fn||rw[e.type||typeof o],this._easing=Si[e.easing]||Si.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=r,this._from=o,this._to=i,this._promises=void 0}active(){return this._active}update(e,n,r){if(this._active){this._notify(!1);const i=this._target[this._prop],s=r-this._start,o=this._duration-s;this._start=r,this._duration=Math.floor(Math.max(o,e.duration)),this._total+=s,this._loop=!!e.loop,this._to=js([e.to,n,i,e.from]),this._from=js([e.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,r=this._duration,i=this._prop,s=this._from,o=this._loop,a=this._to;let l;if(this._active=s!==a&&(o||n<r),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=s;return}l=n/r%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(s,a,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,r)=>{e.push({res:n,rej:r})})}_notify(e){const n=e?"res":"rej",r=this._promises||[];for(let i=0;i<r.length;i++)r[i][n]()}}class ig{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!H(e))return;const n=Object.keys(ce.animation),r=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const s=e[i];if(!H(s))return;const o={};for(const a of n)o[a]=s[a];(pe(s.properties)&&s.properties||[i]).forEach(a=>{(a===i||!r.has(a))&&r.set(a,o)})})}_animateOptions(e,n){const r=n.options,i=ow(e,r);if(!i)return[];const s=this._createAnimations(i,r);return r.$shared&&sw(e.options.$animations,r).then(()=>{e.options=r},()=>{}),s}_createAnimations(e,n){const r=this._properties,i=[],s=e.$animations||(e.$animations={}),o=Object.keys(n),a=Date.now();let l;for(l=o.length-1;l>=0;--l){const u=o[l];if(u.charAt(0)==="$")continue;if(u==="options"){i.push(...this._animateOptions(e,n));continue}const d=n[u];let h=s[u];const f=r.get(u);if(h)if(f&&h.active()){h.update(f,d,a);continue}else h.cancel();if(!f||!f.duration){e[u]=d;continue}s[u]=h=new iw(f,e,u,d),i.push(h)}return i}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const r=this._createAnimations(e,n);if(r.length)return Rt.add(this._chart,r),!0}}function sw(t,e){const n=[],r=Object.keys(e);for(let i=0;i<r.length;i++){const s=t[r[i]];s&&s.active()&&n.push(s.wait())}return Promise.all(n)}function ow(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function ch(t,e){const n=t&&t.options||{},r=n.reverse,i=n.min===void 0?e:0,s=n.max===void 0?e:0;return{start:r?s:i,end:r?i:s}}function aw(t,e,n){if(n===!1)return!1;const r=ch(t,n),i=ch(e,n);return{top:i.end,right:r.end,bottom:i.start,left:r.start}}function lw(t){let e,n,r,i;return H(t)?(e=t.top,n=t.right,r=t.bottom,i=t.left):e=n=r=i=t,{top:e,right:n,bottom:r,left:i,disabled:t===!1}}function sg(t,e){const n=[],r=t._getSortedDatasetMetas(e);let i,s;for(i=0,s=r.length;i<s;++i)n.push(r[i].index);return n}function uh(t,e,n,r={}){const i=t.keys,s=r.mode==="single";let o,a,l,u;if(e===null)return;let d=!1;for(o=0,a=i.length;o<a;++o){if(l=+i[o],l===n){if(d=!0,r.all)continue;break}u=t.values[l],ct(u)&&(s||e===0||yn(e)===yn(u))&&(e+=u)}return!d&&!r.all?0:e}function cw(t,e){const{iScale:n,vScale:r}=e,i=n.axis==="x"?"x":"y",s=r.axis==="x"?"x":"y",o=Object.keys(t),a=new Array(o.length);let l,u,d;for(l=0,u=o.length;l<u;++l)d=o[l],a[l]={[i]:d,[s]:t[d]};return a}function Ba(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function uw(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function dw(t){const{min:e,max:n,minDefined:r,maxDefined:i}=t.getUserBounds();return{min:r?e:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function hw(t,e,n){const r=t[e]||(t[e]={});return r[n]||(r[n]={})}function dh(t,e,n,r){for(const i of e.getMatchingVisibleMetas(r).reverse()){const s=t[i.index];if(n&&s>0||!n&&s<0)return i.index}return null}function hh(t,e){const{chart:n,_cachedMeta:r}=t,i=n._stacks||(n._stacks={}),{iScale:s,vScale:o,index:a}=r,l=s.axis,u=o.axis,d=uw(s,o,r),h=e.length;let f;for(let m=0;m<h;++m){const v=e[m],{[l]:x,[u]:b}=v,g=v._stacks||(v._stacks={});f=g[u]=hw(i,d,x),f[a]=b,f._top=dh(f,o,!0,r.type),f._bottom=dh(f,o,!1,r.type);const p=f._visualValues||(f._visualValues={});p[a]=b}}function Va(t,e){const n=t.scales;return Object.keys(n).filter(r=>n[r].axis===e).shift()}function fw(t,e){return Ir(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function mw(t,e,n){return Ir(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function Zr(t,e){const n=t.controller.index,r=t.vScale&&t.vScale.axis;if(r){e=e||t._parsed;for(const i of e){const s=i._stacks;if(!s||s[r]===void 0||s[r][n]===void 0)return;delete s[r][n],s[r]._visualValues!==void 0&&s[r]._visualValues[n]!==void 0&&delete s[r]._visualValues[n]}}}const Ya=t=>t==="reset"||t==="none",fh=(t,e)=>e?t:Object.assign({},t),pw=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:sg(n,!0),values:null};class kr{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Ba(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&Zr(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,r=this.getDataset(),i=(h,f,m,v)=>h==="x"?f:h==="r"?v:m,s=n.xAxisID=W(r.xAxisID,Va(e,"x")),o=n.yAxisID=W(r.yAxisID,Va(e,"y")),a=n.rAxisID=W(r.rAxisID,Va(e,"r")),l=n.indexAxis,u=n.iAxisID=i(l,s,o,a),d=n.vAxisID=i(l,o,s,a);n.xScale=this.getScaleForId(s),n.yScale=this.getScaleForId(o),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&Gd(this._data,this),e._stacked&&Zr(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),r=this._data;if(H(n)){const i=this._cachedMeta;this._data=cw(n,i)}else if(r!==n){if(r){Gd(r,this);const i=this._cachedMeta;Zr(i),i._parsed=[]}n&&Object.isExtensible(n)&&pb(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,r=this.getDataset();let i=!1;this._dataCheck();const s=n._stacked;n._stacked=Ba(n.vScale,n),n.stack!==r.stack&&(i=!0,Zr(n),n.stack=r.stack),this._resyncElements(e),(i||s!==n._stacked)&&(hh(this,n._parsed),n._stacked=Ba(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),r=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(r,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:r,_data:i}=this,{iScale:s,_stacked:o}=r,a=s.axis;let l=e===0&&n===i.length?!0:r._sorted,u=e>0&&r._parsed[e-1],d,h,f;if(this._parsing===!1)r._parsed=i,r._sorted=!0,f=i;else{pe(i[e])?f=this.parseArrayData(r,i,e,n):H(i[e])?f=this.parseObjectData(r,i,e,n):f=this.parsePrimitiveData(r,i,e,n);const m=()=>h[a]===null||u&&h[a]<u[a];for(d=0;d<n;++d)r._parsed[d+e]=h=f[d],l&&(m()&&(l=!1),u=h);r._sorted=l}o&&hh(this,f)}parsePrimitiveData(e,n,r,i){const{iScale:s,vScale:o}=e,a=s.axis,l=o.axis,u=s.getLabels(),d=s===o,h=new Array(i);let f,m,v;for(f=0,m=i;f<m;++f)v=f+r,h[f]={[a]:d||s.parse(u[v],v),[l]:o.parse(n[v],v)};return h}parseArrayData(e,n,r,i){const{xScale:s,yScale:o}=e,a=new Array(i);let l,u,d,h;for(l=0,u=i;l<u;++l)d=l+r,h=n[d],a[l]={x:s.parse(h[0],d),y:o.parse(h[1],d)};return a}parseObjectData(e,n,r,i){const{xScale:s,yScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=new Array(i);let d,h,f,m;for(d=0,h=i;d<h;++d)f=d+r,m=n[f],u[d]={x:s.parse(Kn(m,a),f),y:o.parse(Kn(m,l),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,r){const i=this.chart,s=this._cachedMeta,o=n[e.axis],a={keys:sg(i,!0),values:n._stacks[e.axis]._visualValues};return uh(a,o,s.index,{mode:r})}updateRangeFromParsed(e,n,r,i){const s=r[n.axis];let o=s===null?NaN:s;const a=i&&r._stacks[n.axis];i&&a&&(i.values=a,o=uh(i,s,this._cachedMeta.index)),e.min=Math.min(e.min,o),e.max=Math.max(e.max,o)}getMinMax(e,n){const r=this._cachedMeta,i=r._parsed,s=r._sorted&&e===r.iScale,o=i.length,a=this._getOtherScale(e),l=pw(n,r,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=dw(a);let f,m;function v(){m=i[f];const x=m[a.axis];return!ct(m[e.axis])||d>x||h<x}for(f=0;f<o&&!(!v()&&(this.updateRangeFromParsed(u,e,m,l),s));++f);if(s){for(f=o-1;f>=0;--f)if(!v()){this.updateRangeFromParsed(u,e,m,l);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,r=[];let i,s,o;for(i=0,s=n.length;i<s;++i)o=n[i][e.axis],ct(o)&&r.push(o);return r}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,r=n.iScale,i=n.vScale,s=this.getParsed(e);return{label:r?""+r.getLabelForValue(s[r.axis]):"",value:i?""+i.getLabelForValue(s[i.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=lw(W(this.options.clip,aw(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,r=this._cachedMeta,i=r.data||[],s=n.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||i.length-a,u=this.options.drawActiveElementsOnTop;let d;for(r.dataset&&r.dataset.draw(e,s,a,l),d=a;d<a+l;++d){const h=i[d];h.hidden||(h.active&&u?o.push(h):h.draw(e,s))}for(d=0;d<o.length;++d)o[d].draw(e,s)}getStyle(e,n){const r=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(r):this.resolveDataElementOptions(e||0,r)}getContext(e,n,r){const i=this.getDataset();let s;if(e>=0&&e<this._cachedMeta.data.length){const o=this._cachedMeta.data[e];s=o.$context||(o.$context=mw(this.getContext(),e,o)),s.parsed=this.getParsed(e),s.raw=i.data[e],s.index=s.dataIndex=e}else s=this.$context||(this.$context=fw(this.chart.getContext(),this.index)),s.dataset=i,s.index=s.datasetIndex=this.index;return s.active=!!n,s.mode=r,s}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",r){const i=n==="active",s=this._cachedDataOpts,o=e+"-"+n,a=s[o],l=this.enableOptionSharing&&Vi(r);if(a)return fh(a,l);const u=this.chart.config,d=u.datasetElementScopeKeys(this._type,e),h=i?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),d),m=Object.keys(ce.elements[e]),v=()=>this.getContext(r,i,n),x=u.resolveNamedOptions(f,m,v,h);return x.$shared&&(x.$shared=l,s[o]=Object.freeze(fh(x,l))),x}_resolveAnimations(e,n,r){const i=this.chart,s=this._cachedDataOpts,o=`animation-${n}`,a=s[o];if(a)return a;let l;if(i.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);l=d.createResolver(f,this.getContext(e,r,n))}const u=new ig(i,l&&l.animations);return l&&l._cacheable&&(s[o]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||Ya(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const r=this.resolveDataElementOptions(e,n),i=this._sharedOptions,s=this.getSharedOptions(r),o=this.includeOptions(n,s)||s!==i;return this.updateSharedOptions(s,n,r),{sharedOptions:s,includeOptions:o}}updateElement(e,n,r,i){Ya(i)?Object.assign(e,r):this._resolveAnimations(n,i).update(e,r)}updateSharedOptions(e,n,r){e&&!Ya(n)&&this._resolveAnimations(void 0,n).update(e,r)}_setStyle(e,n,r,i){e.active=i;const s=this.getStyle(n,i);this._resolveAnimations(n,r,i).update(e,{options:!i&&this.getSharedOptions(s)||s})}removeHoverStyle(e,n,r){this._setStyle(e,r,"active",!1)}setHoverStyle(e,n,r){this._setStyle(e,r,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,r=this._cachedMeta.data;for(const[a,l,u]of this._syncList)this[a](l,u);this._syncList=[];const i=r.length,s=n.length,o=Math.min(s,i);o&&this.parse(0,o),s>i?this._insertElements(i,s-i,e):s<i&&this._removeElements(s,i-s)}_insertElements(e,n,r=!0){const i=this._cachedMeta,s=i.data,o=e+n;let a;const l=u=>{for(u.length+=n,a=u.length-1;a>=o;a--)u[a]=u[a-n]};for(l(s),a=e;a<o;++a)s[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(e,n),r&&this.updateElements(s,e,n,"reset")}updateElements(e,n,r,i){}_removeElements(e,n){const r=this._cachedMeta;if(this._parsing){const i=r._parsed.splice(e,n);r._stacked&&Zr(r,i)}r.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,r,i]=e;this[n](r,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const r=arguments.length-2;r&&this._sync(["_insertElements",e,r])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}L(kr,"defaults",{}),L(kr,"datasetElementType",null),L(kr,"dataElementType",null);function gw(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let r=[];for(let i=0,s=n.length;i<s;i++)r=r.concat(n[i].controller.getAllParsedValues(t));t._cache.$bar=Bp(r.sort((i,s)=>i-s))}return t._cache.$bar}function xw(t){const e=t.iScale,n=gw(e,t.type);let r=e._length,i,s,o,a;const l=()=>{o===32767||o===-32768||(Vi(a)&&(r=Math.min(r,Math.abs(o-a)||r)),a=o)};for(i=0,s=n.length;i<s;++i)o=e.getPixelForValue(n[i]),l();for(a=void 0,i=0,s=e.ticks.length;i<s;++i)o=e.getPixelForTick(i),l();return r}function vw(t,e,n,r){const i=n.barThickness;let s,o;return Q(i)?(s=e.min*n.categoryPercentage,o=n.barPercentage):(s=i*r,o=1),{chunk:s/r,ratio:o,start:e.pixels[t]-s/2}}function yw(t,e,n,r){const i=e.pixels,s=i[t];let o=t>0?i[t-1]:null,a=t<i.length-1?i[t+1]:null;const l=n.categoryPercentage;o===null&&(o=s-(a===null?e.end-e.start:a-s)),a===null&&(a=s+s-o);const u=s-(s-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/r,ratio:n.barPercentage,start:u}}function bw(t,e,n,r){const i=n.parse(t[0],r),s=n.parse(t[1],r),o=Math.min(i,s),a=Math.max(i,s);let l=o,u=a;Math.abs(o)>Math.abs(a)&&(l=a,u=o),e[n.axis]=u,e._custom={barStart:l,barEnd:u,start:i,end:s,min:o,max:a}}function og(t,e,n,r){return pe(t)?bw(t,e,n,r):e[n.axis]=n.parse(t,r),e}function mh(t,e,n,r){const i=t.iScale,s=t.vScale,o=i.getLabels(),a=i===s,l=[];let u,d,h,f;for(u=n,d=n+r;u<d;++u)f=e[u],h={},h[i.axis]=a||i.parse(o[u],u),l.push(og(f,h,s,u));return l}function Xa(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function ww(t,e,n){return t!==0?yn(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function Sw(t){let e,n,r,i,s;return t.horizontal?(e=t.base>t.x,n="left",r="right"):(e=t.base<t.y,n="bottom",r="top"),e?(i="end",s="start"):(i="start",s="end"),{start:n,end:r,reverse:e,top:i,bottom:s}}function _w(t,e,n,r){let i=e.borderSkipped;const s={};if(!i){t.borderSkipped=s;return}if(i===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:a,reverse:l,top:u,bottom:d}=Sw(t);i==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===r?i=u:(n._bottom||0)===r?i=d:(s[ph(d,o,a,l)]=!0,i=u)),s[ph(i,o,a,l)]=!0,t.borderSkipped=s}function ph(t,e,n,r){return r?(t=Nw(t,e,n),t=gh(t,n,e)):t=gh(t,e,n),t}function Nw(t,e,n){return t===e?n:t===n?e:t}function gh(t,e,n){return t==="start"?e:t==="end"?n:t}function jw(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Ks extends kr{parsePrimitiveData(e,n,r,i){return mh(e,n,r,i)}parseArrayData(e,n,r,i){return mh(e,n,r,i)}parseObjectData(e,n,r,i){const{iScale:s,vScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=s.axis==="x"?a:l,d=o.axis==="x"?a:l,h=[];let f,m,v,x;for(f=r,m=r+i;f<m;++f)x=n[f],v={},v[s.axis]=s.parse(Kn(x,u),f),h.push(og(Kn(x,d),v,o,f));return h}updateRangeFromParsed(e,n,r,i){super.updateRangeFromParsed(e,n,r,i);const s=r._custom;s&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,s.min),e.max=Math.max(e.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:r,vScale:i}=n,s=this.getParsed(e),o=s._custom,a=Xa(o)?"["+o.start+", "+o.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+r.getLabelForValue(s[r.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,r,i){const s=i==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),u=a.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,i);for(let m=n;m<n+r;m++){const v=this.getParsed(m),x=s||Q(v[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(m),b=this._calculateBarIndexPixels(m,d),g=(v._stacks||{})[a.axis],p={horizontal:u,base:x.base,enableBorderRadius:!g||Xa(v._custom)||o===g._top||o===g._bottom,x:u?x.head:b.center,y:u?b.center:x.head,height:u?b.size:Math.abs(x.size),width:u?Math.abs(x.size):b.size};f&&(p.options=h||this.resolveDataElementOptions(m,e[m].active?"active":i));const y=p.options||e[m].options;_w(p,y,g,o),jw(p,y,d.ratio),this.updateElement(e[m],m,p,i)}}_getStacks(e,n){const{iScale:r}=this._cachedMeta,i=r.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),s=r.options.stacked,o=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[r.axis],u=d=>{const h=d._parsed.find(m=>m[r.axis]===l),f=h&&h[d.vScale.axis];if(Q(f)||isNaN(f))return!0};for(const d of i)if(!(n!==void 0&&u(d))&&((s===!1||o.indexOf(d.stack)===-1||s===void 0&&d.stack===void 0)&&o.push(d.stack),d.index===e))break;return o.length||o.push(void 0),o}_getStackCount(e){return this._getStacks(void 0,e).length}_getStackIndex(e,n,r){const i=this._getStacks(e,r),s=n!==void 0?i.indexOf(n):-1;return s===-1?i.length-1:s}_getRuler(){const e=this.options,n=this._cachedMeta,r=n.iScale,i=[];let s,o;for(s=0,o=n.data.length;s<o;++s)i.push(r.getPixelForValue(this.getParsed(s)[r.axis],s));const a=e.barThickness;return{min:a||xw(n),pixels:i,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:e.grouped,ratio:a?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:r,index:i},options:{base:s,minBarLength:o}}=this,a=s||0,l=this.getParsed(e),u=l._custom,d=Xa(u);let h=l[n.axis],f=0,m=r?this.applyStack(n,l,r):h,v,x;m!==h&&(f=m-h,m=h),d&&(h=u.barStart,m=u.barEnd-u.barStart,h!==0&&yn(h)!==yn(u.barEnd)&&(f=0),f+=h);const b=!Q(s)&&!d?s:f;let g=n.getPixelForValue(b);if(this.chart.getDataVisibility(e)?v=n.getPixelForValue(f+m):v=g,x=v-g,Math.abs(x)<o){x=ww(x,n,a)*o,h===a&&(g-=x/2);const p=n.getPixelForDecimal(0),y=n.getPixelForDecimal(1),w=Math.min(p,y),_=Math.max(p,y);g=Math.max(Math.min(g,_),w),v=g+x,r&&!d&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(v)-n.getValueForPixel(g))}if(g===n.getPixelForValue(a)){const p=yn(x)*n.getLineWidthForValue(a)/2;g+=p,x-=p}return{size:x,base:g,head:v,center:v+x/2}}_calculateBarIndexPixels(e,n){const r=n.scale,i=this.options,s=i.skipNull,o=W(i.maxBarThickness,1/0);let a,l;if(n.grouped){const u=s?this._getStackCount(e):n.stackCount,d=i.barThickness==="flex"?yw(e,n,i,u):vw(e,n,i,u),h=this._getStackIndex(this.index,this._cachedMeta.stack,s?e:void 0);a=d.start+d.chunk*h+d.chunk/2,l=Math.min(o,d.chunk*d.ratio)}else a=r.getPixelForValue(this.getParsed(e)[r.axis],e),l=Math.min(o,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,r=e.data,i=r.length;let s=0;for(;s<i;++s)this.getParsed(s)[n.axis]!==null&&!r[s].hidden&&r[s].draw(this._ctx)}}L(Ks,"id","bar"),L(Ks,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),L(Ks,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function kw(t,e,n){let r=1,i=1,s=0,o=0;if(e<de){const a=t,l=a+e,u=Math.cos(a),d=Math.sin(a),h=Math.cos(l),f=Math.sin(l),m=(y,w,_)=>Do(y,a,l,!0)?1:Math.max(w,w*n,_,_*n),v=(y,w,_)=>Do(y,a,l,!0)?-1:Math.min(w,w*n,_,_*n),x=m(0,u,h),b=m(xe,d,f),g=v(he,u,h),p=v(he+xe,d,f);r=(x-g)/2,i=(b-p)/2,s=-(x+g)/2,o=-(b+p)/2}return{ratioX:r,ratioY:i,offsetX:s,offsetY:o}}class li extends kr{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const r=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=r;else{let s=l=>+r[l];if(H(r[e])){const{key:l="value"}=this._parsing;s=u=>+Kn(r[u],l)}let o,a;for(o=e,a=e+n;o<a;++o)i._parsed[o]=s(o)}}_getRotation(){return It(this.options.rotation-90)}_getCircumference(){return It(this.options.circumference)}_getRotationExtents(){let e=de,n=-de;for(let r=0;r<this.chart.data.datasets.length;++r)if(this.chart.isDatasetVisible(r)&&this.chart.getDatasetMeta(r).type===this._type){const i=this.chart.getDatasetMeta(r).controller,s=i._getRotation(),o=i._getCircumference();e=Math.min(e,s),n=Math.max(n,s+o)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:r}=n,i=this._cachedMeta,s=i.data,o=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,a=Math.max((Math.min(r.width,r.height)-o)/2,0),l=Math.min(J1(this.options.cutout,a),1),u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:m,offsetX:v,offsetY:x}=kw(h,d,l),b=(r.width-o)/f,g=(r.height-o)/m,p=Math.max(Math.min(b,g)/2,0),y=Ap(this.options.radius,p),w=Math.max(y*l,0),_=(y-w)/this._getVisibleDatasetWeightTotal();this.offsetX=v*y,this.offsetY=x*y,i.total=this.calculateTotal(),this.outerRadius=y-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*u,0),this.updateElements(s,0,s.length,e)}_circumference(e,n){const r=this.options,i=this._cachedMeta,s=this._getCircumference();return n&&r.animation.animateRotate||!this.chart.getDataVisibility(e)||i._parsed[e]===null||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*s/de)}updateElements(e,n,r,i){const s=i==="reset",o=this.chart,a=o.chartArea,u=o.options.animation,d=(a.left+a.right)/2,h=(a.top+a.bottom)/2,f=s&&u.animateScale,m=f?0:this.innerRadius,v=f?0:this.outerRadius,{sharedOptions:x,includeOptions:b}=this._getSharedOptions(n,i);let g=this._getRotation(),p;for(p=0;p<n;++p)g+=this._circumference(p,s);for(p=n;p<n+r;++p){const y=this._circumference(p,s),w=e[p],_={x:d+this.offsetX,y:h+this.offsetY,startAngle:g,endAngle:g+y,circumference:y,outerRadius:v,innerRadius:m};b&&(_.options=x||this.resolveDataElementOptions(p,w.active?"active":i)),g+=y,this.updateElement(w,p,_,i)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let r=0,i;for(i=0;i<n.length;i++){const s=e._parsed[i];s!==null&&!isNaN(s)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(r+=Math.abs(s))}return r}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?de*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,r=this.chart,i=r.data.labels||[],s=ou(n._parsed[e],r.options.locale);return{label:i[e]||"",value:s}}getMaxBorderWidth(e){let n=0;const r=this.chart;let i,s,o,a,l;if(!e){for(i=0,s=r.data.datasets.length;i<s;++i)if(r.isDatasetVisible(i)){o=r.getDatasetMeta(i),e=o.data,a=o.controller;break}}if(!e)return 0;for(i=0,s=e.length;i<s;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let r=0,i=e.length;r<i;++r){const s=this.resolveDataElementOptions(r);n=Math.max(n,s.offset||0,s.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let r=0;r<e;++r)this.chart.isDatasetVisible(r)&&(n+=this._getRingWeight(r));return n}_getRingWeight(e){return Math.max(W(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}L(li,"id","doughnut"),L(li,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),L(li,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),L(li,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:r,color:i}}=e.legend.options;return n.labels.map((s,o)=>{const l=e.getDatasetMeta(0).controller.getStyle(o);return{text:s,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:r,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,r){r.chart.toggleDataVisibility(n.index),r.chart.update()}}}});function En(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class pu{constructor(e){L(this,"options");this.options=e||{}}static override(e){Object.assign(pu.prototype,e)}init(){}formats(){return En()}parse(){return En()}format(){return En()}add(){return En()}diff(){return En()}startOf(){return En()}endOf(){return En()}}var Cw={_date:pu};function Mw(t,e,n,r){const{controller:i,data:s,_sorted:o}=t,a=i._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&e!=="r"&&o&&s.length){const u=a._reversePixels?fb:Zl;if(r){if(i._sharedOptions){const d=s[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=u(s,e,n-h),m=u(s,e,n+h);return{lo:f.lo,hi:m.hi}}}}else{const d=u(s,e,n);if(l){const{vScale:h}=i._cachedMeta,{_parsed:f}=t,m=f.slice(0,d.lo+1).reverse().findIndex(x=>!Q(x[h.axis]));d.lo-=Math.max(0,m);const v=f.slice(d.hi).findIndex(x=>!Q(x[h.axis]));d.hi+=Math.max(0,v)}return d}}return{lo:0,hi:s.length-1}}function la(t,e,n,r,i){const s=t.getSortedVisibleDatasetMetas(),o=n[e];for(let a=0,l=s.length;a<l;++a){const{index:u,data:d}=s[a],{lo:h,hi:f}=Mw(s[a],e,o,i);for(let m=h;m<=f;++m){const v=d[m];v.skip||r(v,u,m)}}}function Pw(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(r,i){const s=e?Math.abs(r.x-i.x):0,o=n?Math.abs(r.y-i.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(o,2))}}function Qa(t,e,n,r,i){const s=[];return!i&&!t.isPointInArea(e)||la(t,n,e,function(a,l,u){!i&&!Gp(a,t.chartArea,0)||a.inRange(e.x,e.y,r)&&s.push({element:a,datasetIndex:l,index:u})},!0),s}function Ew(t,e,n,r){let i=[];function s(o,a,l){const{startAngle:u,endAngle:d}=o.getProps(["startAngle","endAngle"],r),{angle:h}=Hp(o,{x:e.x,y:e.y});Do(h,u,d)&&i.push({element:o,datasetIndex:a,index:l})}return la(t,n,e,s),i}function Tw(t,e,n,r,i,s){let o=[];const a=Pw(n);let l=Number.POSITIVE_INFINITY;function u(d,h,f){const m=d.inRange(e.x,e.y,i);if(r&&!m)return;const v=d.getCenterPoint(i);if(!(!!s||t.isPointInArea(v))&&!m)return;const b=a(e,v);b<l?(o=[{element:d,datasetIndex:h,index:f}],l=b):b===l&&o.push({element:d,datasetIndex:h,index:f})}return la(t,n,e,u),o}function Ka(t,e,n,r,i,s){return!s&&!t.isPointInArea(e)?[]:n==="r"&&!r?Ew(t,e,n,i):Tw(t,e,n,r,i,s)}function xh(t,e,n,r,i){const s=[],o=n==="x"?"inXRange":"inYRange";let a=!1;return la(t,n,e,(l,u,d)=>{l[o]&&l[o](e[n],i)&&(s.push({element:l,datasetIndex:u,index:d}),a=a||l.inRange(e.x,e.y,i))}),r&&!a?[]:s}var Rw={modes:{index(t,e,n,r){const i=$n(e,t),s=n.axis||"x",o=n.includeInvisible||!1,a=n.intersect?Qa(t,i,s,r,o):Ka(t,i,s,!1,r,o),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const d=a[0].index,h=u.data[d];h&&!h.skip&&l.push({element:h,datasetIndex:u.index,index:d})}),l):[]},dataset(t,e,n,r){const i=$n(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;let a=n.intersect?Qa(t,i,s,r,o):Ka(t,i,s,!1,r,o);if(a.length>0){const l=a[0].datasetIndex,u=t.getDatasetMeta(l).data;a=[];for(let d=0;d<u.length;++d)a.push({element:u[d],datasetIndex:l,index:d})}return a},point(t,e,n,r){const i=$n(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;return Qa(t,i,s,r,o)},nearest(t,e,n,r){const i=$n(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;return Ka(t,i,s,n.intersect,r,o)},x(t,e,n,r){const i=$n(e,t);return xh(t,i,"x",n.intersect,r)},y(t,e,n,r){const i=$n(e,t);return xh(t,i,"y",n.intersect,r)}}};const ag=["left","top","right","bottom"];function Jr(t,e){return t.filter(n=>n.pos===e)}function vh(t,e){return t.filter(n=>ag.indexOf(n.pos)===-1&&n.box.axis===e)}function ei(t,e){return t.sort((n,r)=>{const i=e?r:n,s=e?n:r;return i.weight===s.weight?i.index-s.index:i.weight-s.weight})}function Ow(t){const e=[];let n,r,i,s,o,a;for(n=0,r=(t||[]).length;n<r;++n)i=t[n],{position:s,options:{stack:o,stackWeight:a=1}}=i,e.push({index:n,box:i,pos:s,horizontal:i.isHorizontal(),weight:i.weight,stack:o&&s+o,stackWeight:a});return e}function Dw(t){const e={};for(const n of t){const{stack:r,pos:i,stackWeight:s}=n;if(!r||!ag.includes(i))continue;const o=e[r]||(e[r]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=s}return e}function $w(t,e){const n=Dw(t),{vBoxMaxWidth:r,hBoxMaxHeight:i}=e;let s,o,a;for(s=0,o=t.length;s<o;++s){a=t[s];const{fullSize:l}=a.box,u=n[a.stack],d=u&&a.stackWeight/u.weight;a.horizontal?(a.width=d?d*r:l&&e.availableWidth,a.height=i):(a.width=r,a.height=d?d*i:l&&e.availableHeight)}return n}function Lw(t){const e=Ow(t),n=ei(e.filter(u=>u.box.fullSize),!0),r=ei(Jr(e,"left"),!0),i=ei(Jr(e,"right")),s=ei(Jr(e,"top"),!0),o=ei(Jr(e,"bottom")),a=vh(e,"x"),l=vh(e,"y");return{fullSize:n,leftAndTop:r.concat(s),rightAndBottom:i.concat(l).concat(o).concat(a),chartArea:Jr(e,"chartArea"),vertical:r.concat(i).concat(l),horizontal:s.concat(o).concat(a)}}function yh(t,e,n,r){return Math.max(t[n],e[n])+Math.max(t[r],e[r])}function lg(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function Fw(t,e,n,r){const{pos:i,box:s}=n,o=t.maxPadding;if(!H(i)){n.size&&(t[i]-=n.size);const h=r[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?s.height:s.width),n.size=h.size/h.count,t[i]+=n.size}s.getPadding&&lg(o,s.getPadding());const a=Math.max(0,e.outerWidth-yh(o,t,"left","right")),l=Math.max(0,e.outerHeight-yh(o,t,"top","bottom")),u=a!==t.w,d=l!==t.h;return t.w=a,t.h=l,n.horizontal?{same:u,other:d}:{same:d,other:u}}function zw(t){const e=t.maxPadding;function n(r){const i=Math.max(e[r]-t[r],0);return t[r]+=i,i}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function Aw(t,e){const n=e.maxPadding;function r(i){const s={left:0,top:0,right:0,bottom:0};return i.forEach(o=>{s[o]=Math.max(e[o],n[o])}),s}return r(t?["left","right"]:["top","bottom"])}function ci(t,e,n,r){const i=[];let s,o,a,l,u,d;for(s=0,o=t.length,u=0;s<o;++s){a=t[s],l=a.box,l.update(a.width||e.w,a.height||e.h,Aw(a.horizontal,e));const{same:h,other:f}=Fw(e,n,a,r);u|=h&&i.length,d=d||f,l.fullSize||i.push(a)}return u&&ci(i,e,n,r)||d}function Ms(t,e,n,r,i){t.top=n,t.left=e,t.right=e+r,t.bottom=n+i,t.width=r,t.height=i}function bh(t,e,n,r){const i=n.padding;let{x:s,y:o}=e;for(const a of t){const l=a.box,u=r[a.stack]||{placed:0,weight:1},d=a.stackWeight/u.weight||1;if(a.horizontal){const h=e.w*d,f=u.size||l.height;Vi(u.start)&&(o=u.start),l.fullSize?Ms(l,i.left,o,n.outerWidth-i.right-i.left,f):Ms(l,e.left+u.placed,o,h,f),u.start=o,u.placed+=h,o=l.bottom}else{const h=e.h*d,f=u.size||l.width;Vi(u.start)&&(s=u.start),l.fullSize?Ms(l,s,i.top,f,n.outerHeight-i.bottom-i.top):Ms(l,s,e.top+u.placed,f,h),u.start=s,u.placed+=h,s=l.right}}e.x=s,e.y=o}var st={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,r){if(!t)return;const i=ut(t.options.layout.padding),s=Math.max(e-i.width,0),o=Math.max(n-i.height,0),a=Lw(t.boxes),l=a.vertical,u=a.horizontal;Y(t.boxes,x=>{typeof x.beforeLayout=="function"&&x.beforeLayout()});const d=l.reduce((x,b)=>b.box.options&&b.box.options.display===!1?x:x+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:i,availableWidth:s,availableHeight:o,vBoxMaxWidth:s/2/d,hBoxMaxHeight:o/2}),f=Object.assign({},i);lg(f,ut(r));const m=Object.assign({maxPadding:f,w:s,h:o,x:i.left,y:i.top},i),v=$w(l.concat(u),h);ci(a.fullSize,m,h,v),ci(l,m,h,v),ci(u,m,h,v)&&ci(l,m,h,v),zw(m),bh(a.leftAndTop,m,h,v),m.x+=m.w,m.y+=m.h,bh(a.rightAndBottom,m,h,v),t.chartArea={left:m.left,top:m.top,right:m.left+m.w,bottom:m.top+m.h,height:m.h,width:m.w},Y(a.chartArea,x=>{const b=x.box;Object.assign(b,t.chartArea),b.update(m.w,m.h,{left:0,top:0,right:0,bottom:0})})}};class cg{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,r){}removeEventListener(e,n,r){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,r,i){return n=Math.max(0,n||e.width),r=r||e.height,{width:n,height:Math.max(0,i?Math.floor(n/i):r)}}isAttached(e){return!0}updateConfig(e){}}class Iw extends cg{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Gs="$chartjs",Uw={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},wh=t=>t===null||t==="";function Hw(t,e){const n=t.style,r=t.getAttribute("height"),i=t.getAttribute("width");if(t[Gs]={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",wh(i)){const s=ah(t,"width");s!==void 0&&(t.width=s)}if(wh(r))if(t.style.height==="")t.height=t.width/(e||2);else{const s=ah(t,"height");s!==void 0&&(t.height=s)}return t}const ug=qb?{passive:!0}:!1;function Ww(t,e,n){t&&t.addEventListener(e,n,ug)}function Bw(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,ug)}function Vw(t,e){const n=Uw[t.type]||t.type,{x:r,y:i}=$n(t,e);return{type:n,chart:e,native:t,x:r!==void 0?r:null,y:i!==void 0?i:null}}function Fo(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function Yw(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let o=!1;for(const a of s)o=o||Fo(a.addedNodes,r),o=o&&!Fo(a.removedNodes,r);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function Xw(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let o=!1;for(const a of s)o=o||Fo(a.removedNodes,r),o=o&&!Fo(a.addedNodes,r);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const Xi=new Map;let Sh=0;function dg(){const t=window.devicePixelRatio;t!==Sh&&(Sh=t,Xi.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function Qw(t,e){Xi.size||window.addEventListener("resize",dg),Xi.set(t,e)}function Kw(t){Xi.delete(t),Xi.size||window.removeEventListener("resize",dg)}function Gw(t,e,n){const r=t.canvas,i=r&&mu(r);if(!i)return;const s=Yp((a,l)=>{const u=i.clientWidth;n(a,l),u<i.clientWidth&&n()},window),o=new ResizeObserver(a=>{const l=a[0],u=l.contentRect.width,d=l.contentRect.height;u===0&&d===0||s(u,d)});return o.observe(i),Qw(t,s),o}function Ga(t,e,n){n&&n.disconnect(),e==="resize"&&Kw(t)}function qw(t,e,n){const r=t.canvas,i=Yp(s=>{t.ctx!==null&&n(Vw(s,t))},t);return Ww(r,e,i),i}class Zw extends cg{acquireContext(e,n){const r=e&&e.getContext&&e.getContext("2d");return r&&r.canvas===e?(Hw(e,n),r):null}releaseContext(e){const n=e.canvas;if(!n[Gs])return!1;const r=n[Gs].initial;["height","width"].forEach(s=>{const o=r[s];Q(o)?n.removeAttribute(s):n.setAttribute(s,o)});const i=r.style||{};return Object.keys(i).forEach(s=>{n.style[s]=i[s]}),n.width=n.width,delete n[Gs],!0}addEventListener(e,n,r){this.removeEventListener(e,n);const i=e.$proxies||(e.$proxies={}),o={attach:Yw,detach:Xw,resize:Gw}[n]||qw;i[n]=o(e,n,r)}removeEventListener(e,n){const r=e.$proxies||(e.$proxies={}),i=r[n];if(!i)return;({attach:Ga,detach:Ga,resize:Ga}[n]||Bw)(e,n,i),r[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,r,i){return Gb(e,n,r,i)}isAttached(e){const n=e&&mu(e);return!!(n&&n.isConnected)}}function Jw(t){return!fu()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?Iw:Zw}class Yt{constructor(){L(this,"x");L(this,"y");L(this,"active",!1);L(this,"options");L(this,"$animations")}tooltipPosition(e){const{x:n,y:r}=this.getProps(["x","y"],e);return{x:n,y:r}}hasValue(){return Oo(this.x)&&Oo(this.y)}getProps(e,n){const r=this.$animations;if(!n||!r)return this;const i={};return e.forEach(s=>{i[s]=r[s]&&r[s].active()?r[s]._to:this[s]}),i}}L(Yt,"defaults",{}),L(Yt,"defaultRoutes");function e2(t,e){const n=t.options.ticks,r=t2(t),i=Math.min(n.maxTicksLimit||r,r),s=n.major.enabled?r2(e):[],o=s.length,a=s[0],l=s[o-1],u=[];if(o>i)return i2(e,u,s,o/i),u;const d=n2(s,e,i);if(o>0){let h,f;const m=o>1?Math.round((l-a)/(o-1)):null;for(Ps(e,u,d,Q(m)?0:a-m,a),h=0,f=o-1;h<f;h++)Ps(e,u,d,s[h],s[h+1]);return Ps(e,u,d,l,Q(m)?e.length:l+m),u}return Ps(e,u,d),u}function t2(t){const e=t.options.offset,n=t._tickSize(),r=t._length/n+(e?0:1),i=t._maxLength/n;return Math.floor(Math.min(r,i))}function n2(t,e,n){const r=s2(t),i=e.length/n;if(!r)return Math.max(i,1);const s=ob(r);for(let o=0,a=s.length-1;o<a;o++){const l=s[o];if(l>i)return l}return Math.max(i,1)}function r2(t){const e=[];let n,r;for(n=0,r=t.length;n<r;n++)t[n].major&&e.push(n);return e}function i2(t,e,n,r){let i=0,s=n[0],o;for(r=Math.ceil(r),o=0;o<t.length;o++)o===s&&(e.push(t[o]),i++,s=n[i*r])}function Ps(t,e,n,r,i){const s=W(r,0),o=Math.min(W(i,t.length),t.length);let a=0,l,u,d;for(n=Math.ceil(n),i&&(l=i-r,n=l/Math.floor(l/n)),d=s;d<0;)a++,d=Math.round(s+a*n);for(u=Math.max(s,0);u<o;u++)u===d&&(e.push(t[u]),a++,d=Math.round(s+a*n))}function s2(t){const e=t.length;let n,r;if(e<2)return!1;for(r=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==r)return!1;return r}const o2=t=>t==="left"?"right":t==="right"?"left":t,_h=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,Nh=(t,e)=>Math.min(e||t,t);function jh(t,e){const n=[],r=t.length/e,i=t.length;let s=0;for(;s<i;s+=r)n.push(t[Math.floor(s)]);return n}function a2(t,e,n){const r=t.ticks.length,i=Math.min(e,r-1),s=t._startPixel,o=t._endPixel,a=1e-6;let l=t.getPixelForTick(i),u;if(!(n&&(r===1?u=Math.max(l-s,o-l):e===0?u=(t.getPixelForTick(1)-l)/2:u=(l-t.getPixelForTick(i-1))/2,l+=i<e?u:-u,l<s-a||l>o+a)))return l}function l2(t,e){Y(t,n=>{const r=n.gc,i=r.length/2;let s;if(i>e){for(s=0;s<i;++s)delete n.data[r[s]];r.splice(0,i)}})}function ti(t){return t.drawTicks?t.tickLength:0}function kh(t,e){if(!t.display)return 0;const n=Pe(t.font,e),r=ut(t.padding);return(pe(t.text)?t.text.length:1)*n.lineHeight+r.height}function c2(t,e){return Ir(t,{scale:e,type:"scale"})}function u2(t,e,n){return Ir(t,{tick:n,index:e,type:"tick"})}function d2(t,e,n){let r=su(t);return(n&&e!=="right"||!n&&e==="right")&&(r=o2(r)),r}function h2(t,e,n,r){const{top:i,left:s,bottom:o,right:a,chart:l}=t,{chartArea:u,scales:d}=l;let h=0,f,m,v;const x=o-i,b=a-s;if(t.isHorizontal()){if(m=ke(r,s,a),H(n)){const g=Object.keys(n)[0],p=n[g];v=d[g].getPixelForValue(p)+x-e}else n==="center"?v=(u.bottom+u.top)/2+x-e:v=_h(t,n,e);f=a-s}else{if(H(n)){const g=Object.keys(n)[0],p=n[g];m=d[g].getPixelForValue(p)-b+e}else n==="center"?m=(u.left+u.right)/2-b+e:m=_h(t,n,e);v=ke(r,o,i),h=n==="left"?-xe:xe}return{titleX:m,titleY:v,maxWidth:f,rotation:h}}class Ur extends Yt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:r,_suggestedMax:i}=this;return e=St(e,Number.POSITIVE_INFINITY),n=St(n,Number.NEGATIVE_INFINITY),r=St(r,Number.POSITIVE_INFINITY),i=St(i,Number.NEGATIVE_INFINITY),{min:St(e,r),max:St(n,i),minDefined:ct(e),maxDefined:ct(n)}}getMinMax(e){let{min:n,max:r,minDefined:i,maxDefined:s}=this.getUserBounds(),o;if(i&&s)return{min:n,max:r};const a=this.getMatchingVisibleMetas();for(let l=0,u=a.length;l<u;++l)o=a[l].controller.getMinMax(this,e),i||(n=Math.min(n,o.min)),s||(r=Math.max(r,o.max));return n=s&&n>r?r:n,r=i&&n>r?n:r,{min:St(n,St(r,n)),max:St(r,St(n,r))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){ee(this.options.beforeUpdate,[this])}update(e,n,r){const{beginAtZero:i,grace:s,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=r=Object.assign({left:0,right:0,top:0,bottom:0},r),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+r.left+r.right:this.height+r.top+r.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=$b(this,s,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?jh(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=e2(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,r;this.isHorizontal()?(n=this.left,r=this.right):(n=this.top,r=this.bottom,e=!e),this._startPixel=n,this._endPixel=r,this._reversePixels=e,this._length=r-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){ee(this.options.afterUpdate,[this])}beforeSetDimensions(){ee(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){ee(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),ee(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){ee(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let r,i,s;for(r=0,i=e.length;r<i;r++)s=e[r],s.label=ee(n.callback,[s.value,r,e],this)}afterTickToLabelConversion(){ee(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){ee(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,r=Nh(this.ticks.length,e.ticks.maxTicksLimit),i=n.minRotation||0,s=n.maxRotation;let o=i,a,l,u;if(!this._isVisible()||!n.display||i>=s||r<=1||!this.isHorizontal()){this.labelRotation=i;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,m=Be(this.chart.width-h,0,this.maxWidth);a=e.offset?this.maxWidth/r:m/(r-1),h+6>a&&(a=m/(r-(e.offset?.5:1)),l=this.maxHeight-ti(e.grid)-n.padding-kh(e.title,this.chart.options.font),u=Math.sqrt(h*h+f*f),o=ub(Math.min(Math.asin(Be((d.highest.height+6)/a,-1,1)),Math.asin(Be(l/u,-1,1))-Math.asin(Be(f/u,-1,1)))),o=Math.max(i,Math.min(s,o))),this.labelRotation=o}afterCalculateLabelRotation(){ee(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){ee(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:r,title:i,grid:s}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){const l=kh(i,n.options.font);if(a?(e.width=this.maxWidth,e.height=ti(s)+l):(e.height=this.maxHeight,e.width=ti(s)+l),r.display&&this.ticks.length){const{first:u,last:d,widest:h,highest:f}=this._getLabelSizes(),m=r.padding*2,v=It(this.labelRotation),x=Math.cos(v),b=Math.sin(v);if(a){const g=r.mirror?0:b*h.width+x*f.height;e.height=Math.min(this.maxHeight,e.height+g+m)}else{const g=r.mirror?0:x*h.width+b*f.height;e.width=Math.min(this.maxWidth,e.width+g+m)}this._calculatePadding(u,d,b,x)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,r,i){const{ticks:{align:s,padding:o},position:a}=this.options,l=this.labelRotation!==0,u=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,m=0;l?u?(f=i*e.width,m=r*n.height):(f=r*e.height,m=i*n.width):s==="start"?m=n.width:s==="end"?f=e.width:s!=="inner"&&(f=e.width/2,m=n.width/2),this.paddingLeft=Math.max((f-d+o)*this.width/(this.width-d),0),this.paddingRight=Math.max((m-h+o)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;s==="start"?(d=0,h=e.height):s==="end"&&(d=n.height,h=0),this.paddingTop=d+o,this.paddingBottom=h+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){ee(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,r;for(n=0,r=e.length;n<r;n++)Q(e[n].label)&&(e.splice(n,1),r--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let r=this.ticks;n<r.length&&(r=jh(r,n)),this._labelSizes=e=this._computeLabelSizes(r,r.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,r){const{ctx:i,_longestTextCache:s}=this,o=[],a=[],l=Math.floor(n/Nh(n,r));let u=0,d=0,h,f,m,v,x,b,g,p,y,w,_;for(h=0;h<n;h+=l){if(v=e[h].label,x=this._resolveTickFontOptions(h),i.font=b=x.string,g=s[b]=s[b]||{data:{},gc:[]},p=x.lineHeight,y=w=0,!Q(v)&&!pe(v))y=th(i,g.data,g.gc,y,v),w=p;else if(pe(v))for(f=0,m=v.length;f<m;++f)_=v[f],!Q(_)&&!pe(_)&&(y=th(i,g.data,g.gc,y,_),w+=p);o.push(y),a.push(w),u=Math.max(y,u),d=Math.max(w,d)}l2(s,n);const S=o.indexOf(u),N=a.indexOf(d),k=T=>({width:o[T]||0,height:a[T]||0});return{first:k(0),last:k(n-1),widest:k(S),highest:k(N),widths:o,heights:a}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return hb(this._alignToPixels?Pn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const r=n[e];return r.$context||(r.$context=u2(this.getContext(),e,r))}return this.$context||(this.$context=c2(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=It(this.labelRotation),r=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),s=this._getLabelSizes(),o=e.autoSkipPadding||0,a=s?s.widest.width+o:0,l=s?s.highest.height+o:0;return this.isHorizontal()?l*r>a*i?a/r:l/i:l*i<a*r?l/r:a/i}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,r=this.chart,i=this.options,{grid:s,position:o,border:a}=i,l=s.offset,u=this.isHorizontal(),h=this.ticks.length+(l?1:0),f=ti(s),m=[],v=a.setContext(this.getContext()),x=v.display?v.width:0,b=x/2,g=function(A){return Pn(r,A,x)};let p,y,w,_,S,N,k,T,M,E,$,I;if(o==="top")p=g(this.bottom),N=this.bottom-f,T=p-b,E=g(e.top)+b,I=e.bottom;else if(o==="bottom")p=g(this.top),E=e.top,I=g(e.bottom)-b,N=p+b,T=this.top+f;else if(o==="left")p=g(this.right),S=this.right-f,k=p-b,M=g(e.left)+b,$=e.right;else if(o==="right")p=g(this.left),M=e.left,$=g(e.right)-b,S=p+b,k=this.left+f;else if(n==="x"){if(o==="center")p=g((e.top+e.bottom)/2+.5);else if(H(o)){const A=Object.keys(o)[0],B=o[A];p=g(this.chart.scales[A].getPixelForValue(B))}E=e.top,I=e.bottom,N=p+b,T=N+f}else if(n==="y"){if(o==="center")p=g((e.left+e.right)/2);else if(H(o)){const A=Object.keys(o)[0],B=o[A];p=g(this.chart.scales[A].getPixelForValue(B))}S=p-b,k=S-f,M=e.left,$=e.right}const fe=W(i.ticks.maxTicksLimit,h),R=Math.max(1,Math.ceil(h/fe));for(y=0;y<h;y+=R){const A=this.getContext(y),B=s.setContext(A),P=a.setContext(A),D=B.lineWidth,F=B.color,K=P.dash||[],Z=P.dashOffset,bt=B.tickWidth,Te=B.tickColor,Et=B.tickBorderDash||[],Re=B.tickBorderDashOffset;w=a2(this,y,l),w!==void 0&&(_=Pn(r,w,D),u?S=k=M=$=_:N=T=E=I=_,m.push({tx1:S,ty1:N,tx2:k,ty2:T,x1:M,y1:E,x2:$,y2:I,width:D,color:F,borderDash:K,borderDashOffset:Z,tickWidth:bt,tickColor:Te,tickBorderDash:Et,tickBorderDashOffset:Re}))}return this._ticksLength=h,this._borderValue=p,m}_computeLabelItems(e){const n=this.axis,r=this.options,{position:i,ticks:s}=r,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:u,padding:d,mirror:h}=s,f=ti(r.grid),m=f+d,v=h?-d:m,x=-It(this.labelRotation),b=[];let g,p,y,w,_,S,N,k,T,M,E,$,I="middle";if(i==="top")S=this.bottom-v,N=this._getXAxisLabelAlignment();else if(i==="bottom")S=this.top+v,N=this._getXAxisLabelAlignment();else if(i==="left"){const R=this._getYAxisLabelAlignment(f);N=R.textAlign,_=R.x}else if(i==="right"){const R=this._getYAxisLabelAlignment(f);N=R.textAlign,_=R.x}else if(n==="x"){if(i==="center")S=(e.top+e.bottom)/2+m;else if(H(i)){const R=Object.keys(i)[0],A=i[R];S=this.chart.scales[R].getPixelForValue(A)+m}N=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")_=(e.left+e.right)/2-m;else if(H(i)){const R=Object.keys(i)[0],A=i[R];_=this.chart.scales[R].getPixelForValue(A)}N=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?I="top":l==="end"&&(I="bottom"));const fe=this._getLabelSizes();for(g=0,p=a.length;g<p;++g){y=a[g],w=y.label;const R=s.setContext(this.getContext(g));k=this.getPixelForTick(g)+s.labelOffset,T=this._resolveTickFontOptions(g),M=T.lineHeight,E=pe(w)?w.length:1;const A=E/2,B=R.color,P=R.textStrokeColor,D=R.textStrokeWidth;let F=N;o?(_=k,N==="inner"&&(g===p-1?F=this.options.reverse?"left":"right":g===0?F=this.options.reverse?"right":"left":F="center"),i==="top"?u==="near"||x!==0?$=-E*M+M/2:u==="center"?$=-fe.highest.height/2-A*M+M:$=-fe.highest.height+M/2:u==="near"||x!==0?$=M/2:u==="center"?$=fe.highest.height/2-A*M:$=fe.highest.height-E*M,h&&($*=-1),x!==0&&!R.showLabelBackdrop&&(_+=M/2*Math.sin(x))):(S=k,$=(1-E)*M/2);let K;if(R.showLabelBackdrop){const Z=ut(R.backdropPadding),bt=fe.heights[g],Te=fe.widths[g];let Et=$-Z.top,Re=0-Z.left;switch(I){case"middle":Et-=bt/2;break;case"bottom":Et-=bt;break}switch(N){case"center":Re-=Te/2;break;case"right":Re-=Te;break;case"inner":g===p-1?Re-=Te:g>0&&(Re-=Te/2);break}K={left:Re,top:Et,width:Te+Z.width,height:bt+Z.height,color:R.backdropColor}}b.push({label:w,font:T,textOffset:$,options:{rotation:x,color:B,strokeColor:P,strokeWidth:D,textAlign:F,textBaseline:I,translation:[_,S],backdrop:K}})}return b}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-It(this.labelRotation))return e==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:r,mirror:i,padding:s}}=this.options,o=this._getLabelSizes(),a=e+s,l=o.widest.width;let u,d;return n==="left"?i?(d=this.right+s,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d+=l)):(d=this.right-a,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d=this.left)):n==="right"?i?(d=this.left+s,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d-=l)):(d=this.left+a,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d=this.right)):u="right",{textAlign:u,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:r,top:i,width:s,height:o}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(r,i,s,o),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(s=>s.value===e);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(e){const n=this.options.grid,r=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let s,o;const a=(l,u,d)=>{!d.width||!d.color||(r.save(),r.lineWidth=d.width,r.strokeStyle=d.color,r.setLineDash(d.borderDash||[]),r.lineDashOffset=d.borderDashOffset,r.beginPath(),r.moveTo(l.x,l.y),r.lineTo(u.x,u.y),r.stroke(),r.restore())};if(n.display)for(s=0,o=i.length;s<o;++s){const l=i[s];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:r,grid:i}}=this,s=r.setContext(this.getContext()),o=r.display?s.width:0;if(!o)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let u,d,h,f;this.isHorizontal()?(u=Pn(e,this.left,o)-o/2,d=Pn(e,this.right,a)+a/2,h=f=l):(h=Pn(e,this.top,o)-o/2,f=Pn(e,this.bottom,a)+a/2,u=d=l),n.save(),n.lineWidth=s.width,n.strokeStyle=s.color,n.beginPath(),n.moveTo(u,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const r=this.ctx,i=this._computeLabelArea();i&&au(r,i);const s=this.getLabelItems(e);for(const o of s){const a=o.options,l=o.font,u=o.label,d=o.textOffset;Yi(r,u,0,d,l,a)}i&&lu(r)}drawTitle(){const{ctx:e,options:{position:n,title:r,reverse:i}}=this;if(!r.display)return;const s=Pe(r.font),o=ut(r.padding),a=r.align;let l=s.lineHeight/2;n==="bottom"||n==="center"||H(n)?(l+=o.bottom,pe(r.text)&&(l+=s.lineHeight*(r.text.length-1))):l+=o.top;const{titleX:u,titleY:d,maxWidth:h,rotation:f}=h2(this,l,n,a);Yi(e,r.text,0,0,s,{color:r.color,maxWidth:h,rotation:f,textAlign:d2(a,n,i),textBaseline:"middle",translation:[u,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,r=W(e.grid&&e.grid.z,-1),i=W(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Ur.prototype.draw?[{z:n,draw:s=>{this.draw(s)}}]:[{z:r,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",i=[];let s,o;for(s=0,o=n.length;s<o;++s){const a=n[s];a[r]===this.id&&(!e||a.type===e)&&i.push(a)}return i}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Pe(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Es{constructor(e,n,r){this.type=e,this.scope=n,this.override=r,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let r;p2(n)&&(r=this.register(n));const i=this.items,s=e.id,o=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+e);return s in i||(i[s]=e,f2(e,o,r),this.override&&ce.override(e.id,e.overrides)),o}get(e){return this.items[e]}unregister(e){const n=this.items,r=e.id,i=this.scope;r in n&&delete n[r],i&&r in ce[i]&&(delete ce[i][r],this.override&&delete Gn[r])}}function f2(t,e,n){const r=Bi(Object.create(null),[n?ce.get(n):{},ce.get(e),t.defaults]);ce.set(e,r),t.defaultRoutes&&m2(e,t.defaultRoutes),t.descriptors&&ce.describe(e,t.descriptors)}function m2(t,e){Object.keys(e).forEach(n=>{const r=n.split("."),i=r.pop(),s=[t].concat(r).join("."),o=e[n].split("."),a=o.pop(),l=o.join(".");ce.route(s,i,l,a)})}function p2(t){return"id"in t&&"defaults"in t}class g2{constructor(){this.controllers=new Es(kr,"datasets",!0),this.elements=new Es(Yt,"elements"),this.plugins=new Es(Object,"plugins"),this.scales=new Es(Ur,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,r){[...n].forEach(i=>{const s=r||this._getRegistryForType(i);r||s.isForType(i)||s===this.plugins&&i.id?this._exec(e,s,i):Y(i,o=>{const a=r||this._getRegistryForType(o);this._exec(e,a,o)})})}_exec(e,n,r){const i=ru(e);ee(r["before"+i],[],r),n[e](r),ee(r["after"+i],[],r)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const r=this._typedRegistries[n];if(r.isForType(e))return r}return this.plugins}_get(e,n,r){const i=n.get(e);if(i===void 0)throw new Error('"'+e+'" is not a registered '+r+".");return i}}var jt=new g2;class x2{constructor(){this._init=[]}notify(e,n,r,i){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const s=i?this._descriptors(e).filter(i):this._descriptors(e),o=this._notify(s,e,n,r);return n==="afterDestroy"&&(this._notify(s,e,"stop"),this._notify(this._init,e,"uninstall")),o}_notify(e,n,r,i){i=i||{};for(const s of e){const o=s.plugin,a=o[r],l=[n,i,s.options];if(ee(a,l,o)===!1&&i.cancelable)return!1}return!0}invalidate(){Q(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const r=e&&e.config,i=W(r.options&&r.options.plugins,{}),s=v2(r);return i===!1&&!n?[]:b2(e,s,i,n)}_notifyStateChanges(e){const n=this._oldCache||[],r=this._cache,i=(s,o)=>s.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,r),e,"stop"),this._notify(i(r,n),e,"start")}}function v2(t){const e={},n=[],r=Object.keys(jt.plugins.items);for(let s=0;s<r.length;s++)n.push(jt.getPlugin(r[s]));const i=t.plugins||[];for(let s=0;s<i.length;s++){const o=i[s];n.indexOf(o)===-1&&(n.push(o),e[o.id]=!0)}return{plugins:n,localIds:e}}function y2(t,e){return!e&&t===!1?null:t===!0?{}:t}function b2(t,{plugins:e,localIds:n},r,i){const s=[],o=t.getContext();for(const a of e){const l=a.id,u=y2(r[l],i);u!==null&&s.push({plugin:a,options:w2(t.config,{plugin:a,local:n[l]},u,o)})}return s}function w2(t,{plugin:e,local:n},r,i){const s=t.pluginScopeKeys(e),o=t.getOptionScopes(r,s);return n&&e.defaults&&o.push(e.defaults),t.createResolver(o,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ec(t,e){const n=ce.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function S2(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function _2(t,e){return t===e?"_index_":"_value_"}function Ch(t){if(t==="x"||t==="y"||t==="r")return t}function N2(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function tc(t,...e){if(Ch(t))return t;for(const n of e){const r=n.axis||N2(n.position)||t.length>1&&Ch(t[0].toLowerCase());if(r)return r}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Mh(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function j2(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(r=>r.xAxisID===t||r.yAxisID===t);if(n.length)return Mh(t,"x",n[0])||Mh(t,"y",n[0])}return{}}function k2(t,e){const n=Gn[t.type]||{scales:{}},r=e.scales||{},i=ec(t.type,e),s=Object.create(null);return Object.keys(r).forEach(o=>{const a=r[o];if(!H(a))return console.error(`Invalid scale configuration for scale: ${o}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const l=tc(o,a,j2(o,t),ce.scales[a.type]),u=_2(l,i),d=n.scales||{};s[o]=wi(Object.create(null),[{axis:l},a,d[l],d[u]])}),t.data.datasets.forEach(o=>{const a=o.type||t.type,l=o.indexAxis||ec(a,e),d=(Gn[a]||{}).scales||{};Object.keys(d).forEach(h=>{const f=S2(h,l),m=o[f+"AxisID"]||f;s[m]=s[m]||Object.create(null),wi(s[m],[{axis:f},r[m],d[h]])})}),Object.keys(s).forEach(o=>{const a=s[o];wi(a,[ce.scales[a.type],ce.scale])}),s}function hg(t){const e=t.options||(t.options={});e.plugins=W(e.plugins,{}),e.scales=k2(t,e)}function fg(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function C2(t){return t=t||{},t.data=fg(t.data),hg(t),t}const Ph=new Map,mg=new Set;function Ts(t,e){let n=Ph.get(t);return n||(n=e(),Ph.set(t,n),mg.add(n)),n}const ni=(t,e,n)=>{const r=Kn(e,n);r!==void 0&&t.add(r)};class M2{constructor(e){this._config=C2(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=fg(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),hg(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Ts(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return Ts(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return Ts(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,r=this.type;return Ts(`${r}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const r=this._scopeCache;let i=r.get(e);return(!i||n)&&(i=new Map,r.set(e,i)),i}getOptionScopes(e,n,r){const{options:i,type:s}=this,o=this._cachedScopes(e,r),a=o.get(n);if(a)return a;const l=new Set;n.forEach(d=>{e&&(l.add(e),d.forEach(h=>ni(l,e,h))),d.forEach(h=>ni(l,i,h)),d.forEach(h=>ni(l,Gn[s]||{},h)),d.forEach(h=>ni(l,ce,h)),d.forEach(h=>ni(l,Jl,h))});const u=Array.from(l);return u.length===0&&u.push(Object.create(null)),mg.has(n)&&o.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,Gn[n]||{},ce.datasets[n]||{},{type:n},ce,Jl]}resolveNamedOptions(e,n,r,i=[""]){const s={$shared:!0},{resolver:o,subPrefixes:a}=Eh(this._resolverCache,e,i);let l=o;if(E2(o,n)){s.$shared=!1,r=Sn(r)?r():r;const u=this.createResolver(e,r,a);l=$r(o,r,u)}for(const u of n)s[u]=l[u];return s}createResolver(e,n,r=[""],i){const{resolver:s}=Eh(this._resolverCache,e,r);return H(n)?$r(s,n,void 0,i):s}}function Eh(t,e,n){let r=t.get(e);r||(r=new Map,t.set(e,r));const i=n.join();let s=r.get(i);return s||(s={resolver:uu(e,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},r.set(i,s)),s}const P2=t=>H(t)&&Object.getOwnPropertyNames(t).some(e=>Sn(t[e]));function E2(t,e){const{isScriptable:n,isIndexable:r}=Zp(t);for(const i of e){const s=n(i),o=r(i),a=(o||s)&&t[i];if(s&&(Sn(a)||P2(a))||o&&pe(a))return!0}return!1}var T2="4.4.9";const R2=["top","bottom","left","right","chartArea"];function Th(t,e){return t==="top"||t==="bottom"||R2.indexOf(t)===-1&&e==="x"}function Rh(t,e){return function(n,r){return n[t]===r[t]?n[e]-r[e]:n[t]-r[t]}}function Oh(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),ee(n&&n.onComplete,[t],e)}function O2(t){const e=t.chart,n=e.options.animation;ee(n&&n.onProgress,[t],e)}function pg(t){return fu()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const qs={},Dh=t=>{const e=pg(t);return Object.values(qs).filter(n=>n.canvas===e).pop()};function D2(t,e,n){const r=Object.keys(t);for(const i of r){const s=+i;if(s>=e){const o=t[i];delete t[i],(n>0||s>e)&&(t[s+n]=o)}}}function $2(t,e,n,r){return!n||t.type==="mouseout"?null:r?e:t}var Gt;let ns=(Gt=class{static register(...e){jt.add(...e),$h()}static unregister(...e){jt.remove(...e),$h()}constructor(e,n){const r=this.config=new M2(n),i=pg(e),s=Dh(i);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const o=r.createResolver(r.chartOptionScopes(),this.getContext());this.platform=new(r.platform||Jw(i)),this.platform.updateConfig(r);const a=this.platform.acquireContext(i,o.aspectRatio),l=a&&a.canvas,u=l&&l.height,d=l&&l.width;if(this.id=Z1(),this.ctx=a,this.canvas=l,this.width=d,this.height=u,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new x2,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=gb(h=>this.update(h),o.resizeDelay||0),this._dataChanges=[],qs[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Rt.listen(this,"complete",Oh),Rt.listen(this,"progress",O2),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:r,height:i,_aspectRatio:s}=this;return Q(e)?n&&s?s:i?r/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return jt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():oh(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return nh(this.canvas,this.ctx),this}stop(){return Rt.stop(this),this}resize(e,n){Rt.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const r=this.options,i=this.canvas,s=r.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(i,e,n,s),a=r.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,oh(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),ee(r.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};Y(n,(r,i)=>{r.id=i})}buildOrUpdateScales(){const e=this.options,n=e.scales,r=this.scales,i=Object.keys(r).reduce((o,a)=>(o[a]=!1,o),{});let s=[];n&&(s=s.concat(Object.keys(n).map(o=>{const a=n[o],l=tc(o,a),u=l==="r",d=l==="x";return{options:a,dposition:u?"chartArea":d?"bottom":"left",dtype:u?"radialLinear":d?"category":"linear"}}))),Y(s,o=>{const a=o.options,l=a.id,u=tc(l,a),d=W(a.type,o.dtype);(a.position===void 0||Th(a.position,u)!==Th(o.dposition))&&(a.position=o.dposition),i[l]=!0;let h=null;if(l in r&&r[l].type===d)h=r[l];else{const f=jt.getScale(d);h=new f({id:l,type:d,ctx:this.ctx,chart:this}),r[h.id]=h}h.init(a,e)}),Y(i,(o,a)=>{o||delete r[a]}),Y(r,o=>{st.configure(this,o,o.options),st.addBox(this,o)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,r=e.length;if(e.sort((i,s)=>i.index-s.index),r>n){for(let i=n;i<r;++i)this._destroyDatasetMeta(i);e.splice(n,r-n)}this._sortedMetasets=e.slice(0).sort(Rh("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((r,i)=>{n.filter(s=>s===r._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let r,i;for(this._removeUnreferencedMetasets(),r=0,i=n.length;r<i;r++){const s=n[r];let o=this.getDatasetMeta(r);const a=s.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(r),o=this.getDatasetMeta(r)),o.type=a,o.indexAxis=s.indexAxis||ec(a,this.options),o.order=s.order||0,o.index=r,o.label=""+s.label,o.visible=this.isDatasetVisible(r),o.controller)o.controller.updateIndex(r),o.controller.linkScales();else{const l=jt.getController(a),{datasetElementType:u,dataElementType:d}=ce.datasets[a];Object.assign(l,{dataElementType:jt.getElement(d),datasetElementType:u&&jt.getElement(u)}),o.controller=new l(this,r),e.push(o.controller)}}return this._updateMetasets(),e}_resetElements(){Y(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const r=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!r.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let u=0,d=this.data.datasets.length;u<d;u++){const{controller:h}=this.getDatasetMeta(u),f=!i&&s.indexOf(h)===-1;h.buildOrUpdateElements(f),o=Math.max(+h.getMaxOverflow(),o)}o=this._minPadding=r.layout.autoPadding?o:0,this._updateLayout(o),i||Y(s,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Rh("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){Y(this.scales,e=>{st.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),r=new Set(e.events);(!Yd(n,r)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:r,start:i,count:s}of n){const o=r==="_removeElements"?-s:s;D2(e,i,o)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,r=s=>new Set(e.filter(o=>o[0]===s).map((o,a)=>a+","+o.splice(1).join(","))),i=r(0);for(let s=1;s<n;s++)if(!Yd(i,r(s)))return;return Array.from(i).map(s=>s.split(",")).map(s=>({method:s[1],start:+s[2],count:+s[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;st.update(this,this.width,this.height,e);const n=this.chartArea,r=n.width<=0||n.height<=0;this._layers=[],Y(this.boxes,i=>{r&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,s)=>{i._idx=s}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,r=this.data.datasets.length;n<r;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,r=this.data.datasets.length;n<r;++n)this._updateDataset(n,Sn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const r=this.getDatasetMeta(e),i={meta:r,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(r.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Rt.has(this)?this.attached&&!Rt.running(this)&&Rt.start(this):(this.draw(),Oh({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:r,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(r,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,r=[];let i,s;for(i=0,s=n.length;i<s;++i){const o=n[i];(!e||o.visible)&&r.push(o)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,r={meta:e,index:e.index,cancelable:!0},i=tw(this,e);this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(i&&au(n,i),e.controller.draw(),i&&lu(n),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(e){return Gp(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,r,i){const s=Rw.modes[n];return typeof s=="function"?s(this,e,r,i):[]}getDatasetMeta(e){const n=this.data.datasets[e],r=this._metasets;let i=r.filter(s=>s&&s._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},r.push(i)),i}getContext(){return this.$context||(this.$context=Ir(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const r=this.getDatasetMeta(e);return typeof r.hidden=="boolean"?!r.hidden:!n.hidden}setDatasetVisibility(e,n){const r=this.getDatasetMeta(e);r.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,r){const i=r?"show":"hide",s=this.getDatasetMeta(e),o=s.controller._resolveAnimations(void 0,i);Vi(n)?(s.data[n].hidden=!r,this.update()):(this.setDatasetVisibility(e,r),o.update(s,{visible:r}),this.update(a=>a.datasetIndex===e?i:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Rt.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),nh(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete qs[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,r=(s,o)=>{n.addEventListener(this,s,o),e[s]=o},i=(s,o,a)=>{s.offsetX=o,s.offsetY=a,this._eventHandler(s)};Y(this.options.events,s=>r(s,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,r=(l,u)=>{n.addEventListener(this,l,u),e[l]=u},i=(l,u)=>{e[l]&&(n.removeEventListener(this,l,u),delete e[l])},s=(l,u)=>{this.canvas&&this.resize(l,u)};let o;const a=()=>{i("attach",a),this.attached=!0,this.resize(),r("resize",s),r("detach",o)};o=()=>{this.attached=!1,i("resize",s),this._stop(),this._resize(0,0),r("attach",a)},n.isAttached(this.canvas)?a():o()}unbindEvents(){Y(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},Y(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,r){const i=r?"set":"remove";let s,o,a,l;for(n==="dataset"&&(s=this.getDatasetMeta(e[0].datasetIndex),s.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=e.length;a<l;++a){o=e[a];const u=o&&this.getDatasetMeta(o.datasetIndex).controller;u&&u[i+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],r=e.map(({datasetIndex:s,index:o})=>{const a=this.getDatasetMeta(s);if(!a)throw new Error("No dataset found at index "+s);return{datasetIndex:s,element:a.data[o],index:o}});!Eo(r,n)&&(this._active=r,this._lastEvent=null,this._updateHoverStyles(r,n))}notifyPlugins(e,n,r){return this._plugins.notify(this,e,n,r)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,r){const i=this.options.hover,s=(l,u)=>l.filter(d=>!u.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),o=s(n,e),a=r?e:s(e,n);o.length&&this.updateHoverStyle(o,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(e,n){const r={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},i=o=>(o.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",r,i)===!1)return;const s=this._handleEvent(e,n,r.inChartArea);return r.cancelable=!1,this.notifyPlugins("afterEvent",r,i),(s||r.changed)&&this.render(),this}_handleEvent(e,n,r){const{_active:i=[],options:s}=this,o=n,a=this._getActiveElements(e,i,r,o),l=ib(e),u=$2(e,this._lastEvent,r,l);r&&(this._lastEvent=null,ee(s.onHover,[e,a,this],this),l&&ee(s.onClick,[e,a,this],this));const d=!Eo(a,i);return(d||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=u,d}_getActiveElements(e,n,r,i){if(e.type==="mouseout")return[];if(!r)return n;const s=this.options.hover;return this.getElementsAtEventForMode(e,s.mode,s,i)}},L(Gt,"defaults",ce),L(Gt,"instances",qs),L(Gt,"overrides",Gn),L(Gt,"registry",jt),L(Gt,"version",T2),L(Gt,"getChart",Dh),Gt);function $h(){return Y(ns.instances,t=>t._plugins.invalidate())}function L2(t,e,n){const{startAngle:r,pixelMargin:i,x:s,y:o,outerRadius:a,innerRadius:l}=e;let u=i/a;t.beginPath(),t.arc(s,o,a,r-u,n+u),l>i?(u=i/l,t.arc(s,o,l,n+u,r-u,!0)):t.arc(s,o,i,n+xe,r-xe),t.closePath(),t.clip()}function F2(t){return cu(t,["outerStart","outerEnd","innerStart","innerEnd"])}function z2(t,e,n,r){const i=F2(t.options.borderRadius),s=(n-e)/2,o=Math.min(s,r*e/2),a=l=>{const u=(n-Math.min(s,l))*r/2;return Be(l,0,Math.min(s,u))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:Be(i.innerStart,0,o),innerEnd:Be(i.innerEnd,0,o)}}function rr(t,e,n,r){return{x:n+t*Math.cos(e),y:r+t*Math.sin(e)}}function zo(t,e,n,r,i,s){const{x:o,y:a,startAngle:l,pixelMargin:u,innerRadius:d}=e,h=Math.max(e.outerRadius+r+n-u,0),f=d>0?d+r+n+u:0;let m=0;const v=i-l;if(r){const R=d>0?d-r:0,A=h>0?h-r:0,B=(R+A)/2,P=B!==0?v*B/(B+r):v;m=(v-P)/2}const x=Math.max(.001,v*h-n/he)/h,b=(v-x)/2,g=l+b+m,p=i-b-m,{outerStart:y,outerEnd:w,innerStart:_,innerEnd:S}=z2(e,f,h,p-g),N=h-y,k=h-w,T=g+y/N,M=p-w/k,E=f+_,$=f+S,I=g+_/E,fe=p-S/$;if(t.beginPath(),s){const R=(T+M)/2;if(t.arc(o,a,h,T,R),t.arc(o,a,h,R,M),w>0){const D=rr(k,M,o,a);t.arc(D.x,D.y,w,M,p+xe)}const A=rr($,p,o,a);if(t.lineTo(A.x,A.y),S>0){const D=rr($,fe,o,a);t.arc(D.x,D.y,S,p+xe,fe+Math.PI)}const B=(p-S/f+(g+_/f))/2;if(t.arc(o,a,f,p-S/f,B,!0),t.arc(o,a,f,B,g+_/f,!0),_>0){const D=rr(E,I,o,a);t.arc(D.x,D.y,_,I+Math.PI,g-xe)}const P=rr(N,g,o,a);if(t.lineTo(P.x,P.y),y>0){const D=rr(N,T,o,a);t.arc(D.x,D.y,y,g-xe,T)}}else{t.moveTo(o,a);const R=Math.cos(T)*h+o,A=Math.sin(T)*h+a;t.lineTo(R,A);const B=Math.cos(M)*h+o,P=Math.sin(M)*h+a;t.lineTo(B,P)}t.closePath()}function A2(t,e,n,r,i){const{fullCircles:s,startAngle:o,circumference:a}=e;let l=e.endAngle;if(s){zo(t,e,n,r,l,i);for(let u=0;u<s;++u)t.fill();isNaN(a)||(l=o+(a%de||de))}return zo(t,e,n,r,l,i),t.fill(),l}function I2(t,e,n,r,i){const{fullCircles:s,startAngle:o,circumference:a,options:l}=e,{borderWidth:u,borderJoinStyle:d,borderDash:h,borderDashOffset:f}=l,m=l.borderAlign==="inner";if(!u)return;t.setLineDash(h||[]),t.lineDashOffset=f,m?(t.lineWidth=u*2,t.lineJoin=d||"round"):(t.lineWidth=u,t.lineJoin=d||"bevel");let v=e.endAngle;if(s){zo(t,e,n,r,v,i);for(let x=0;x<s;++x)t.stroke();isNaN(a)||(v=o+(a%de||de))}m&&L2(t,e,v),s||(zo(t,e,n,r,v,i),t.stroke())}class ui extends Yt{constructor(n){super();L(this,"circumference");L(this,"endAngle");L(this,"fullCircles");L(this,"innerRadius");L(this,"outerRadius");L(this,"pixelMargin");L(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,r,i){const s=this.getProps(["x","y"],i),{angle:o,distance:a}=Hp(s,{x:n,y:r}),{startAngle:l,endAngle:u,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),m=(this.options.spacing+this.options.borderWidth)/2,v=W(f,u-l),x=Do(o,l,u)&&l!==u,b=v>=de||x,g=An(a,d+m,h+m);return b&&g}getCenterPoint(n){const{x:r,y:i,startAngle:s,endAngle:o,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:d}=this.options,h=(s+o)/2,f=(a+l+d+u)/2;return{x:r+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:r,circumference:i}=this,s=(r.offset||0)/4,o=(r.spacing||0)/2,a=r.circular;if(this.pixelMargin=r.borderAlign==="inner"?.33:0,this.fullCircles=i>de?Math.floor(i/de):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*s,Math.sin(l)*s);const u=1-Math.sin(Math.min(he,i||0)),d=s*u;n.fillStyle=r.backgroundColor,n.strokeStyle=r.borderColor,A2(n,this,d,o,a),I2(n,this,d,o,a),n.restore()}}L(ui,"id","arc"),L(ui,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),L(ui,"defaultRoutes",{backgroundColor:"backgroundColor"}),L(ui,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function gg(t,e){const{x:n,y:r,base:i,width:s,height:o}=t.getProps(["x","y","base","width","height"],e);let a,l,u,d,h;return t.horizontal?(h=o/2,a=Math.min(n,i),l=Math.max(n,i),u=r-h,d=r+h):(h=s/2,a=n-h,l=n+h,u=Math.min(r,i),d=Math.max(r,i)),{left:a,top:u,right:l,bottom:d}}function an(t,e,n,r){return t?0:Be(e,n,r)}function U2(t,e,n){const r=t.options.borderWidth,i=t.borderSkipped,s=qp(r);return{t:an(i.top,s.top,0,n),r:an(i.right,s.right,0,e),b:an(i.bottom,s.bottom,0,n),l:an(i.left,s.left,0,e)}}function H2(t,e,n){const{enableBorderRadius:r}=t.getProps(["enableBorderRadius"]),i=t.options.borderRadius,s=Nr(i),o=Math.min(e,n),a=t.borderSkipped,l=r||H(i);return{topLeft:an(!l||a.top||a.left,s.topLeft,0,o),topRight:an(!l||a.top||a.right,s.topRight,0,o),bottomLeft:an(!l||a.bottom||a.left,s.bottomLeft,0,o),bottomRight:an(!l||a.bottom||a.right,s.bottomRight,0,o)}}function W2(t){const e=gg(t),n=e.right-e.left,r=e.bottom-e.top,i=U2(t,n/2,r/2),s=H2(t,n/2,r/2);return{outer:{x:e.left,y:e.top,w:n,h:r,radius:s},inner:{x:e.left+i.l,y:e.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,s.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(i.b,i.r))}}}}function qa(t,e,n,r){const i=e===null,s=n===null,a=t&&!(i&&s)&&gg(t,r);return a&&(i||An(e,a.left,a.right))&&(s||An(n,a.top,a.bottom))}function B2(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function V2(t,e){t.rect(e.x,e.y,e.w,e.h)}function Za(t,e,n={}){const r=t.x!==n.x?-e:0,i=t.y!==n.y?-e:0,s=(t.x+t.w!==n.x+n.w?e:0)-r,o=(t.y+t.h!==n.y+n.h?e:0)-i;return{x:t.x+r,y:t.y+i,w:t.w+s,h:t.h+o,radius:t.radius}}class Zs extends Yt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:r,backgroundColor:i}}=this,{inner:s,outer:o}=W2(this),a=B2(o.radius)?$o:V2;e.save(),(o.w!==s.w||o.h!==s.h)&&(e.beginPath(),a(e,Za(o,n,s)),e.clip(),a(e,Za(s,-n,o)),e.fillStyle=r,e.fill("evenodd")),e.beginPath(),a(e,Za(s,n)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,n,r){return qa(this,e,n,r)}inXRange(e,n){return qa(this,e,null,n)}inYRange(e,n){return qa(this,null,e,n)}getCenterPoint(e){const{x:n,y:r,base:i,horizontal:s}=this.getProps(["x","y","base","horizontal"],e);return{x:s?(n+i)/2:n,y:s?r:(r+i)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}L(Zs,"id","bar"),L(Zs,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),L(Zs,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const Lh=(t,e)=>{let{boxHeight:n=e,boxWidth:r=e}=t;return t.usePointStyle&&(n=Math.min(n,e),r=t.pointStyleWidth||Math.min(r,e)),{boxWidth:r,boxHeight:n,itemHeight:Math.max(e,n)}},Y2=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Fh extends Yt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,r){this.maxWidth=e,this.maxHeight=n,this._margins=r,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=ee(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(r=>e.filter(r,this.chart.data))),e.sort&&(n=n.sort((r,i)=>e.sort(r,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const r=e.labels,i=Pe(r.font),s=i.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Lh(r,s);let u,d;n.font=i.string,this.isHorizontal()?(u=this.maxWidth,d=this._fitRows(o,s,a,l)+10):(d=this.maxHeight,u=this._fitCols(o,i,a,l)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,r,i){const{ctx:s,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.lineWidths=[0],d=i+a;let h=e;s.textAlign="left",s.textBaseline="middle";let f=-1,m=-d;return this.legendItems.forEach((v,x)=>{const b=r+n/2+s.measureText(v.text).width;(x===0||u[u.length-1]+b+2*a>o)&&(h+=d,u[u.length-(x>0?0:1)]=0,m+=d,f++),l[x]={left:0,top:m,row:f,width:b,height:i},u[u.length-1]+=b+a}),h}_fitCols(e,n,r,i){const{ctx:s,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.columnSizes=[],d=o-e;let h=a,f=0,m=0,v=0,x=0;return this.legendItems.forEach((b,g)=>{const{itemWidth:p,itemHeight:y}=X2(r,n,s,b,i);g>0&&m+y+2*a>d&&(h+=f+a,u.push({width:f,height:m}),v+=f+a,x++,f=m=0),l[g]={left:v,top:m,col:x,width:p,height:y},f=Math.max(f,p),m+=y+a}),h+=f,u.push({width:f,height:m}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:r,labels:{padding:i},rtl:s}}=this,o=jr(s,this.left,this.width);if(this.isHorizontal()){let a=0,l=ke(r,this.left+i,this.right-this.lineWidths[a]);for(const u of n)a!==u.row&&(a=u.row,l=ke(r,this.left+i,this.right-this.lineWidths[a])),u.top+=this.top+e+i,u.left=o.leftForLtr(o.x(l),u.width),l+=u.width+i}else{let a=0,l=ke(r,this.top+e+i,this.bottom-this.columnSizes[a].height);for(const u of n)u.col!==a&&(a=u.col,l=ke(r,this.top+e+i,this.bottom-this.columnSizes[a].height)),u.top=l,u.left+=this.left+i,u.left=o.leftForLtr(o.x(u.left),u.width),l+=u.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;au(e,this),this._draw(),lu(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:r,ctx:i}=this,{align:s,labels:o}=e,a=ce.color,l=jr(e.rtl,this.left,this.width),u=Pe(o.font),{padding:d}=o,h=u.size,f=h/2;let m;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=u.string;const{boxWidth:v,boxHeight:x,itemHeight:b}=Lh(o,h),g=function(S,N,k){if(isNaN(v)||v<=0||isNaN(x)||x<0)return;i.save();const T=W(k.lineWidth,1);if(i.fillStyle=W(k.fillStyle,a),i.lineCap=W(k.lineCap,"butt"),i.lineDashOffset=W(k.lineDashOffset,0),i.lineJoin=W(k.lineJoin,"miter"),i.lineWidth=T,i.strokeStyle=W(k.strokeStyle,a),i.setLineDash(W(k.lineDash,[])),o.usePointStyle){const M={radius:x*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:T},E=l.xPlus(S,v/2),$=N+f;Kp(i,M,E,$,o.pointStyleWidth&&v)}else{const M=N+Math.max((h-x)/2,0),E=l.leftForLtr(S,v),$=Nr(k.borderRadius);i.beginPath(),Object.values($).some(I=>I!==0)?$o(i,{x:E,y:M,w:v,h:x,radius:$}):i.rect(E,M,v,x),i.fill(),T!==0&&i.stroke()}i.restore()},p=function(S,N,k){Yi(i,k.text,S,N+b/2,u,{strikethrough:k.hidden,textAlign:l.textAlign(k.textAlign)})},y=this.isHorizontal(),w=this._computeTitleHeight();y?m={x:ke(s,this.left+d,this.right-r[0]),y:this.top+d+w,line:0}:m={x:this.left+d,y:ke(s,this.top+w+d,this.bottom-n[0].height),line:0},ng(this.ctx,e.textDirection);const _=b+d;this.legendItems.forEach((S,N)=>{i.strokeStyle=S.fontColor,i.fillStyle=S.fontColor;const k=i.measureText(S.text).width,T=l.textAlign(S.textAlign||(S.textAlign=o.textAlign)),M=v+f+k;let E=m.x,$=m.y;l.setWidth(this.width),y?N>0&&E+M+d>this.right&&($=m.y+=_,m.line++,E=m.x=ke(s,this.left+d,this.right-r[m.line])):N>0&&$+_>this.bottom&&(E=m.x=E+n[m.line].width+d,m.line++,$=m.y=ke(s,this.top+w+d,this.bottom-n[m.line].height));const I=l.x(E);if(g(I,$,S),E=xb(T,E+v+f,y?E+M:this.right,e.rtl),p(l.x(E),$,S),y)m.x+=M+d;else if(typeof S.text!="string"){const fe=u.lineHeight;m.y+=xg(S,fe)+d}else m.y+=_}),rg(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,r=Pe(n.font),i=ut(n.padding);if(!n.display)return;const s=jr(e.rtl,this.left,this.width),o=this.ctx,a=n.position,l=r.size/2,u=i.top+l;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+u,h=ke(e.align,h,this.right-f);else{const v=this.columnSizes.reduce((x,b)=>Math.max(x,b.height),0);d=u+ke(e.align,this.top,this.bottom-v-e.labels.padding-this._computeTitleHeight())}const m=ke(a,h,h+f);o.textAlign=s.textAlign(su(a)),o.textBaseline="middle",o.strokeStyle=n.color,o.fillStyle=n.color,o.font=r.string,Yi(o,n.text,m,d,r)}_computeTitleHeight(){const e=this.options.title,n=Pe(e.font),r=ut(e.padding);return e.display?n.lineHeight+r.height:0}_getLegendItemAt(e,n){let r,i,s;if(An(e,this.left,this.right)&&An(n,this.top,this.bottom)){for(s=this.legendHitBoxes,r=0;r<s.length;++r)if(i=s[r],An(e,i.left,i.left+i.width)&&An(n,i.top,i.top+i.height))return this.legendItems[r]}return null}handleEvent(e){const n=this.options;if(!G2(e.type,n))return;const r=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const i=this._hoveredItem,s=Y2(i,r);i&&!s&&ee(n.onLeave,[e,i,this],this),this._hoveredItem=r,r&&!s&&ee(n.onHover,[e,r,this],this)}else r&&ee(n.onClick,[e,r,this],this)}}function X2(t,e,n,r,i){const s=Q2(r,t,e,n),o=K2(i,r,e.lineHeight);return{itemWidth:s,itemHeight:o}}function Q2(t,e,n,r){let i=t.text;return i&&typeof i!="string"&&(i=i.reduce((s,o)=>s.length>o.length?s:o)),e+n.size/2+r.measureText(i).width}function K2(t,e,n){let r=t;return typeof e.text!="string"&&(r=xg(e,n)),r}function xg(t,e){const n=t.text?t.text.length:0;return e*n}function G2(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var vg={id:"legend",_element:Fh,start(t,e,n){const r=t.legend=new Fh({ctx:t.ctx,options:n,chart:t});st.configure(t,r,n),st.addBox(t,r)},stop(t){st.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const r=t.legend;st.configure(t,r,n),r.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const r=e.datasetIndex,i=n.chart;i.isDatasetVisible(r)?(i.hide(r),e.hidden=!0):(i.show(r),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:r,textAlign:i,color:s,useBorderRadius:o,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const u=l.controller.getStyle(n?0:void 0),d=ut(u.borderWidth);return{text:e[l.index].label,fillStyle:u.backgroundColor,fontColor:s,hidden:!l.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:u.borderColor,pointStyle:r||u.pointStyle,rotation:u.rotation,textAlign:i||u.textAlign,borderRadius:o&&(a||u.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class yg extends Yt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const r=this.options;if(this.left=0,this.top=0,!r.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const i=pe(r.text)?r.text.length:1;this._padding=ut(r.padding);const s=i*Pe(r.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:r,bottom:i,right:s,options:o}=this,a=o.align;let l=0,u,d,h;return this.isHorizontal()?(d=ke(a,r,s),h=n+e,u=s-r):(o.position==="left"?(d=r+e,h=ke(a,i,n),l=he*-.5):(d=s-e,h=ke(a,n,i),l=he*.5),u=i-n),{titleX:d,titleY:h,maxWidth:u,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const r=Pe(n.font),s=r.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:u}=this._drawArgs(s);Yi(e,n.text,0,0,r,{color:n.color,maxWidth:l,rotation:u,textAlign:su(n.align),textBaseline:"middle",translation:[o,a]})}}function q2(t,e){const n=new yg({ctx:t.ctx,options:e,chart:t});st.configure(t,n,e),st.addBox(t,n),t.titleBlock=n}var Z2={id:"title",_element:yg,start(t,e,n){q2(t,n)},stop(t){const e=t.titleBlock;st.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const r=t.titleBlock;st.configure(t,r,n),r.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const di={average(t){if(!t.length)return!1;let e,n,r=new Set,i=0,s=0;for(e=0,n=t.length;e<n;++e){const a=t[e].element;if(a&&a.hasValue()){const l=a.tooltipPosition();r.add(l.x),i+=l.y,++s}}return s===0||r.size===0?!1:{x:[...r].reduce((a,l)=>a+l)/r.size,y:i/s}},nearest(t,e){if(!t.length)return!1;let n=e.x,r=e.y,i=Number.POSITIVE_INFINITY,s,o,a;for(s=0,o=t.length;s<o;++s){const l=t[s].element;if(l&&l.hasValue()){const u=l.getCenterPoint(),d=db(e,u);d<i&&(i=d,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,r=l.y}return{x:n,y:r}}};function _t(t,e){return e&&(pe(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Ot(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function J2(t,e){const{element:n,datasetIndex:r,index:i}=e,s=t.getDatasetMeta(r).controller,{label:o,value:a}=s.getLabelAndValue(i);return{chart:t,label:o,parsed:s.getParsed(i),raw:t.data.datasets[r].data[i],formattedValue:a,dataset:s.getDataset(),dataIndex:i,datasetIndex:r,element:n}}function zh(t,e){const n=t.chart.ctx,{body:r,footer:i,title:s}=t,{boxWidth:o,boxHeight:a}=e,l=Pe(e.bodyFont),u=Pe(e.titleFont),d=Pe(e.footerFont),h=s.length,f=i.length,m=r.length,v=ut(e.padding);let x=v.height,b=0,g=r.reduce((w,_)=>w+_.before.length+_.lines.length+_.after.length,0);if(g+=t.beforeBody.length+t.afterBody.length,h&&(x+=h*u.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),g){const w=e.displayColors?Math.max(a,l.lineHeight):l.lineHeight;x+=m*w+(g-m)*l.lineHeight+(g-1)*e.bodySpacing}f&&(x+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let p=0;const y=function(w){b=Math.max(b,n.measureText(w).width+p)};return n.save(),n.font=u.string,Y(t.title,y),n.font=l.string,Y(t.beforeBody.concat(t.afterBody),y),p=e.displayColors?o+2+e.boxPadding:0,Y(r,w=>{Y(w.before,y),Y(w.lines,y),Y(w.after,y)}),p=0,n.font=d.string,Y(t.footer,y),n.restore(),b+=v.width,{width:b,height:x}}function eS(t,e){const{y:n,height:r}=e;return n<r/2?"top":n>t.height-r/2?"bottom":"center"}function tS(t,e,n,r){const{x:i,width:s}=r,o=n.caretSize+n.caretPadding;if(t==="left"&&i+s+o>e.width||t==="right"&&i-s-o<0)return!0}function nS(t,e,n,r){const{x:i,width:s}=n,{width:o,chartArea:{left:a,right:l}}=t;let u="center";return r==="center"?u=i<=(a+l)/2?"left":"right":i<=s/2?u="left":i>=o-s/2&&(u="right"),tS(u,t,e,n)&&(u="center"),u}function Ah(t,e,n){const r=n.yAlign||e.yAlign||eS(t,n);return{xAlign:n.xAlign||e.xAlign||nS(t,e,n,r),yAlign:r}}function rS(t,e){let{x:n,width:r}=t;return e==="right"?n-=r:e==="center"&&(n-=r/2),n}function iS(t,e,n){let{y:r,height:i}=t;return e==="top"?r+=n:e==="bottom"?r-=i+n:r-=i/2,r}function Ih(t,e,n,r){const{caretSize:i,caretPadding:s,cornerRadius:o}=t,{xAlign:a,yAlign:l}=n,u=i+s,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:m}=Nr(o);let v=rS(e,a);const x=iS(e,l,u);return l==="center"?a==="left"?v+=u:a==="right"&&(v-=u):a==="left"?v-=Math.max(d,f)+i:a==="right"&&(v+=Math.max(h,m)+i),{x:Be(v,0,r.width-e.width),y:Be(x,0,r.height-e.height)}}function Rs(t,e,n){const r=ut(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-r.right:t.x+r.left}function Uh(t){return _t([],Ot(t))}function sS(t,e,n){return Ir(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function Hh(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const bg={beforeTitle:Tt,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,r=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(r>0&&e.dataIndex<r)return n[e.dataIndex]}return""},afterTitle:Tt,beforeBody:Tt,beforeLabel:Tt,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return Q(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Tt,afterBody:Tt,beforeFooter:Tt,footer:Tt,afterFooter:Tt};function Ie(t,e,n,r){const i=t[e].call(n,r);return typeof i>"u"?bg[e].call(n,r):i}class nc extends Yt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,r=this.options.setContext(this.getContext()),i=r.enabled&&n.options.animation&&r.animations,s=new ig(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=sS(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:r}=n,i=Ie(r,"beforeTitle",this,e),s=Ie(r,"title",this,e),o=Ie(r,"afterTitle",this,e);let a=[];return a=_t(a,Ot(i)),a=_t(a,Ot(s)),a=_t(a,Ot(o)),a}getBeforeBody(e,n){return Uh(Ie(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:r}=n,i=[];return Y(e,s=>{const o={before:[],lines:[],after:[]},a=Hh(r,s);_t(o.before,Ot(Ie(a,"beforeLabel",this,s))),_t(o.lines,Ie(a,"label",this,s)),_t(o.after,Ot(Ie(a,"afterLabel",this,s))),i.push(o)}),i}getAfterBody(e,n){return Uh(Ie(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:r}=n,i=Ie(r,"beforeFooter",this,e),s=Ie(r,"footer",this,e),o=Ie(r,"afterFooter",this,e);let a=[];return a=_t(a,Ot(i)),a=_t(a,Ot(s)),a=_t(a,Ot(o)),a}_createItems(e){const n=this._active,r=this.chart.data,i=[],s=[],o=[];let a=[],l,u;for(l=0,u=n.length;l<u;++l)a.push(J2(this.chart,n[l]));return e.filter&&(a=a.filter((d,h,f)=>e.filter(d,h,f,r))),e.itemSort&&(a=a.sort((d,h)=>e.itemSort(d,h,r))),Y(a,d=>{const h=Hh(e.callbacks,d);i.push(Ie(h,"labelColor",this,d)),s.push(Ie(h,"labelPointStyle",this,d)),o.push(Ie(h,"labelTextColor",this,d))}),this.labelColors=i,this.labelPointStyles=s,this.labelTextColors=o,this.dataPoints=a,a}update(e,n){const r=this.options.setContext(this.getContext()),i=this._active;let s,o=[];if(!i.length)this.opacity!==0&&(s={opacity:0});else{const a=di[r.position].call(this,i,this._eventPosition);o=this._createItems(r),this.title=this.getTitle(o,r),this.beforeBody=this.getBeforeBody(o,r),this.body=this.getBody(o,r),this.afterBody=this.getAfterBody(o,r),this.footer=this.getFooter(o,r);const l=this._size=zh(this,r),u=Object.assign({},a,l),d=Ah(this.chart,r,u),h=Ih(r,u,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,s={opacity:1,x:h.x,y:h.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,s&&this._resolveAnimations().update(this,s),e&&r.external&&r.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,r,i){const s=this.getCaretPosition(e,r,i);n.lineTo(s.x1,s.y1),n.lineTo(s.x2,s.y2),n.lineTo(s.x3,s.y3)}getCaretPosition(e,n,r){const{xAlign:i,yAlign:s}=this,{caretSize:o,cornerRadius:a}=r,{topLeft:l,topRight:u,bottomLeft:d,bottomRight:h}=Nr(a),{x:f,y:m}=e,{width:v,height:x}=n;let b,g,p,y,w,_;return s==="center"?(w=m+x/2,i==="left"?(b=f,g=b-o,y=w+o,_=w-o):(b=f+v,g=b+o,y=w-o,_=w+o),p=b):(i==="left"?g=f+Math.max(l,d)+o:i==="right"?g=f+v-Math.max(u,h)-o:g=this.caretX,s==="top"?(y=m,w=y-o,b=g-o,p=g+o):(y=m+x,w=y+o,b=g+o,p=g-o),_=y),{x1:b,x2:g,x3:p,y1:y,y2:w,y3:_}}drawTitle(e,n,r){const i=this.title,s=i.length;let o,a,l;if(s){const u=jr(r.rtl,this.x,this.width);for(e.x=Rs(this,r.titleAlign,r),n.textAlign=u.textAlign(r.titleAlign),n.textBaseline="middle",o=Pe(r.titleFont),a=r.titleSpacing,n.fillStyle=r.titleColor,n.font=o.string,l=0;l<s;++l)n.fillText(i[l],u.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+a,l+1===s&&(e.y+=r.titleMarginBottom-a)}}_drawColorBox(e,n,r,i,s){const o=this.labelColors[r],a=this.labelPointStyles[r],{boxHeight:l,boxWidth:u}=s,d=Pe(s.bodyFont),h=Rs(this,"left",s),f=i.x(h),m=l<d.lineHeight?(d.lineHeight-l)/2:0,v=n.y+m;if(s.usePointStyle){const x={radius:Math.min(u,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},b=i.leftForLtr(f,u)+u/2,g=v+l/2;e.strokeStyle=s.multiKeyBackground,e.fillStyle=s.multiKeyBackground,rh(e,x,b,g),e.strokeStyle=o.borderColor,e.fillStyle=o.backgroundColor,rh(e,x,b,g)}else{e.lineWidth=H(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,e.strokeStyle=o.borderColor,e.setLineDash(o.borderDash||[]),e.lineDashOffset=o.borderDashOffset||0;const x=i.leftForLtr(f,u),b=i.leftForLtr(i.xPlus(f,1),u-2),g=Nr(o.borderRadius);Object.values(g).some(p=>p!==0)?(e.beginPath(),e.fillStyle=s.multiKeyBackground,$o(e,{x,y:v,w:u,h:l,radius:g}),e.fill(),e.stroke(),e.fillStyle=o.backgroundColor,e.beginPath(),$o(e,{x:b,y:v+1,w:u-2,h:l-2,radius:g}),e.fill()):(e.fillStyle=s.multiKeyBackground,e.fillRect(x,v,u,l),e.strokeRect(x,v,u,l),e.fillStyle=o.backgroundColor,e.fillRect(b,v+1,u-2,l-2))}e.fillStyle=this.labelTextColors[r]}drawBody(e,n,r){const{body:i}=this,{bodySpacing:s,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:u,boxPadding:d}=r,h=Pe(r.bodyFont);let f=h.lineHeight,m=0;const v=jr(r.rtl,this.x,this.width),x=function(k){n.fillText(k,v.x(e.x+m),e.y+f/2),e.y+=f+s},b=v.textAlign(o);let g,p,y,w,_,S,N;for(n.textAlign=o,n.textBaseline="middle",n.font=h.string,e.x=Rs(this,b,r),n.fillStyle=r.bodyColor,Y(this.beforeBody,x),m=a&&b!=="right"?o==="center"?u/2+d:u+2+d:0,w=0,S=i.length;w<S;++w){for(g=i[w],p=this.labelTextColors[w],n.fillStyle=p,Y(g.before,x),y=g.lines,a&&y.length&&(this._drawColorBox(n,e,w,v,r),f=Math.max(h.lineHeight,l)),_=0,N=y.length;_<N;++_)x(y[_]),f=h.lineHeight;Y(g.after,x)}m=0,f=h.lineHeight,Y(this.afterBody,x),e.y-=s}drawFooter(e,n,r){const i=this.footer,s=i.length;let o,a;if(s){const l=jr(r.rtl,this.x,this.width);for(e.x=Rs(this,r.footerAlign,r),e.y+=r.footerMarginTop,n.textAlign=l.textAlign(r.footerAlign),n.textBaseline="middle",o=Pe(r.footerFont),n.fillStyle=r.footerColor,n.font=o.string,a=0;a<s;++a)n.fillText(i[a],l.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+r.footerSpacing}}drawBackground(e,n,r,i){const{xAlign:s,yAlign:o}=this,{x:a,y:l}=e,{width:u,height:d}=r,{topLeft:h,topRight:f,bottomLeft:m,bottomRight:v}=Nr(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+h,l),o==="top"&&this.drawCaret(e,n,r,i),n.lineTo(a+u-f,l),n.quadraticCurveTo(a+u,l,a+u,l+f),o==="center"&&s==="right"&&this.drawCaret(e,n,r,i),n.lineTo(a+u,l+d-v),n.quadraticCurveTo(a+u,l+d,a+u-v,l+d),o==="bottom"&&this.drawCaret(e,n,r,i),n.lineTo(a+m,l+d),n.quadraticCurveTo(a,l+d,a,l+d-m),o==="center"&&s==="left"&&this.drawCaret(e,n,r,i),n.lineTo(a,l+h),n.quadraticCurveTo(a,l,a+h,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,r=this.$animations,i=r&&r.x,s=r&&r.y;if(i||s){const o=di[e.position].call(this,this._active,this._eventPosition);if(!o)return;const a=this._size=zh(this,e),l=Object.assign({},o,this._size),u=Ah(n,e,l),d=Ih(e,l,u,n);(i._to!==d.x||s._to!==d.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let r=this.opacity;if(!r)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},s={x:this.x,y:this.y};r=Math.abs(r)<.001?0:r;const o=ut(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(e.save(),e.globalAlpha=r,this.drawBackground(s,e,i,n),ng(e,n.textDirection),s.y+=o.top,this.drawTitle(s,e,n),this.drawBody(s,e,n),this.drawFooter(s,e,n),rg(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const r=this._active,i=e.map(({datasetIndex:a,index:l})=>{const u=this.chart.getDatasetMeta(a);if(!u)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:u.data[l],index:l}}),s=!Eo(r,i),o=this._positionChanged(i,n);(s||o)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,r=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,s=this._active||[],o=this._getActiveElements(e,s,n,r),a=this._positionChanged(o,e),l=n||!Eo(o,s)||a;return l&&(this._active=o,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,r,i){const s=this.options;if(e.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const o=this.chart.getElementsAtEventForMode(e,s.mode,s,r);return s.reverse&&o.reverse(),o}_positionChanged(e,n){const{caretX:r,caretY:i,options:s}=this,o=di[s.position].call(this,e,n);return o!==!1&&(r!==o.x||i!==o.y)}}L(nc,"positioners",di);var wg={id:"tooltip",_element:nc,positioners:di,afterInit(t,e,n){n&&(t.tooltip=new nc({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:bg},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const oS=(t,e,n,r)=>(typeof e=="string"?(n=t.push(e)-1,r.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function aS(t,e,n,r){const i=t.indexOf(e);if(i===-1)return oS(t,e,n,r);const s=t.lastIndexOf(e);return i!==s?n:i}const lS=(t,e)=>t===null?null:Be(Math.round(t),0,e);function Wh(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class rc extends Ur{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const r=this.getLabels();for(const{index:i,label:s}of n)r[i]===s&&r.splice(i,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(Q(e))return null;const r=this.getLabels();return n=isFinite(n)&&r[n]===e?n:aS(r,e,W(n,e),this._addedLabels),lS(n,r.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:r,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(r=0),n||(i=this.getLabels().length-1)),this.min=r,this.max=i}buildTicks(){const e=this.min,n=this.max,r=this.options.offset,i=[];let s=this.getLabels();s=e===0&&n===s.length-1?s:s.slice(e,n+1),this._valueRange=Math.max(s.length-(r?0:1),1),this._startValue=this.min-(r?.5:0);for(let o=e;o<=n;o++)i.push({value:o});return i}getLabelForValue(e){return Wh.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}L(rc,"id","category"),L(rc,"defaults",{ticks:{callback:Wh}});function cS(t,e){const n=[],{bounds:i,step:s,min:o,max:a,precision:l,count:u,maxTicks:d,maxDigits:h,includeBounds:f}=t,m=s||1,v=d-1,{min:x,max:b}=e,g=!Q(o),p=!Q(a),y=!Q(u),w=(b-x)/(h+1);let _=Qd((b-x)/v/m)*m,S,N,k,T;if(_<1e-14&&!g&&!p)return[{value:x},{value:b}];T=Math.ceil(b/_)-Math.floor(x/_),T>v&&(_=Qd(T*_/v/m)*m),Q(l)||(S=Math.pow(10,l),_=Math.ceil(_*S)/S),i==="ticks"?(N=Math.floor(x/_)*_,k=Math.ceil(b/_)*_):(N=x,k=b),g&&p&&s&&lb((a-o)/s,_/1e3)?(T=Math.round(Math.min((a-o)/_,d)),_=(a-o)/T,N=o,k=a):y?(N=g?o:N,k=p?a:k,T=u-1,_=(k-N)/T):(T=(k-N)/_,Qs(T,Math.round(T),_/1e3)?T=Math.round(T):T=Math.ceil(T));const M=Math.max(Kd(_),Kd(N));S=Math.pow(10,Q(l)?M:l),N=Math.round(N*S)/S,k=Math.round(k*S)/S;let E=0;for(g&&(f&&N!==o?(n.push({value:o}),N<o&&E++,Qs(Math.round((N+E*_)*S)/S,o,Bh(o,w,t))&&E++):N<o&&E++);E<T;++E){const $=Math.round((N+E*_)*S)/S;if(p&&$>a)break;n.push({value:$})}return p&&f&&k!==a?n.length&&Qs(n[n.length-1].value,a,Bh(a,w,t))?n[n.length-1].value=a:n.push({value:a}):(!p||k===a)&&n.push({value:k}),n}function Bh(t,e,{horizontal:n,minRotation:r}){const i=It(r),s=(n?Math.sin(i):Math.cos(i))||.001,o=.75*e*(""+t).length;return Math.min(e/s,o)}class uS extends Ur{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return Q(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:r}=this.getUserBounds();let{min:i,max:s}=this;const o=l=>i=n?i:l,a=l=>s=r?s:l;if(e){const l=yn(i),u=yn(s);l<0&&u<0?a(0):l>0&&u>0&&o(0)}if(i===s){let l=s===0?1:Math.abs(s*.05);a(s+l),e||o(i-l)}this.min=i,this.max=s}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:r}=e,i;return r?(i=Math.ceil(this.max/r)-Math.floor(this.min/r)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${r} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let r=this.getTickLimit();r=Math.max(2,r);const i={maxTicks:r,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},s=this._range||this,o=cS(i,s);return e.bounds==="ticks"&&cb(o,this,"value"),e.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const e=this.ticks;let n=this.min,r=this.max;if(super.configure(),this.options.offset&&e.length){const i=(r-n)/Math.max(e.length-1,1)/2;n-=i,r+=i}this._startValue=n,this._endValue=r,this._valueRange=r-n}getLabelForValue(e){return ou(e,this.chart.options.locale,this.options.ticks.format)}}class ic extends uS{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=ct(e)?e:0,this.max=ct(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,r=It(this.options.ticks.minRotation),i=(e?Math.sin(r):Math.cos(r))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,s.lineHeight/i))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}L(ic,"id","linear"),L(ic,"defaults",{ticks:{callback:Qp.formatters.numeric}});const ca={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},He=Object.keys(ca);function Vh(t,e){return t-e}function Yh(t,e){if(Q(e))return null;const n=t._adapter,{parser:r,round:i,isoWeekday:s}=t._parseOpts;let o=e;return typeof r=="function"&&(o=r(o)),ct(o)||(o=typeof r=="string"?n.parse(o,r):n.parse(o)),o===null?null:(i&&(o=i==="week"&&(Oo(s)||s===!0)?n.startOf(o,"isoWeek",s):n.startOf(o,i)),+o)}function Xh(t,e,n,r){const i=He.length;for(let s=He.indexOf(t);s<i-1;++s){const o=ca[He[s]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((n-e)/(a*o.size))<=r)return He[s]}return He[i-1]}function dS(t,e,n,r,i){for(let s=He.length-1;s>=He.indexOf(n);s--){const o=He[s];if(ca[o].common&&t._adapter.diff(i,r,o)>=e-1)return o}return He[n?He.indexOf(n):0]}function hS(t){for(let e=He.indexOf(t)+1,n=He.length;e<n;++e)if(ca[He[e]].common)return He[e]}function Qh(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:r,hi:i}=iu(n,e),s=n[r]>=e?n[r]:n[i];t[s]=!0}}function fS(t,e,n,r){const i=t._adapter,s=+i.startOf(e[0].value,r),o=e[e.length-1].value;let a,l;for(a=s;a<=o;a=+i.add(a,1,r))l=n[a],l>=0&&(e[l].major=!0);return e}function Kh(t,e,n){const r=[],i={},s=e.length;let o,a;for(o=0;o<s;++o)a=e[o],i[a]=o,r.push({value:a,major:!1});return s===0||!n?r:fS(t,r,i,n)}class Ao extends Ur{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const r=e.time||(e.time={}),i=this._adapter=new Cw._date(e.adapters.date);i.init(n),wi(r.displayFormats,i.formats()),this._parseOpts={parser:r.parser,round:r.round,isoWeekday:r.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:Yh(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,r=e.time.unit||"day";let{min:i,max:s,minDefined:o,maxDefined:a}=this.getUserBounds();function l(u){!o&&!isNaN(u.min)&&(i=Math.min(i,u.min)),!a&&!isNaN(u.max)&&(s=Math.max(s,u.max))}(!o||!a)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=ct(i)&&!isNaN(i)?i:+n.startOf(Date.now(),r),s=ct(s)&&!isNaN(s)?s:+n.endOf(Date.now(),r)+1,this.min=Math.min(i,s-1),this.max=Math.max(i+1,s)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],r=e[e.length-1]),{min:n,max:r}}buildTicks(){const e=this.options,n=e.time,r=e.ticks,i=r.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const s=this.min,o=this.max,a=mb(i,s,o);return this._unit=n.unit||(r.autoSkip?Xh(n.minUnit,this.min,this.max,this._getLabelCapacity(s)):dS(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!r.major.enabled||this._unit==="year"?void 0:hS(this._unit),this.initOffsets(i),e.reverse&&a.reverse(),Kh(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,r=0,i,s;this.options.offset&&e.length&&(i=this.getDecimalForValue(e[0]),e.length===1?n=1-i:n=(this.getDecimalForValue(e[1])-i)/2,s=this.getDecimalForValue(e[e.length-1]),e.length===1?r=s:r=(s-this.getDecimalForValue(e[e.length-2]))/2);const o=e.length<3?.5:.25;n=Be(n,0,o),r=Be(r,0,o),this._offsets={start:n,end:r,factor:1/(n+1+r)}}_generate(){const e=this._adapter,n=this.min,r=this.max,i=this.options,s=i.time,o=s.unit||Xh(s.minUnit,n,r,this._getLabelCapacity(n)),a=W(i.ticks.stepSize,1),l=o==="week"?s.isoWeekday:!1,u=Oo(l)||l===!0,d={};let h=n,f,m;if(u&&(h=+e.startOf(h,"isoWeek",l)),h=+e.startOf(h,u?"day":o),e.diff(r,n,o)>1e5*a)throw new Error(n+" and "+r+" are too far apart with stepSize of "+a+" "+o);const v=i.ticks.source==="data"&&this.getDataTimestamps();for(f=h,m=0;f<r;f=+e.add(f,a,o),m++)Qh(d,f,v);return(f===r||i.bounds==="ticks"||m===1)&&Qh(d,f,v),Object.keys(d).sort(Vh).map(x=>+x)}getLabelForValue(e){const n=this._adapter,r=this.options.time;return r.tooltipFormat?n.format(e,r.tooltipFormat):n.format(e,r.displayFormats.datetime)}format(e,n){const i=this.options.time.displayFormats,s=this._unit,o=n||i[s];return this._adapter.format(e,o)}_tickFormatFunction(e,n,r,i){const s=this.options,o=s.ticks.callback;if(o)return ee(o,[e,n,r],this);const a=s.time.displayFormats,l=this._unit,u=this._majorUnit,d=l&&a[l],h=u&&a[u],f=r[n],m=u&&h&&f&&f.major;return this._adapter.format(e,i||(m?h:d))}generateTickLabels(e){let n,r,i;for(n=0,r=e.length;n<r;++n)i=e[n],i.label=this._tickFormatFunction(i.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,r=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+r)*n.factor)}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+r*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,r=this.ctx.measureText(e).width,i=It(this.isHorizontal()?n.maxRotation:n.minRotation),s=Math.cos(i),o=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:r*s+a*o,h:r*o+a*s}}_getLabelCapacity(e){const n=this.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,s=this._tickFormatFunction(e,0,Kh(this,[e],this._majorUnit),i),o=this._getLabelSize(s),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let e=this._cache.data||[],n,r;if(e.length)return e;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,r=i.length;n<r;++n)e=e.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,r;if(e.length)return e;const i=this.getLabels();for(n=0,r=i.length;n<r;++n)e.push(Yh(this,i[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return Bp(e.sort(Vh))}}L(Ao,"id","time"),L(Ao,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Os(t,e,n){let r=0,i=t.length-1,s,o,a,l;n?(e>=t[r].pos&&e<=t[i].pos&&({lo:r,hi:i}=Zl(t,"pos",e)),{pos:s,time:a}=t[r],{pos:o,time:l}=t[i]):(e>=t[r].time&&e<=t[i].time&&({lo:r,hi:i}=Zl(t,"time",e)),{time:s,pos:a}=t[r],{time:o,pos:l}=t[i]);const u=o-s;return u?a+(l-a)*(e-s)/u:a}class Gh extends Ao{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=Os(n,this.min),this._tableRange=Os(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:r}=this,i=[],s=[];let o,a,l,u,d;for(o=0,a=e.length;o<a;++o)u=e[o],u>=n&&u<=r&&i.push(u);if(i.length<2)return[{time:n,pos:0},{time:r,pos:1}];for(o=0,a=i.length;o<a;++o)d=i[o+1],l=i[o-1],u=i[o],Math.round((d+l)/2)!==u&&s.push({time:u,pos:o/(a-1)});return s}_generate(){const e=this.min,n=this.max;let r=super.getDataTimestamps();return(!r.includes(e)||!r.length)&&r.splice(0,0,e),(!r.includes(n)||r.length===1)&&r.push(n),r.sort((i,s)=>i-s)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),r=this.getLabelTimestamps();return n.length&&r.length?e=this.normalize(n.concat(r)):e=n.length?n:r,e=this._cache.all=e,e}getDecimalForValue(e){return(Os(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return Os(this._table,r*this._tableRange+this._minPos,!0)}}L(Gh,"id","timeseries"),L(Gh,"defaults",Ao.defaults);const Sg="label";function qh(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function mS(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function _g(t,e){t.labels=e}function Ng(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Sg;const r=[];t.datasets=e.map(i=>{const s=t.datasets.find(o=>o[n]===i[n]);return!s||!i.data||r.includes(s)?{...i}:(r.push(s),Object.assign(s,i),s)})}function pS(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Sg;const n={labels:[],datasets:[]};return _g(n,t.labels),Ng(n,t.datasets,e),n}function gS(t,e){const{height:n=150,width:r=300,redraw:i=!1,datasetIdKey:s,type:o,data:a,options:l,plugins:u=[],fallbackContent:d,updateMode:h,...f}=t,m=j.useRef(null),v=j.useRef(null),x=()=>{m.current&&(v.current=new ns(m.current,{type:o,data:pS(a,s),options:l&&{...l},plugins:u}),qh(e,v.current))},b=()=>{qh(e,null),v.current&&(v.current.destroy(),v.current=null)};return j.useEffect(()=>{!i&&v.current&&l&&mS(v.current,l)},[i,l]),j.useEffect(()=>{!i&&v.current&&_g(v.current.config.data,a.labels)},[i,a.labels]),j.useEffect(()=>{!i&&v.current&&a.datasets&&Ng(v.current.config.data,a.datasets,s)},[i,a.datasets]),j.useEffect(()=>{v.current&&(i?(b(),setTimeout(x)):v.current.update(h))},[i,l,a.labels,a.datasets,h]),j.useEffect(()=>{v.current&&(b(),setTimeout(x))},[o]),j.useEffect(()=>(x(),()=>b()),[]),gt.createElement("canvas",{ref:m,role:"img",height:n,width:r,...f},d)}const xS=j.forwardRef(gS);function jg(t,e){return ns.register(e),j.forwardRef((n,r)=>gt.createElement(xS,{...n,ref:r,type:t}))}const vS=jg("bar",Ks),yS=jg("doughnut",li);ns.register(ui,wg,vg);function bS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n}=Qe(),{theme:r}=_e(),i=j.useRef(null),s=t.currentUnits,o=e,a=s+o,l=a>0?o/a*100:0;j.useEffect(()=>{const h=i.current;if(h){const f=h.ctx,m=f.createRadialGradient(200,200,50,200,200,150);m.addColorStop(0,"#667eea"),m.addColorStop(.3,"#764ba2"),m.addColorStop(.6,"#667eea"),m.addColorStop(1,"#f093fb");const v=f.createRadialGradient(200,200,50,200,200,150);v.addColorStop(0,"#ff9a9e"),v.addColorStop(.3,"#fecfef"),v.addColorStop(.6,"#fecfef"),v.addColorStop(1,"#ffc3a0"),h.data.datasets[0].backgroundColor=[m,v],h.update()}},[s,o]);const u={labels:[`Remaining ${n()}`,`Used ${n()}`],datasets:[{data:[s,o],backgroundColor:["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)"],borderColor:["rgba(255, 255, 255, 0.9)","rgba(255, 255, 255, 0.9)"],borderWidth:4,cutout:"78%",borderRadius:12,borderJoinStyle:"round",hoverBorderWidth:6,hoverBorderColor:["rgba(255, 255, 255, 1)","rgba(255, 255, 255, 1)"],shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.1)"}]},d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(h){const f=h.label||"",m=h.parsed,v=a>0?(m/a*100).toFixed(1):0;return`${f}: ${m.toFixed(2)} (${v}%)`}}}},animation:{animateRotate:!0,animateScale:!0,duration:2e3,easing:"easeInOutCubic",delay:h=>h.dataIndex*200},interaction:{intersect:!1,mode:"nearest"},elements:{arc:{borderWidth:4,hoverBorderWidth:6,borderSkipped:!1,borderAlign:"inner"}},layout:{padding:{top:20,bottom:20,left:20,right:20}}};return c.jsxs("div",{className:"relative",children:[c.jsxs("div",{className:"relative h-96 p-8",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 rounded-3xl opacity-60"}),c.jsx("div",{className:"absolute inset-1 bg-gradient-to-tr from-white/90 via-white/70 to-white/50 rounded-2xl backdrop-blur-lg border border-white/20 shadow-2xl"}),c.jsx("div",{className:"absolute inset-3 bg-gradient-to-bl from-white/40 to-transparent rounded-xl"}),c.jsx("div",{className:"absolute top-4 left-4 w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-xl animate-pulse"}),c.jsx("div",{className:"absolute bottom-4 right-4 w-20 h-20 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-15 blur-2xl animate-pulse",style:{animationDelay:"1s"}}),c.jsxs("div",{className:"relative h-full flex items-center justify-center",children:[c.jsx("div",{className:"w-full h-full max-w-sm max-h-sm",children:c.jsx(yS,{ref:i,data:u,options:d})}),c.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 w-40 h-40 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"}),c.jsxs("div",{className:"relative w-40 h-40 bg-white/40 backdrop-blur-lg rounded-full border border-white/30 shadow-2xl flex items-center justify-center",children:[c.jsx("div",{className:"absolute inset-2 bg-gradient-to-br from-white/60 via-white/40 to-white/20 rounded-full"}),c.jsxs("div",{className:"relative text-center z-10",children:[c.jsx("div",{className:"mb-2 flex justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"}),c.jsx($e,{className:"relative h-8 w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce",style:{animationDuration:"2s"}})]})}),c.jsxs("div",{className:"relative mb-1",children:[c.jsx("div",{className:"text-3xl font-black bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent drop-shadow-lg",children:s.toFixed(1)}),c.jsxs("div",{className:"text-xs font-semibold text-gray-600 mt-1 tracking-wide",children:[n()," Left"]})]}),c.jsx("div",{className:"mt-1",children:c.jsxs("div",{className:`text-sm font-bold tracking-tight ${l>70?"text-red-500 drop-shadow-lg":l>40?"text-amber-500 drop-shadow-lg":"text-emerald-500 drop-shadow-lg"}`,children:[l.toFixed(1),"% Used"]})})]}),c.jsx("div",{className:"absolute top-3 right-3 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-pulse"}),c.jsx("div",{className:"absolute bottom-3 left-3 w-1.5 h-1.5 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-50 animate-pulse",style:{animationDelay:"1s"}})]})]})})]})]}),c.jsxs("div",{className:"mt-8 grid grid-cols-2 gap-6",children:[c.jsxs("div",{className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 p-6 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/40 to-transparent"}),c.jsxs("div",{className:"relative flex items-center space-x-4",children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"}),c.jsx("div",{className:"relative p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-lg",children:c.jsx(Jc,{className:"h-6 w-6 text-white"})})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-black bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:s.toFixed(2)}),c.jsxs("div",{className:"text-sm font-semibold text-gray-600 tracking-wide",children:[n()," Available"]})]})]})]}),c.jsxs("div",{className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-50 via-pink-50 to-red-50 p-6 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/40 to-transparent"}),c.jsxs("div",{className:"relative flex items-center space-x-4",children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"}),c.jsx("div",{className:"relative p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg",children:c.jsx(k1,{className:"h-6 w-6 text-white"})})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-black bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:o.toFixed(2)}),c.jsxs("div",{className:"text-sm font-semibold text-gray-600 tracking-wide",children:[n()," Consumed"]})]})]})]})]}),c.jsxs("div",{className:"mt-8 relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 p-6 border border-white/30 shadow-xl backdrop-blur-sm",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/60 to-white/20"}),c.jsxs("div",{className:"relative flex justify-between items-center",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:"text-sm font-semibold text-gray-500 tracking-wider uppercase",children:"Total Cost"}),c.jsxs("div",{className:"text-3xl font-black bg-gradient-to-r from-slate-700 via-gray-800 to-zinc-700 bg-clip-text text-transparent",children:[t.currencySymbol||"R",(o*t.unitCost).toFixed(2)]})]}),c.jsxs("div",{className:"text-right space-y-2",children:[c.jsx("div",{className:"text-sm font-semibold text-gray-500 tracking-wider uppercase",children:"Rate"}),c.jsxs("div",{className:"text-xl font-bold bg-gradient-to-r from-slate-600 to-gray-700 bg-clip-text text-transparent",children:[t.currencySymbol||"R",t.unitCost.toFixed(2),"/",n()]})]})]}),c.jsx("div",{className:"absolute top-2 right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-sm"}),c.jsx("div",{className:"absolute bottom-2 left-2 w-6 h-6 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-15 blur-sm"})]}),c.jsxs("div",{className:"mt-8",children:[c.jsxs("div",{className:"flex justify-between items-center mb-4",children:[c.jsx("span",{className:"text-sm font-bold text-gray-700 tracking-wide uppercase",children:"Usage Progress"}),c.jsxs("span",{className:`text-lg font-black px-3 py-1 rounded-full ${l>70?"bg-gradient-to-r from-red-100 to-red-200 text-red-700":l>40?"bg-gradient-to-r from-amber-100 to-yellow-200 text-amber-700":"bg-gradient-to-r from-emerald-100 to-green-200 text-emerald-700"}`,children:[l.toFixed(1),"%"]})]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"w-full bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 rounded-full h-4 shadow-inner border border-white/50 backdrop-blur-sm",children:c.jsx("div",{className:`h-4 rounded-full transition-all duration-1500 ease-out shadow-lg ${l>70?"bg-gradient-to-r from-red-400 via-red-500 to-red-600":l>40?"bg-gradient-to-r from-amber-400 via-yellow-500 to-orange-500":"bg-gradient-to-r from-emerald-400 via-green-500 to-teal-500"}`,style:{width:`${Math.min(l,100)}%`}})}),c.jsx("div",{className:`absolute top-0 h-4 rounded-full opacity-60 blur-sm transition-all duration-1500 ${l>70?"bg-gradient-to-r from-red-300 to-red-500":l>40?"bg-gradient-to-r from-amber-300 to-orange-500":"bg-gradient-to-r from-emerald-300 to-green-500"}`,style:{width:`${Math.min(l,100)}%`}}),c.jsx("div",{className:`absolute top-0 h-4 rounded-full opacity-30 blur-md transition-all duration-1500 ${l>70?"bg-gradient-to-r from-red-200 to-red-400":l>40?"bg-gradient-to-r from-amber-200 to-orange-400":"bg-gradient-to-r from-emerald-200 to-green-400"}`,style:{width:`${Math.min(l,100)}%`}}),l>0&&c.jsx("div",{className:"absolute top-0 h-4 rounded-full bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse",style:{width:`${Math.min(l,100)}%`}})]})]})]})}function wS(){const t=ia(),{state:e,getDisplayUnitName:n}=Qe(),{theme:r}=_e(),i=e.currentUnits,s=e.thresholdLimit;return c.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx("div",{className:"p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg",children:c.jsx(_r,{className:"h-6 w-6 text-white animate-pulse"})})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsxs("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:["⚠️ Low ",n()," Warning!"]}),c.jsxs("div",{className:"text-sm text-amber-700 space-y-2",children:[c.jsxs("p",{className:"font-medium",children:["You have ",c.jsxs("strong",{className:"text-amber-900",children:[i.toFixed(2)," ",n()]})," remaining, which is below your threshold of ",c.jsxs("strong",{className:"text-amber-900",children:[s.toFixed(2)," ",n()]}),"."]}),c.jsxs("p",{children:["💡 ",c.jsx("strong",{children:"Time to top up!"})," Consider purchasing more ",n()," to avoid running out of power."]})]}),c.jsxs("div",{className:"mt-4 flex flex-wrap gap-3",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",children:"🛒 Top Up Now"}),c.jsx("button",{onClick:()=>t("/settings"),className:"bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200",children:"⚙️ Adjust Threshold"})]})]})]})})}function ua({title:t,content:e,position:n="top"}){const[r,i]=j.useState(!1),{theme:s}=_e(),o={top:"bottom-full left-1/2 transform -translate-x-1/2 mb-2",bottom:"top-full left-1/2 transform -translate-x-1/2 mt-2",left:"right-full top-1/2 transform -translate-y-1/2 mr-2",right:"left-full top-1/2 transform -translate-y-1/2 ml-2"},a={top:"top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-blue-600",bottom:"bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-blue-600",left:"left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-blue-600",right:"right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-blue-600"};return c.jsxs("div",{className:"relative inline-block",children:[c.jsx("button",{onClick:()=>i(!r),className:"p-1 rounded-full text-blue-500 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200","aria-label":"Show help",children:c.jsx(_1,{className:"h-5 w-5"})}),r&&c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>i(!1)}),c.jsxs("div",{className:`absolute z-50 ${o[n]} w-80 max-w-sm`,children:[c.jsxs("div",{className:`${s.card} rounded-xl shadow-xl border-2 border-blue-200 p-4 bg-gradient-to-br from-blue-50 to-indigo-50`,children:[c.jsxs("div",{className:"flex items-center justify-between mb-3",children:[c.jsx("h3",{className:"font-bold text-blue-800 text-sm",children:t}),c.jsx("button",{onClick:()=>i(!1),className:"p-1 rounded-full text-blue-400 hover:text-blue-600 hover:bg-blue-100 transition-all duration-200",children:c.jsx($p,{className:"h-4 w-4"})})]}),c.jsx("div",{className:"text-sm text-blue-700 leading-relaxed",children:e})]}),c.jsx("div",{className:`absolute w-0 h-0 border-4 ${a[n]}`})]})]})]})}function Zh(){const t=ia(),{state:e,isThresholdExceeded:n,getDisplayUnitName:r,weeklyPurchaseTotal:i,monthlyPurchaseTotal:s,weeklyUsageTotal:o,monthlyUsageTotal:a,usageSinceLastRecording:l}=Qe(),{theme:u}=_e();return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${u.text}`,children:"Dashboard"}),c.jsx(ua,{title:"Dashboard Overview",content:"This is your main control center. Here you can see your current units, usage patterns, weekly/monthly totals, and quick access to all major functions. The dial shows your usage visually, and the cards below show real-time calculations.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${u.textSecondary}`,children:"Monitor your electricity usage and current meter readings"})]}),n&&c.jsx(wS,{}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[c.jsx("div",{className:`${u.card} rounded-xl shadow-lg p-4 border ${u.border} bg-gradient-to-br from-emerald-50 to-green-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${u.textSecondary}`,children:"Weekly Purchases"}),c.jsxs("p",{className:"text-xl font-bold text-emerald-600",children:[e.currencySymbol,i.toFixed(2)]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-emerald-100",children:c.jsx(Me,{className:"h-5 w-5 text-emerald-600"})})]})}),c.jsx("div",{className:`${u.card} rounded-xl shadow-lg p-4 border ${u.border} bg-gradient-to-br from-blue-50 to-indigo-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${u.textSecondary}`,children:"Monthly Purchases"}),c.jsxs("p",{className:"text-xl font-bold text-blue-600",children:[e.currencySymbol,s.toFixed(2)]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-blue-100",children:c.jsx(Me,{className:"h-5 w-5 text-blue-600"})})]})}),c.jsx("div",{className:`${u.card} rounded-xl shadow-lg p-4 border ${u.border} bg-gradient-to-br from-rose-50 to-pink-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${u.textSecondary}`,children:"Weekly Usage"}),c.jsxs("p",{className:"text-xl font-bold text-rose-600",children:[o.toFixed(2)," ",r()]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-rose-100",children:c.jsx(De,{className:"h-5 w-5 text-rose-600"})})]})}),c.jsx("div",{className:`${u.card} rounded-xl shadow-lg p-4 border ${u.border} bg-gradient-to-br from-purple-50 to-violet-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${u.textSecondary}`,children:"Monthly Usage"}),c.jsxs("p",{className:"text-xl font-bold text-purple-600",children:[a.toFixed(2)," ",r()]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-purple-100",children:c.jsx(De,{className:"h-5 w-5 text-purple-600"})})]})})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.jsx("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border} bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-2`,children:"Current Units"}),c.jsx("p",{className:"text-3xl font-bold text-amber-600",children:e.currentUnits.toFixed(2)}),c.jsxs("p",{className:"text-sm text-amber-500 font-medium",children:[r()," remaining"]}),c.jsxs("p",{className:`text-xs ${u.textSecondary} mt-2`,children:["Value: ",e.currencySymbol,(e.currentUnits*e.unitCost).toFixed(2)]})]}),c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-amber-400 to-orange-500 shadow-lg",children:c.jsx($e,{className:"h-8 w-8 text-white"})})]})}),c.jsx("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border} bg-gradient-to-br from-cyan-50 via-teal-50 to-blue-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-2`,children:"Usage Since Last Recording"}),c.jsx("p",{className:"text-3xl font-bold text-cyan-600",children:l.toFixed(2)}),c.jsxs("p",{className:"text-sm text-cyan-500 font-medium",children:[r()," used"]}),c.jsxs("p",{className:`text-xs ${u.textSecondary} mt-2`,children:["Cost: ",e.currencySymbol,(l*e.unitCost).toFixed(2)]})]}),c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-cyan-400 to-teal-500 shadow-lg",children:c.jsx(De,{className:"h-8 w-8 text-white"})})]})})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${u.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx($e,{className:"h-5 w-5 text-white"})}),"Usage Overview"]}),c.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-4",children:c.jsx(bS,{})})]}),c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${u.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(De,{className:"h-5 w-5 text-white"})}),"Recent Activity"]}),c.jsxs("div",{className:"space-y-3",children:[e.purchases.slice(0,3).map(d=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500 shadow-sm",children:c.jsx(Me,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsxs("p",{className:`text-sm font-semibold ${u.text}`,children:["Purchase: ",e.currencySymbol||"R",d.currency.toFixed(2)]}),c.jsx("p",{className:`text-xs ${u.textSecondary} opacity-70`,children:new Date(d.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:"text-sm font-semibold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:["+",d.units.toFixed(2)," ",r()]})]})},d.id)),e.usageHistory.slice(0,2).map(d=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-rose-400 to-pink-500 shadow-sm",children:c.jsx(De,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-sm font-semibold ${u.text}`,children:"Usage recorded"}),c.jsx("p",{className:`text-xs ${u.textSecondary} opacity-70`,children:new Date(d.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:"text-sm font-semibold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:["-",d.usage.toFixed(2)," ",r()]})]})},d.id)),e.purchases.length===0&&e.usageHistory.length===0&&c.jsxs("div",{className:"text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-green-200 w-fit mx-auto mb-4",children:c.jsx($e,{className:"h-12 w-12 text-emerald-600"})}),c.jsx("p",{className:`text-sm ${u.textSecondary} opacity-80 font-medium`,children:"No recent activity"}),c.jsx("p",{className:`text-xs ${u.textSecondary} opacity-60 mt-1`,children:"Start by making a purchase or recording usage"})]})]})]})]}),c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-8 border ${u.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsxs("h2",{className:`text-2xl font-bold ${u.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-slate-500 to-gray-600 shadow-lg",children:c.jsx($e,{className:"h-6 w-6 text-white"})}),"Quick Actions"]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[c.jsxs("button",{onClick:()=>t("/purchases"),className:"p-6 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-2xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[c.jsx(Me,{className:"h-8 w-8 mx-auto mb-3"}),c.jsx("span",{className:"block text-lg font-semibold",children:"Add Purchase"}),c.jsx("span",{className:"block text-sm opacity-80 mt-1",children:"Top up your units"})]}),c.jsxs("button",{onClick:()=>t("/usage"),className:"p-6 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[c.jsx(De,{className:"h-8 w-8 mx-auto mb-3"}),c.jsx("span",{className:"block text-lg font-semibold",children:"Record Usage"}),c.jsx("span",{className:"block text-sm opacity-80 mt-1",children:"Track consumption"})]}),c.jsxs("button",{onClick:()=>t("/history"),className:"p-6 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-2xl hover:from-violet-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[c.jsx($e,{className:"h-8 w-8 mx-auto mb-3"}),c.jsx("span",{className:"block text-lg font-semibold",children:"View History"}),c.jsx("span",{className:"block text-sm opacity-80 mt-1",children:"See all records"})]})]})]})]})}function SS(){var x;const[t,e]=j.useState(""),[n,r]=j.useState(!1),{state:i,addPurchase:s,getDisplayUnitName:o}=Qe(),{theme:a}=_e(),l=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],u=b=>Math.round((b+Number.EPSILON)*100)/100,d=b=>{const g=u(b);return g%1===0?g.toString():g.toFixed(2)},h=parseFloat(t)||0,f=i.unitCost||0,m=f>0?u(h/f):0,v=async b=>{b.preventDefault(),r(!0);try{const g=u(parseFloat(t)),p=m;if(isNaN(g)||g<=0){alert("Please enter a valid positive amount");return}if(f<=0){alert("Please set a valid unit cost in Settings before making a purchase");return}if(p<=0){alert("The calculated units must be greater than 0");return}s(g,p),e(""),alert(`Purchase added successfully! Added ${d(p)} ${o()} for ${i.currencySymbol||"R"}${d(g)}`)}catch(g){console.error("Error adding purchase:",g),alert("Error adding purchase. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:v,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"currency",className:`block text-sm font-semibold ${a.text} mb-3`,children:["💰 Amount (",((x=l.find(b=>b.code===(i.currency||"ZAR")))==null?void 0:x.name)||"South African Rand",")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500",children:c.jsx(Me,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currency",value:t,onChange:b=>e(b.target.value),step:"0.01",min:"0",placeholder:"Enter amount to spend",className:`w-full pl-12 pr-4 py-4 border-4 border-emerald-300 rounded-xl focus:ring-4 focus:ring-emerald-500 focus:border-emerald-600 bg-gradient-to-br from-emerald-50 to-green-50 ${a.text} placeholder-emerald-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-emerald-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${a.textSecondary} opacity-80 font-medium`,children:"💡 Enter the amount you want to spend"})]}),c.jsxs("div",{children:[c.jsxs("label",{className:`block text-sm font-semibold ${a.text} mb-3`,children:["⚡ Units Preview (",o(),")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500",children:c.jsx($e,{className:"h-4 w-4 text-white"})})}),c.jsx("div",{className:`w-full pl-12 pr-4 py-4 border-4 border-blue-300 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 ${a.text} font-bold text-lg shadow-lg flex items-center min-h-[56px]`,children:h>0&&f>0?c.jsxs("span",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:[d(m)," ",o()]}):c.jsx("span",{className:"text-blue-400 font-medium",children:f<=0?"Set unit cost in Settings first":"Enter amount above to see units"})})]}),c.jsx("p",{className:`mt-2 text-xs ${a.textSecondary} opacity-80 font-medium`,children:"⚡ Live preview of units you'll receive"})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Rp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${a.text} text-lg`,children:"Calculation Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${a.text} font-bold`,children:[i.currencySymbol||"R",d(f)," per ",o()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Amount:"}),c.jsxs("span",{className:"font-bold text-lg bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[i.currencySymbol||"R",d(h)]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Units:"}),c.jsxs("span",{className:"font-bold text-lg bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:[d(m)," ",o()]})]}),c.jsx("div",{className:"border-t border-violet-200 my-3"}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg border border-amber-100",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"New Total Units:"}),c.jsxs("span",{className:"font-bold text-xl bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent",children:[d(u(i.currentUnits+m))," ",o()]})]})]})]}),c.jsx("button",{type:"submit",disabled:n||h<=0||m<=0||f<=0,className:"w-full bg-gradient-to-r from-emerald-500 via-green-500 to-teal-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-emerald-600 hover:via-green-600 hover:to-teal-700 transition-all duration-300 focus:ring-4 focus:ring-emerald-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Adding Purchase..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Me,{className:"h-5 w-5"}),"Add Purchase"]})})}),c.jsxs("div",{className:"text-center p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-100",children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-2",children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${a.text}`,children:"Live Calculator"})]}),c.jsx("p",{className:`text-xs ${a.textSecondary} opacity-80 leading-relaxed`,children:"Enter the amount you want to spend and see the units you'll receive in real-time. The calculation is based on your current unit cost setting."})]})]})}function _S(){const{state:t,getDisplayUnitName:e}=Qe(),{theme:n}=_e(),r=t.purchases.reduce((s,o)=>s+o.currency,0),i=t.purchases.reduce((s,o)=>s+o.units,0);return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${n.text}`,children:"Purchases"}),c.jsx(ua,{title:"How Purchases Work",content:"Enter the amount you want to spend and see exactly how many units you'll receive. The calculation is based on your unit cost setting. All purchases are automatically added to your current units balance and saved to history.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${n.textSecondary}`,children:"Add new purchases and view your purchase history"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg",children:c.jsx(Me,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Spent"}),c.jsxs("p",{className:"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[t.currencySymbol||"R",r.toFixed(2)]}),c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})]})]})}),c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg",children:c.jsx($e,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsxs("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:["Total ",e()," Purchased"]}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:i.toFixed(2)}),c.jsx("p",{className:"text-xs text-blue-500 font-medium",children:e()})]})]})}),c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Purchases"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:t.purchases.length}),c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Transactions"})]})]})})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Me,{className:"h-5 w-5 text-white"})}),"Add New Purchase"]}),c.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-4",children:c.jsx(SS,{})})]}),c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(Sr,{className:"h-5 w-5 text-white"})}),"Recent Purchases"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.purchases.slice(0,10).map(s=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${n.text} text-lg`,children:[t.currencySymbol||"R",s.currency.toFixed(2)]}),c.jsxs("p",{className:`text-sm ${n.textSecondary} opacity-80`,children:[s.units.toFixed(2)," ",e()," @ ",t.currencySymbol||"R",s.unitCost.toFixed(2),"/",e()]}),c.jsx("p",{className:`text-xs ${n.textSecondary} mt-1 opacity-70`,children:s.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200",children:["+",s.units.toFixed(2)," ",e()]}),c.jsxs("p",{className:`text-xs ${n.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",s.currency.toFixed(2)]})]})]})},s.id)),t.purchases.length===0&&c.jsxs("div",{className:"text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-green-200 w-fit mx-auto mb-4",children:c.jsx(Me,{className:"h-12 w-12 text-emerald-600"})}),c.jsx("p",{className:`text-sm ${n.textSecondary} opacity-80 font-medium`,children:"No purchases yet"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-60 mt-1`,children:"Add your first purchase above to get started"})]})]})]})]}),t.purchases.length>0&&c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg border ${n.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsxs("div",{className:"px-8 py-6 border-b border-gray-200 bg-white/60 backdrop-blur-sm rounded-t-2xl",children:[c.jsxs("h2",{className:`text-2xl font-bold ${n.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-slate-500 to-gray-600 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),"All Purchases"]}),c.jsx("p",{className:`mt-2 ${n.textSecondary} opacity-80`,children:"Complete history of all your purchase transactions"})]}),c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsx("thead",{className:n.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsxs("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:["Amount (",t.currencySymbol||"R",")"]}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:e()}),c.jsxs("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:["Cost per ",e()]})]})}),c.jsx("tbody",{className:`${n.card} divide-y divide-gray-200`,children:t.purchases.map(s=>c.jsxs("tr",{children:[c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${n.text}`,children:s.timestamp}),c.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${n.text}`,children:[t.currencySymbol||"R",s.currency.toFixed(2)]}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${n.text}`,children:s.units.toFixed(2)}),c.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${n.text}`,children:[t.currencySymbol||"R",s.unitCost.toFixed(2)]})]},s.id))})]})})]})]})}function NS(){const[t,e]=j.useState(""),[n,r]=j.useState(!1),{state:i,updateUsage:s,usageSinceLastRecording:o,getDisplayUnitName:a}=Qe(),{theme:l}=_e(),u=parseFloat(t)||0,d=i.currentUnits-u,h=d*i.unitCost,f=async m=>{m.preventDefault(),r(!0);try{const v=parseFloat(t);if(isNaN(v)||v<0){alert("Please enter a valid meter reading (0 or greater)");return}if(v>i.currentUnits){alert("Current reading cannot be higher than your available units");return}s(v),e(""),alert(`Usage recorded successfully! Used ${d.toFixed(2)} ${a()} costing ${i.currencySymbol||"R"}${h.toFixed(2)}`)}catch(v){console.error("Error recording usage:",v),alert("Error recording usage. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:f,className:"space-y-6",children:[c.jsxs("div",{className:"p-5 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm",children:[c.jsxs("h3",{className:`font-semibold ${l.text} mb-4 flex items-center gap-2`,children:[c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500",children:c.jsx($e,{className:"h-4 w-4 text-white"})}),"Current Status"]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-2 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Available Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-2 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.previousUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-2 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Usage Since Last:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[o.toFixed(2)," ",a()]})]})]})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currentReading",className:`block text-sm font-semibold ${l.text} mb-3`,children:"Current Meter Reading"}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500",children:c.jsx($e,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currentReading",value:t,onChange:m=>e(m.target.value),step:"0.01",min:"0",max:i.currentUnits,placeholder:"Enter current meter reading",className:`w-full pl-12 pr-4 py-4 border-4 border-emerald-300 rounded-xl focus:ring-4 focus:ring-emerald-500 focus:border-emerald-600 bg-gradient-to-br from-emerald-50 to-green-50 ${l.text} placeholder-emerald-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-emerald-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${l.textSecondary} opacity-80 font-medium`,children:"📊 Enter the current reading from your electricity meter"})]}),u>0&&c.jsxs("div",{className:"p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Rp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${l.text} text-lg`,children:"Usage Calculation"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"New Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[u.toFixed(2)," ",a()]})]}),c.jsx("div",{className:"border-t border-violet-200 my-3"}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-rose-50 to-pink-50 rounded-lg border border-rose-100",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Units Used:"}),c.jsxs("span",{className:`font-bold text-lg ${d>=0?"bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent":"text-red-600"}`,children:[d.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg border border-amber-100",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`font-bold text-lg ${d>=0?"bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent":"text-red-600"}`,children:[i.currencySymbol||"R",h.toFixed(2)]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Remaining Units:"}),c.jsxs("span",{className:"font-bold text-lg bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[u.toFixed(2)," ",a()]})]})]}),d<0&&c.jsx("div",{className:"mt-4 p-4 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-xl",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-1 rounded-lg bg-red-500 mr-2",children:c.jsx("span",{className:"text-white text-xs",children:"⚠️"})}),c.jsx("span",{className:"text-red-700 text-sm font-medium",children:"Warning: New reading cannot be higher than available units"})]})})]}),c.jsx("button",{type:"submit",disabled:n||u<=0||u>i.currentUnits,className:"w-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-600 hover:via-indigo-600 hover:to-purple-700 transition-all duration-300 focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Recording Usage..."]}):c.jsxs(c.Fragment,{children:[c.jsx($e,{className:"h-5 w-5"}),"Record Usage"]})})}),c.jsxs("div",{className:"text-center p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-100",children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-2",children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${l.text}`,children:"How it works"})]}),c.jsx("p",{className:`text-xs ${l.textSecondary} opacity-80 leading-relaxed`,children:"Record your current meter reading to track electricity usage. The system will calculate how many units you've used since the last recording."})]})]})}ns.register(rc,ic,Zs,Z2,wg,vg);function jS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n,weeklyUsageTotal:r,monthlyUsageTotal:i}=Qe(),{theme:s}=_e(),o=t.usageHistory.reduce((h,f)=>h+f.usage,0),a=t.usageHistory.length>0?o/t.usageHistory.length:0,l=t.usageHistory.slice(-7).reverse(),u={labels:l.length>0?l.map((h,f)=>new Date(h.timestamp).toLocaleDateString("en-US",{month:"short",day:"numeric"})):["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:`Daily Usage (${n()})`,data:l.length>0?l.map(h=>h.usage):[12.5,15.2,8.7,22.1,18.9,14.3,16.8],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(251, 146, 60, 0.8)","rgba(14, 165, 233, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgba(99, 102, 241, 1)","rgba(139, 92, 246, 1)","rgba(236, 72, 153, 1)","rgba(34, 197, 94, 1)","rgba(251, 146, 60, 1)","rgba(14, 165, 233, 1)","rgba(168, 85, 247, 1)"],borderWidth:2,borderRadius:8,borderSkipped:!1}]},d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Daily Usage Trend",font:{size:16,weight:"bold"},color:s.text==="text-gray-900"?"#1f2937":"#f9fafb",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(h){return`Usage: ${h.parsed.y.toFixed(2)} ${n()}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(156, 163, 175, 0.2)",drawBorder:!1},ticks:{color:s.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12},callback:function(h){return h+" "+n()}}},x:{grid:{display:!1},ticks:{color:s.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12}}}},animation:{duration:1500,easing:"easeInOutQuart"}};return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${s.text}`,children:"Usage Tracking"}),c.jsx(ua,{title:"Usage Calculation",content:"Enter your current meter reading to track usage. The app calculates: Previous Reading - Current Reading = Usage. For example: if you had 100 units and now have 75 units, you used 25 units. The chart shows your daily usage patterns.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${s.textSecondary}`,children:"Record your current meter readings and track electricity usage"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 border ${s.border} bg-gradient-to-br from-emerald-50 to-green-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${s.textSecondary}`,children:"Weekly Usage"}),c.jsxs("p",{className:"text-xl font-bold text-emerald-600",children:[r.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${s.textSecondary} mt-1`,children:["Cost: ",t.currencySymbol,(r*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-emerald-100",children:c.jsx(De,{className:"h-5 w-5 text-emerald-600"})})]})}),c.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 border ${s.border} bg-gradient-to-br from-blue-50 to-indigo-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${s.textSecondary}`,children:"Monthly Usage"}),c.jsxs("p",{className:"text-xl font-bold text-blue-600",children:[i.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${s.textSecondary} mt-1`,children:["Cost: ",t.currencySymbol,(i*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-blue-100",children:c.jsx(De,{className:"h-5 w-5 text-blue-600"})})]})})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[c.jsx("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg",children:c.jsx($e,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80`,children:"Current Reading"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:t.currentUnits.toFixed(2)}),c.jsx("p",{className:"text-xs text-blue-500 font-medium",children:n()})]})]})}),c.jsx("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-rose-400 to-pink-500 shadow-lg",children:c.jsx(De,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80`,children:"Usage Since Last"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:e.toFixed(2)}),c.jsx("p",{className:"text-xs text-rose-500 font-medium",children:n()})]})]})}),c.jsx("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80`,children:"Total Usage"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:o.toFixed(2)}),c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:n()})]})]})}),c.jsx("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg",children:c.jsx(Ad,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80`,children:"Average Usage"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:a.toFixed(2)}),c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:n()})]})]})})]}),c.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-8 border ${s.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsx("div",{className:"flex items-center justify-between mb-6",children:c.jsxs("div",{children:[c.jsxs("h2",{className:`text-2xl font-bold ${s.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg",children:c.jsx(Jc,{className:"h-6 w-6 text-white"})}),"Usage Analytics"]}),c.jsx("p",{className:`mt-2 ${s.textSecondary} opacity-80`,children:"Visual representation of your daily electricity consumption"})]})}),c.jsxs("div",{className:"h-80 relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 rounded-xl backdrop-blur-sm"}),c.jsx("div",{className:"relative h-full p-4",children:c.jsx(vS,{data:u,options:d})})]})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${s.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx($e,{className:"h-5 w-5 text-white"})}),"Record New Reading"]}),c.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-4",children:c.jsx(NS,{})})]}),c.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${s.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(Sr,{className:"h-5 w-5 text-white"})}),"Recent Readings"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.usageHistory.slice(0,10).map(h=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${s.text} text-lg`,children:[h.currentUnits.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-sm ${s.textSecondary} opacity-80`,children:["Previous: ",h.previousUnits.toFixed(2)," ",n()]}),c.jsx("p",{className:`text-xs ${s.textSecondary} mt-1 opacity-70`,children:h.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${h.usage>0?"bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200":"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200"}`,children:["-",h.usage.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${s.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",(h.usage*t.unitCost).toFixed(2)]})]})]})},h.id)),t.usageHistory.length===0&&c.jsxs("div",{className:"text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-gray-100 to-slate-200 w-fit mx-auto mb-4",children:c.jsx(De,{className:"h-12 w-12 text-gray-400"})}),c.jsx("p",{className:`text-sm ${s.textSecondary} opacity-80 font-medium`,children:"No usage records yet"}),c.jsx("p",{className:`text-xs ${s.textSecondary} opacity-60 mt-1`,children:"Record your first reading above to get started"})]})]})]})]}),c.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-8 border ${s.border} bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${s.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md",children:c.jsx(Ad,{className:"h-5 w-5 text-white"})}),"How Usage is Calculated"]}),c.jsx("div",{className:"p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm",children:c.jsxs("div",{className:"space-y-4 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100",children:[c.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${s.text} font-bold text-lg`,children:[t.previousUnits.toFixed(2)," ",n()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100",children:[c.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Current Reading:"}),c.jsxs("span",{className:`${s.text} font-bold text-lg`,children:[t.currentUnits.toFixed(2)," ",n()]})]}),c.jsx("div",{className:"border-t border-gradient-to-r from-gray-200 to-slate-200 my-4"}),c.jsxs("div",{className:"flex justify-between items-center p-4 bg-gradient-to-r from-rose-50 to-pink-50 rounded-lg border border-rose-100",children:[c.jsx("span",{className:`${s.text} font-semibold`,children:"Usage Since Last Recording:"}),c.jsxs("div",{className:"text-right",children:[c.jsxs("div",{className:`${s.text} text-sm opacity-70 mb-1`,children:[t.previousUnits.toFixed(2)," - ",t.currentUnits.toFixed(2)]}),c.jsxs("span",{className:`${s.text} font-bold text-xl bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent`,children:[e.toFixed(2)," ",n()]})]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg border border-violet-100",children:[c.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`${s.text} font-bold text-lg bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent`,children:[t.currencySymbol||"R",(e*t.unitCost).toFixed(2)]})]})]})})]})]})}function kS({history:t}){const{state:e,getDisplayUnitName:n}=Qe(),{theme:r}=_e();return t.length===0?null:c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsx("thead",{className:r.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Type"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Details"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:n()}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Amount"})]})}),c.jsx("tbody",{className:`${r.card} divide-y divide-gray-200`,children:t.map(i=>c.jsxs("tr",{className:"hover:bg-gray-50",children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"flex items-center",children:i.type==="purchase"?c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:c.jsx(Me,{className:"h-4 w-4 text-green-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[c.jsx(v1,{className:"mr-1 h-3 w-3"}),"Purchase"]})})]}):c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:c.jsx(De,{className:"h-4 w-4 text-red-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[c.jsx(x1,{className:"mr-1 h-3 w-3"}),"Usage"]})})]})})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:i.timestamp}),c.jsx("td",{className:`px-6 py-4 text-sm ${r.text}`,children:i.type==="purchase"?c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Electricity Purchase"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["@ ",e.currencySymbol||"R",i.unitCost.toFixed(2)," per ",n()]})]}):c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Usage Recording"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["From ",i.previousUnits.toFixed(2)," to ",i.currentUnits.toFixed(2)," ",n()]})]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:i.type==="purchase"?c.jsxs("span",{className:"text-green-600 font-medium",children:["+",i.units.toFixed(2)]}):c.jsxs("span",{className:"text-red-600 font-medium",children:["-",i.usage.toFixed(2)]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:i.type==="purchase"?c.jsxs("span",{className:"text-green-600 font-medium",children:["+",e.currencySymbol||"R",i.currency.toFixed(2)]}):c.jsxs("span",{className:"text-red-600 font-medium",children:["-",e.currencySymbol||"R",(i.usage*e.unitCost).toFixed(2)]})})]},`${i.type}-${i.id}`))})]})})}function CS(){const t=ia(),[e,n]=j.useState("all"),[r,i]=j.useState(""),{state:s,getDisplayUnitName:o,weeklyPurchaseTotal:a,monthlyPurchaseTotal:l,weeklyUsageTotal:u,monthlyUsageTotal:d}=Qe(),{theme:h}=_e(),f=[...s.purchases.map(p=>({...p,type:"purchase"})),...s.usageHistory.map(p=>({...p,type:"usage"}))].sort((p,y)=>new Date(y.date)-new Date(p.date)),m=f.filter(p=>{const y=e==="all"||p.type===e,w=!r||p.date.includes(r);return y&&w}),v=s.purchases.reduce((p,y)=>p+y.currency,0),x=s.usageHistory.reduce((p,y)=>p+y.usage,0),b=x*s.unitCost,g=[{id:"all",name:"All Activity",icon:Ys},{id:"purchase",name:"Purchases",icon:Me},{id:"usage",name:"Usage",icon:De}];return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${h.text}`,children:"History"}),c.jsx("p",{className:`mt-2 ${h.textSecondary}`,children:"View detailed logs of all purchases and usage patterns"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[c.jsx("div",{className:`${h.card} rounded-xl shadow-lg p-4 border ${h.border} bg-gradient-to-br from-emerald-50 to-green-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary}`,children:"This Week"}),c.jsxs("p",{className:"text-xl font-bold text-emerald-600",children:[s.currencySymbol,a.toFixed(2)]}),c.jsx("p",{className:"text-xs text-emerald-500",children:"Purchases"})]}),c.jsx("div",{className:"p-2 rounded-lg bg-emerald-100",children:c.jsx(Me,{className:"h-5 w-5 text-emerald-600"})})]})}),c.jsx("div",{className:`${h.card} rounded-xl shadow-lg p-4 border ${h.border} bg-gradient-to-br from-blue-50 to-indigo-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary}`,children:"This Month"}),c.jsxs("p",{className:"text-xl font-bold text-blue-600",children:[s.currencySymbol,l.toFixed(2)]}),c.jsx("p",{className:"text-xs text-blue-500",children:"Purchases"})]}),c.jsx("div",{className:"p-2 rounded-lg bg-blue-100",children:c.jsx(Me,{className:"h-5 w-5 text-blue-600"})})]})}),c.jsx("div",{className:`${h.card} rounded-xl shadow-lg p-4 border ${h.border} bg-gradient-to-br from-rose-50 to-pink-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary}`,children:"This Week"}),c.jsx("p",{className:"text-xl font-bold text-rose-600",children:u.toFixed(2)}),c.jsxs("p",{className:"text-xs text-rose-500",children:[o()," Used"]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-rose-100",children:c.jsx(De,{className:"h-5 w-5 text-rose-600"})})]})}),c.jsx("div",{className:`${h.card} rounded-xl shadow-lg p-4 border ${h.border} bg-gradient-to-br from-purple-50 to-violet-50`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary}`,children:"This Month"}),c.jsx("p",{className:"text-xl font-bold text-purple-600",children:d.toFixed(2)}),c.jsxs("p",{className:"text-xs text-purple-500",children:[o()," Used"]})]}),c.jsx("div",{className:"p-2 rounded-lg bg-purple-100",children:c.jsx(De,{className:"h-5 w-5 text-purple-600"})})]})})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[c.jsx("div",{className:`${h.card} rounded-2xl shadow-lg p-6 border ${h.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg",children:c.jsx(Me,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary} opacity-80`,children:"Total Spent"}),c.jsxs("p",{className:"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[s.currencySymbol||"R",v.toFixed(2)]}),c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})]})]})}),c.jsx("div",{className:`${h.card} rounded-2xl shadow-lg p-6 border ${h.border} bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-rose-400 to-pink-500 shadow-lg",children:c.jsx(De,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsxs("p",{className:`text-sm font-medium ${h.textSecondary} opacity-80`,children:["Total ",o()," Used"]}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:x.toFixed(2)}),c.jsx("p",{className:"text-xs text-rose-500 font-medium",children:o()})]})]})}),c.jsx("div",{className:`${h.card} rounded-2xl shadow-lg p-6 border ${h.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary} opacity-80`,children:"Usage Cost"}),c.jsxs("p",{className:"text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:[s.currencySymbol||"R",b.toFixed(2)]}),c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Total Cost"})]})]})}),c.jsx("div",{className:`${h.card} rounded-2xl shadow-lg p-6 border ${h.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg",children:c.jsx(Ys,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${h.textSecondary} opacity-80`,children:"Total Records"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:f.length}),c.jsx("p",{className:"text-xs text-blue-500 font-medium",children:"Entries"})]})]})})]}),c.jsxs("div",{className:`${h.card} rounded-2xl shadow-lg border ${h.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsx("div",{className:"p-8 bg-white/60 backdrop-blur-sm rounded-t-2xl",children:c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[c.jsx("div",{className:"flex space-x-1",children:g.map(p=>c.jsxs("button",{onClick:()=>n(p.id),className:`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${e===p.id?`${h.primary} text-white`:`${h.text} hover:${h.secondary}`}`,children:[c.jsx(p.icon,{className:"mr-2 h-4 w-4"}),p.name]},p.id))}),c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(y1,{className:`h-5 w-5 ${h.textSecondary}`}),c.jsx("input",{type:"date",value:r,onChange:p=>i(p.target.value),className:`px-3 py-2 border ${h.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${h.card} ${h.text}`}),r&&c.jsx("button",{onClick:()=>i(""),className:`px-3 py-2 text-sm ${h.textSecondary} hover:${h.text}`,children:"Clear"})]})]})}),c.jsx("div",{className:"border-t border-gray-200",children:c.jsx(kS,{history:m})})]}),m.length===0&&c.jsxs("div",{className:`${h.card} rounded-2xl shadow-lg p-16 text-center border ${h.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 rounded-full opacity-20 scale-110"}),c.jsx("div",{className:"relative p-6 rounded-2xl bg-gradient-to-br from-gray-100 to-slate-200 w-fit mx-auto",children:c.jsx(Ys,{className:"h-16 w-16 text-gray-400"})})]}),c.jsx("h3",{className:`mt-6 text-2xl font-bold ${h.text}`,children:"No history found"}),c.jsx("p",{className:`mt-3 ${h.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`,children:r?"No records found for the selected date. Try a different date or clear the filter.":"Start by making purchases or recording usage to see your history here."}),!r&&c.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row justify-center gap-4",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"💰"}),"Add Purchase"]})}),c.jsx("button",{onClick:()=>t("/usage"),className:"bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"⚡"}),"Record Usage"]})})]})]})]})}function MS(){const[t,e]=j.useState(!1),[n,r]=j.useState(!1),[i,s]=j.useState(!1),o=j.useRef(null),a=j.useRef(null),l=j.useRef(null),{currentTheme:u,setCurrentTheme:d,fontSize:h,setFontSize:f,fontFamily:m,setFontFamily:v,theme:x}=_e();return j.useEffect(()=>{function b(g){o.current&&!o.current.contains(g.target)&&e(!1),a.current&&!a.current.contains(g.target)&&r(!1),l.current&&!l.current.contains(g.target)&&s(!1)}return document.addEventListener("mousedown",b),()=>{document.removeEventListener("mousedown",b)}},[]),c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${x.text} mb-4 flex items-center`,children:[c.jsx(Op,{className:"mr-2 h-5 w-5"}),"Choose Theme"]}),c.jsxs("div",{className:"relative",ref:o,children:[c.jsxs("button",{onClick:()=>e(!t),className:`w-full p-4 ${x.card} border ${x.border} rounded-lg flex items-center justify-between hover:${x.secondary} transition-colors`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:`h-6 w-16 ${pt[u].gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-2 w-2 ${pt[u].primary} rounded`}),c.jsx("div",{className:`h-2 w-2 ${pt[u].accent} rounded`}),c.jsx("div",{className:`h-2 w-2 ${pt[u].secondary} rounded`})]})]}),c.jsx("span",{className:`text-lg font-medium ${x.text}`,children:pt[u].name})]}),c.jsx(Ia,{className:`h-5 w-5 ${x.textSecondary} transition-transform ${t?"rotate-180":""}`})]}),t&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${x.card} border ${x.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:c.jsx("div",{className:"grid grid-cols-1 gap-2 p-2",children:Object.entries(pt).map(([b,g])=>c.jsx("button",{onClick:()=>{d(b),e(!1)},className:`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${u===b?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2 flex-shrink-0",children:[c.jsx("div",{className:`h-8 w-20 ${g.gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-3 w-3 ${g.primary} rounded`}),c.jsx("div",{className:`h-3 w-3 ${g.accent} rounded`}),c.jsx("div",{className:`h-3 w-3 ${g.secondary} rounded`})]})]}),c.jsx("div",{className:"flex-1",children:c.jsx("p",{className:`text-sm font-medium ${x.text}`,children:g.name})}),u===b&&c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Aa,{className:"h-5 w-5 text-blue-500"})})]})},b))})})]})]}),c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${x.text} mb-4 flex items-center`,children:[c.jsx(C1,{className:"mr-2 h-5 w-5"}),"Font Family"]}),c.jsxs("div",{className:"relative",ref:a,children:[c.jsxs("button",{onClick:()=>r(!n),className:`w-full p-4 ${x.card} border ${x.border} rounded-lg flex items-center justify-between hover:${x.secondary} transition-colors`,children:[c.jsx("span",{className:`text-lg font-medium ${x.text}`,style:{fontFamily:Lt[m].fallback},children:Lt[m].name}),c.jsx(Ia,{className:`h-5 w-5 ${x.textSecondary} transition-transform ${n?"rotate-180":""}`})]}),n&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${x.card} border ${x.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:Object.entries(Lt).map(([b,g])=>c.jsx("button",{onClick:()=>{v(b),r(!1)},className:`w-full p-4 text-left hover:${x.secondary} transition-colors border-b ${x.border} last:border-b-0 ${m===b?x.secondary:""}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("span",{className:`text-lg font-medium ${x.text}`,style:{fontFamily:g.fallback},children:g.name}),m===b&&c.jsx(Aa,{className:"h-5 w-5 text-blue-500"})]})},b))})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${x.text} mb-4`,children:"Font Size"}),c.jsxs("div",{className:"relative",ref:l,children:[c.jsxs("button",{onClick:()=>s(!i),className:`w-full p-4 ${x.card} border ${x.border} rounded-lg flex items-center justify-between hover:${x.secondary} transition-colors`,children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`${sn[h].class} font-medium ${x.text}`,children:"Aa"}),c.jsxs("span",{className:`text-sm ${x.textSecondary}`,children:[sn[h].name," (",sn[h].size,")"]})]}),c.jsx(Ia,{className:`h-5 w-5 ${x.textSecondary} transition-transform ${i?"rotate-180":""}`})]}),i&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${x.card} border ${x.border} rounded-lg shadow-lg z-50`,children:Object.entries(sn).map(([b,g])=>c.jsx("button",{onClick:()=>{f(b),s(!1)},className:`w-full p-4 text-left hover:${x.secondary} transition-colors border-b ${x.border} last:border-b-0 ${h===b?x.secondary:""}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("div",{className:`${g.class} font-medium ${x.text}`,children:"Aa"}),c.jsxs("div",{children:[c.jsx("p",{className:`font-medium ${x.text}`,children:g.name}),c.jsx("p",{className:`text-sm ${x.textSecondary}`,children:g.size})]})]}),h===b&&c.jsx(Aa,{className:"h-5 w-5 text-blue-500"})]})},b))})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${x.text} mb-4`,children:"Preview"}),c.jsxs("div",{className:`p-6 ${x.card} rounded-lg border ${x.border} space-y-4`,children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("h4",{className:`text-xl font-bold ${x.text}`,children:"Sample Dashboard"}),c.jsx("div",{className:`px-3 py-1 ${x.primary} text-white rounded-full text-sm`,children:"Active"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[c.jsxs("div",{className:`p-4 ${x.secondary} rounded-lg`,children:[c.jsx("p",{className:`text-sm ${x.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-2xl font-bold ${x.text}`,children:"125.50"})]}),c.jsxs("div",{className:`p-4 ${x.secondary} rounded-lg`,children:[c.jsx("p",{className:`text-sm ${x.textSecondary}`,children:"Usage Today"}),c.jsx("p",{className:`text-2xl font-bold ${x.text}`,children:"8.25"})]}),c.jsxs("div",{className:`p-4 ${x.secondary} rounded-lg`,children:[c.jsx("p",{className:`text-sm ${x.textSecondary}`,children:"Cost"}),c.jsx("p",{className:`text-2xl font-bold ${x.text}`,children:"R20.63"})]})]}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{className:`px-4 py-2 ${x.primary} text-white rounded-lg text-sm`,children:"Primary Button"}),c.jsx("button",{className:`px-4 py-2 border ${x.border} ${x.text} rounded-lg text-sm`,children:"Secondary Button"})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${x.text} mb-4`,children:"Reset Appearance"}),c.jsxs("div",{className:"flex space-x-4",children:[c.jsx("button",{onClick:()=>{d("electric"),f("base"),v("inter"),e(!1),r(!1),s(!1)},className:`px-6 py-3 border ${x.border} ${x.text} rounded-lg hover:${x.secondary} transition-colors`,children:"Reset All to Default"}),c.jsx("button",{onClick:()=>{f("base"),v("inter"),r(!1),s(!1)},className:`px-6 py-3 border ${x.border} ${x.text} rounded-lg hover:${x.secondary} transition-colors`,children:"Reset Fonts Only"})]})]})]})}function PS(){const[t,e]=j.useState(!1),[n,r]=j.useState(!1),[i,s]=j.useState(!1),{state:o,factoryReset:a,dashboardReset:l}=Qe(),{theme:u}=_e(),d=async()=>{s(!0);try{a(),e(!1),alert("Factory reset completed successfully! The app will now restart.")}catch(f){console.error("Error during factory reset:",f),alert("Error during factory reset. Please try again.")}finally{s(!1)}},h=async()=>{s(!0);try{l(),r(!1),alert("Dashboard data reset successfully! Your history has been preserved.")}catch(f){console.error("Error during dashboard reset:",f),alert("Error during dashboard reset. Please try again.")}finally{s(!1)}};return c.jsxs("div",{className:"space-y-8",children:[c.jsx("div",{className:`p-6 border ${u.border} rounded-lg`,children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Dp,{className:"h-6 w-6 text-orange-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text}`,children:"Dashboard Data Reset"}),c.jsx("p",{className:`mt-2 text-sm ${u.textSecondary}`,children:"Reset current units and previous readings to zero. This will clear your dashboard data but preserve your purchase and usage history for reference."}),c.jsxs("div",{className:"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:[c.jsx("h4",{className:"text-sm font-medium text-orange-800 mb-2",children:"What will be reset:"}),c.jsxs("ul",{className:"text-xs text-orange-700 space-y-1",children:[c.jsx("li",{children:"• Current units will be set to 0"}),c.jsx("li",{children:"• Previous units will be set to 0"}),c.jsx("li",{children:"• Usage since last recording will be reset"})]}),c.jsx("h4",{className:"text-sm font-medium text-orange-800 mt-3 mb-2",children:"What will be preserved:"}),c.jsxs("ul",{className:"text-xs text-orange-700 space-y-1",children:[c.jsx("li",{children:"• All purchase history"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]})]}),n?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:"flex items-center p-3 bg-red-50 border border-red-200 rounded-lg",children:[c.jsx(_r,{className:"h-5 w-5 text-red-600 mr-2"}),c.jsx("span",{className:"text-sm text-red-800",children:"Are you sure? This action cannot be undone."})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:h,disabled:i,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Reset Dashboard"}),c.jsx("button",{onClick:()=>r(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>r(!0),className:"mt-4 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:"Reset Dashboard Data"})]})]})}),c.jsx("div",{className:"p-6 border border-red-200 rounded-lg bg-red-50",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(j1,{className:"h-6 w-6 text-red-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:"text-lg font-semibold text-red-800",children:"Factory Reset"}),c.jsx("p",{className:"mt-2 text-sm text-red-700",children:"Completely reset the app to its initial state. This will delete ALL data including purchases, usage history, and settings. You will need to set up the app again from scratch."}),c.jsxs("div",{className:"mt-4 p-3 bg-red-100 border border-red-300 rounded-lg",children:[c.jsx("h4",{className:"text-sm font-medium text-red-800 mb-2",children:"What will be deleted:"}),c.jsxs("ul",{className:"text-xs text-red-700 space-y-1",children:[c.jsx("li",{children:"• All purchase records"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Current and previous unit readings"}),c.jsx("li",{children:"• All settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]}),c.jsxs("div",{className:"mt-3 p-2 bg-red-200 border border-red-400 rounded text-xs text-red-800",children:[c.jsx("strong",{children:"Warning:"})," This action is irreversible. Make sure you have backed up any important data."]})]}),t?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:"flex items-center p-3 bg-red-100 border border-red-300 rounded-lg",children:[c.jsx(_r,{className:"h-5 w-5 text-red-700 mr-2"}),c.jsx("span",{className:"text-sm text-red-800 font-medium",children:"This will permanently delete ALL your data. Are you absolutely sure?"})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:d,disabled:i,className:"px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Delete Everything"}),c.jsx("button",{onClick:()=>e(!1),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>e(!0),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Factory Reset"})]})]})}),c.jsxs("div",{className:`p-6 ${u.card} border ${u.border} rounded-lg`,children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-4`,children:"Current Data Summary"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"App Data"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Current Units: ",o.currentUnits.toFixed(2)]}),c.jsxs("li",{children:["• Previous Units: ",o.previousUnits.toFixed(2)]}),c.jsxs("li",{children:["• Unit Cost: R",o.unitCost.toFixed(2)]}),c.jsxs("li",{children:["• Threshold: ",o.thresholdLimit.toFixed(2)," units"]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"History"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Purchases: ",o.purchases.length," records"]}),c.jsxs("li",{children:["• Usage Records: ",o.usageHistory.length," records"]}),c.jsxs("li",{children:["• Last Reset: ",o.lastResetDate?new Date(o.lastResetDate).toLocaleDateString():"Never"]}),c.jsxs("li",{children:["• App Initialized: ",o.isInitialized?"Yes":"No"]})]})]})]})]})]})}function ES(){var I,fe;const{state:t,updateSettings:e}=Qe(),{theme:n}=_e(),[r,i]=j.useState(t.unitCost.toString()),[s,o]=j.useState(t.thresholdLimit.toString()),[a,l]=j.useState(t.currency||"ZAR"),[u,d]=j.useState(t.customCurrencyName||""),[h,f]=j.useState(t.customCurrencySymbol||""),[m,v]=j.useState(t.unitName||"kWh"),[x,b]=j.useState(t.customUnitName||""),[g,p]=j.useState(t.notificationsEnabled||!1),[y,w]=j.useState(t.notificationTime||"18:00"),[_,S]=j.useState(!1),N=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CUSTOM",name:"Custom Currency",symbol:"C"}],k=[{value:"kWh",label:"kWh (Kilowatt Hours)"},{value:"Units",label:"Units"},{value:"custom",label:"Custom"}],T=async R=>{R.preventDefault(),S(!0);try{const A=parseFloat(r),B=parseFloat(s);if(isNaN(A)||A<=0){alert("Please enter a valid unit cost (greater than 0)");return}if(isNaN(B)||B<0){alert("Please enter a valid threshold limit (0 or greater)");return}if(m==="custom"&&!x.trim()){alert("Please enter a custom unit name");return}if(a==="CUSTOM"&&(!u.trim()||!h.trim())){alert("Please enter both custom currency name and symbol");return}const P=N.find(F=>F.code===a),D=a==="CUSTOM"?h:(P==null?void 0:P.symbol)||"R";e({unitCost:A,thresholdLimit:B,currency:a,currencySymbol:D,customCurrencyName:a==="CUSTOM"?u.trim():"",customCurrencySymbol:a==="CUSTOM"?h.trim():"",unitName:m,customUnitName:m==="custom"?x.trim():"",notificationsEnabled:g,notificationTime:y}),alert("Settings saved successfully!")}catch(A){console.error("Error saving settings:",A),alert("Error saving settings. Please try again.")}finally{S(!1)}},M=[{id:"general",title:"General Settings",icon:Xs,description:"Configure unit costs and usage thresholds"},{id:"appearance",title:"Appearance",icon:Op,description:"Customize themes, fonts, and colors"},{id:"reset",title:"Reset Options",icon:Dp,description:"Factory reset and data management"}],[E,$]=j.useState("general");return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${n.text}`,children:"Settings"}),c.jsx(ua,{title:"Settings Guide",content:"Configure your unit cost (how much you pay per unit), set low units warning threshold, choose your currency and unit names, customize appearance, and set up daily notifications. All settings are saved automatically.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${n.textSecondary}`,children:"Configure your app preferences and manage your data"})]}),c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg border ${n.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsx("div",{className:"flex border-b border-gray-200",children:M.map(R=>c.jsx("button",{onClick:()=>$(R.id),className:`flex-1 px-6 py-4 text-left transition-colors ${E===R.id?`${n.primary} text-white`:`${n.text} hover:${n.secondary}`}`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx(R.icon,{className:"mr-3 h-5 w-5"}),c.jsxs("div",{children:[c.jsx("h3",{className:"font-medium",children:R.title}),c.jsx("p",{className:`text-sm ${E===R.id?"text-white opacity-80":n.textSecondary}`,children:R.description})]})]})},R.id))}),c.jsxs("div",{className:"p-6",children:[E==="general"&&c.jsx("div",{className:"space-y-6",children:c.jsxs("form",{onSubmit:T,className:"space-y-6",children:[c.jsxs("div",{className:"p-6 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl border border-emerald-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500 mr-3",children:c.jsx(b1,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Currency Settings"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currency",className:`block text-sm font-semibold ${n.text} mb-3`,children:"💰 Currency"}),c.jsx("select",{id:"currency",value:a,onChange:R=>l(R.target.value),className:`w-full px-4 py-4 border-4 border-emerald-300 rounded-xl focus:ring-4 focus:ring-emerald-500 focus:border-emerald-600 bg-white ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-emerald-400`,children:N.map(R=>c.jsxs("option",{value:R.code,children:[R.symbol," - ",R.name," (",R.code,")"]},R.code))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 Select your preferred currency for cost calculations"}),a==="CUSTOM"&&c.jsxs("div",{className:"mt-4 space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencyName",className:`block text-sm font-semibold ${n.text} mb-2`,children:"🏷️ Custom Currency Name"}),c.jsx("input",{type:"text",id:"customCurrencyName",value:u,onChange:R=>d(R.target.value),placeholder:"Enter currency name (e.g., Bitcoin, Credits, Points)",className:`w-full px-4 py-3 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 bg-gradient-to-br from-purple-50 to-pink-50 ${n.text} placeholder-purple-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400`,required:a==="CUSTOM"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencySymbol",className:`block text-sm font-semibold ${n.text} mb-2`,children:"💰 Custom Currency Symbol"}),c.jsx("input",{type:"text",id:"customCurrencySymbol",value:h,onChange:R=>f(R.target.value),placeholder:"Enter symbol (e.g., ₿, Cr, Pts)",maxLength:"5",className:`w-full px-4 py-3 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 bg-gradient-to-br from-purple-50 to-pink-50 ${n.text} placeholder-purple-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400`,required:a==="CUSTOM"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['💰 This symbol will be displayed with all amounts (e.g., "',h||"Cr",'100.00")']})]})]})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3",children:c.jsx($e,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Unit Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"unitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⚡ Unit Name"}),c.jsx("select",{id:"unitName",value:m,onChange:R=>v(R.target.value),className:`w-full px-4 py-4 border-4 border-blue-300 rounded-xl focus:ring-4 focus:ring-blue-500 focus:border-blue-600 bg-white ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-400`,children:k.map(R=>c.jsx("option",{value:R.value,children:R.label},R.value))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚡ Choose how your units are displayed throughout the app"})]}),m==="custom"&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customUnitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"🎯 Custom Unit Name"}),c.jsx("input",{type:"text",id:"customUnitName",value:x,onChange:R=>b(R.target.value),placeholder:"Enter custom unit name (e.g., Donkey, Credits, Points)",className:`w-full px-4 py-4 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 bg-gradient-to-br from-purple-50 to-pink-50 ${n.text} placeholder-purple-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400`,required:m==="custom"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['🎯 This name will be used everywhere (e.g., "Cost per ',x||"YourUnit",'")']})]})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Me,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Cost Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-semibold ${n.text} mb-3`,children:["💵 Cost per ",m==="custom"?x||"Unit":m]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500",children:c.jsx(Me,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"unitCost",value:r,onChange:R=>i(R.target.value),step:"0.01",min:"0.01",placeholder:"Enter cost per unit",className:`w-full pl-12 pr-4 py-4 border-4 border-violet-300 rounded-xl focus:ring-4 focus:ring-violet-500 focus:border-violet-600 bg-white ${n.text} placeholder-violet-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-violet-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 This is used to calculate the cost of your electricity usage"})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl border border-amber-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3",children:c.jsx(_r,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Alert Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"thresholdLimit",className:`block text-sm font-semibold ${n.text} mb-3`,children:["⚠️ Low ",m==="custom"?x||"Units":m," Warning Threshold"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500",children:c.jsx(_r,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"thresholdLimit",value:s,onChange:R=>o(R.target.value),step:"0.01",min:"0",placeholder:"Enter low units warning threshold",className:`w-full pl-12 pr-4 py-4 border-4 border-amber-300 rounded-xl focus:ring-4 focus:ring-amber-500 focus:border-amber-600 bg-white ${n.text} placeholder-amber-500 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-amber-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚠️ You'll receive a warning when your remaining units drop below this threshold"})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl border border-gray-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3",children:c.jsx(Xs,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Current Settings Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Currency:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.currency==="CUSTOM"?`${t.customCurrencySymbol||"C"} - ${t.customCurrencyName||"Custom Currency"}`:`${((I=N.find(R=>R.code===(t.currency||"ZAR")))==null?void 0:I.symbol)||"R"} - ${((fe=N.find(R=>R.code===(t.currency||"ZAR")))==null?void 0:fe.name)||"South African Rand"}`})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Name:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)," per ",t.unitName==="custom"?t.customUnitName||"Unit":t.unitName||"kWh"]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Low Units Warning:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.thresholdLimit.toFixed(2)," ",t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Last Reset:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.lastResetDate?new Date(t.lastResetDate).toLocaleDateString():"Never"})]})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl border border-indigo-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 mr-3",children:c.jsx(_r,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Notification Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("label",{className:`text-sm font-semibold ${n.text}`,children:"🔔 Daily Usage Reminders"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-80 mt-1`,children:"Get reminded to record your electricity usage every day"})]}),c.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[c.jsx("input",{type:"checkbox",checked:g,onChange:R=>p(R.target.checked),className:"sr-only peer","aria-label":"Enable daily usage reminder notifications"}),c.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"})]})]}),g&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"notificationTime",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⏰ Reminder Time"}),c.jsx("input",{type:"time",id:"notificationTime",value:y,onChange:R=>w(R.target.value),className:`w-full px-4 py-4 border-4 border-indigo-300 rounded-xl focus:ring-4 focus:ring-indigo-500 focus:border-indigo-600 bg-white ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-indigo-400`}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⏰ You'll receive a notification at this time every day"})]})]})]}),c.jsx("button",{type:"submit",disabled:_,className:"w-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-600 hover:via-indigo-600 hover:to-purple-700 transition-all duration-300 focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:_?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Saving Settings..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Xs,{className:"h-5 w-5"}),"Save Settings"]})})})]})}),E==="appearance"&&c.jsx("div",{className:"space-y-6",children:c.jsx(MS,{})}),E==="reset"&&c.jsx("div",{className:"space-y-6",children:c.jsx(PS,{})})]})]})]})}function TS(){const[t,e]=j.useState(""),[n,r]=j.useState(""),[i,s]=j.useState(""),{initializeApp:o,state:a}=Qe(),{theme:l}=_e(),u=d=>{d.preventDefault(),s("");const h=parseFloat(t),f=parseFloat(n);if(isNaN(h)||h<0){s("Please enter a valid number of units (0 or greater)");return}if(isNaN(f)||f<=0){s("Please enter a valid cost per unit (greater than 0)");return}o(h,f)};return c.jsx("div",{className:`min-h-screen flex items-center justify-center ${l.background} px-4 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:c.jsxs("div",{className:`max-w-lg w-full ${l.card} rounded-2xl shadow-2xl p-8 border ${l.border} bg-white/80 backdrop-blur-sm`,children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"flex justify-center mb-4",children:c.jsx(Po,{size:"xl"})}),c.jsx("h1",{className:`text-3xl font-bold ${l.text} mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`,children:"Welcome to Prepaid Meter App"}),c.jsx("p",{className:`${l.textSecondary} text-lg`,children:"Let's get started by setting up your initial meter reading and cost settings"})]}),c.jsxs("form",{onSubmit:u,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${l.text} mb-2`,children:"Initial Unit Value"}),c.jsx("input",{type:"number",id:"initialUnits",value:t,onChange:d=>e(d.target.value),step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-4 py-4 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} text-lg transition-all duration-200 hover:border-blue-300`,required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-medium ${l.text} mb-2`,children:["Cost per Unit (",a.currencySymbol||"R",")"]}),c.jsx("input",{type:"number",id:"unitCost",value:n,onChange:d=>r(d.target.value),step:"0.01",min:"0.01",placeholder:"Enter cost per unit (e.g., 2.50)",className:`w-full px-4 py-4 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} text-lg transition-all duration-200 hover:border-blue-300`,required:!0})]}),i&&c.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:c.jsx("p",{className:"text-sm text-red-600",children:i})}),c.jsxs("div",{className:`p-4 ${l.secondary} rounded-lg`,children:[c.jsx("h3",{className:`font-medium ${l.text} mb-2`,children:"What happens next?"}),c.jsxs("ul",{className:`text-sm ${l.textSecondary} space-y-1`,children:[c.jsx("li",{children:"• This will be your starting point for tracking usage"}),c.jsx("li",{children:"• The cost setting will be used for purchase calculations"}),c.jsx("li",{children:"• You can add purchases to increase your units"}),c.jsx("li",{children:"• Track your daily electricity consumption"}),c.jsx("li",{children:"• Set up warnings and monthly resets"})]})]}),c.jsx("button",{type:"submit",className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:"🚀 Initialize App"})]}),c.jsx("div",{className:"mt-6 text-center",children:c.jsx("p",{className:`text-xs ${l.textSecondary}`,children:"You can always change these values later in Settings"})})]})})}function RS(){const[t,e]=j.useState(!1),{state:n}=Qe(),{theme:r}=_e();return n.isInitialized?c.jsxs("div",{className:`flex h-screen ${r.background}`,children:[c.jsx(P1,{isOpen:t,onClose:()=>e(!1)}),c.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[c.jsx(M1,{onMenuClick:()=>e(!0)}),c.jsx("main",{className:"flex-1 overflow-x-hidden overflow-y-auto p-4",children:c.jsx("div",{className:"max-w-7xl mx-auto",children:c.jsxs(Vv,{children:[c.jsx(On,{path:"/",element:c.jsx(Zh,{})}),c.jsx(On,{path:"/dashboard",element:c.jsx(Zh,{})}),c.jsx(On,{path:"/purchases",element:c.jsx(_S,{})}),c.jsx(On,{path:"/usage",element:c.jsx(jS,{})}),c.jsx(On,{path:"/history",element:c.jsx(CS,{})}),c.jsx(On,{path:"/settings",element:c.jsx(ES,{})})]})})})]}),t&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>e(!1)})]}):c.jsx(TS,{})}function OS(){return c.jsx(Jv,{children:c.jsx(m1,{children:c.jsx(f1,{children:c.jsx(RS,{})})})})}Ja.createRoot(document.getElementById("root")).render(c.jsx(gt.StrictMode,{children:c.jsx(OS,{})}));
