# 📸 Screenshot Requirements for Google Play Store

## 📱 **Phone Screenshots** (Required: 2-8 screenshots)

### **Dimensions**: 
- Minimum: 320px on shortest side
- Recommended: 1080x1920px (9:16 aspect ratio)
- Format: PNG or JPEG

### **Recommended Screenshots to Take:**

1. **Dashboard Overview** 📊
   - Show the main dashboard with usage dial
   - Display current units and weekly/monthly totals
   - Include the modern logo and quick action buttons
   - Caption: "Beautiful dashboard with real-time usage tracking"

2. **Purchase Calculator** 💰
   - Show the purchase form with live calculation
   - Display currency input and units preview
   - Show the real-time calculation preview section
   - Caption: "Smart purchase calculator with instant unit preview"

3. **Usage Tracking** ⚡
   - Display the usage page with colorful chart
   - Show current reading input and usage statistics
   - Include weekly/monthly usage totals
   - Caption: "Track daily usage with beautiful analytics"

4. **History & Analytics** 📈
   - Show the history page with transaction logs
   - Display weekly and monthly totals cards
   - Include filtering options and detailed records
   - Caption: "Complete history with detailed analytics"

5. **Settings & Customization** ⚙️
   - Show the settings page with theme options
   - Display currency and unit configuration
   - Include notification settings
   - Caption: "Extensive customization and settings"

6. **Themes Showcase** 🎨
   - Show the app in different themes (Dark Mode, Electric Blue)
   - Display theme selector with live preview
   - Caption: "10 beautiful themes to choose from"

## 📱 **Tablet Screenshots** (Optional but recommended: 1-8 screenshots)

### **Dimensions**:
- 7-inch: 1024x600px or 1200x800px
- 10-inch: 1920x1200px or 2560x1600px
- Format: PNG or JPEG

### **Tablet-Specific Screenshots:**
1. **Dashboard in Landscape** - Show how the app adapts to larger screens
2. **Side-by-side Layout** - Demonstrate tablet-optimized layouts
3. **Enhanced Charts** - Show larger, more detailed visualizations

## 🎨 **Feature Graphic** (Required: 1 graphic)

### **Dimensions**: 1024x500px
### **Format**: PNG or JPEG
### **Content Ideas**:
- App logo with lightning bolt
- Text: "PREPAID USER - ELECTRICITY"
- Subtitle: "Smart Electricity Usage Tracking"
- Background: Gradient matching app theme
- Include key features: "Real-time Tracking • Smart Calculator • Beautiful Charts"

## 🔧 **App Icon** (Required)

### **Dimensions**: 512x512px
### **Format**: PNG with transparency
### **Design**: 
- Lightning bolt icon with modern gradient
- Transparent background
- High contrast and visibility
- Matches the app's Logo component design

## 📝 **Screenshot Tips**

### **Best Practices**:
1. **Use Real Data**: Show realistic electricity readings and usage
2. **Highlight Key Features**: Focus on unique selling points
3. **Show Different States**: Include both light and dark themes
4. **Mobile-First**: Ensure screenshots look good on small screens
5. **Clear Text**: Make sure all text is readable
6. **Consistent Branding**: Use the same color scheme throughout

### **What to Avoid**:
- Blurry or low-resolution images
- Screenshots with placeholder text
- Images that don't represent actual app functionality
- Screenshots with personal information
- Inconsistent UI states

### **Tools for Taking Screenshots**:
- **Android Studio Emulator**: Built-in screenshot tool
- **Physical Device**: Use device screenshot function
- **Browser DevTools**: For web version screenshots
- **Design Tools**: Figma, Sketch for mockups

## 📐 **Technical Specifications**

### **File Requirements**:
- **Max file size**: 8MB per image
- **Supported formats**: PNG, JPEG
- **Color space**: sRGB
- **Compression**: High quality, minimal compression

### **Content Guidelines**:
- No text overlay on screenshots (use captions instead)
- Show actual app functionality
- Avoid showing device frames
- Use high-contrast, readable content
- Ensure screenshots represent current app version

## 🚀 **Quick Checklist**

- [ ] 2-8 phone screenshots (1080x1920px recommended)
- [ ] 1-8 tablet screenshots (optional)
- [ ] 1 feature graphic (1024x500px)
- [ ] 1 app icon (512x512px)
- [ ] All images under 8MB
- [ ] PNG or JPEG format
- [ ] High quality, no blur
- [ ] Represents actual app functionality
- [ ] Consistent with app branding

## 📱 **Screenshot Sequence Recommendation**

1. **Dashboard** (Hero shot - most important)
2. **Purchase Calculator** (Key feature)
3. **Usage Tracking** (Core functionality)
4. **Beautiful Charts** (Visual appeal)
5. **Settings/Themes** (Customization)
6. **History** (Data management)

This sequence tells a complete story of the app's capabilities and user journey.
