import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> as Router } from 'react-router-dom'
import { AppProvider } from './context/AppContext'
import { ThemeProvider } from './context/ThemeContext'
import Layout from './components/Layout/Layout'

function App() {
  return (
    <Router>
      <ThemeProvider>
        <AppProvider>
          <Layout />
        </AppProvider>
      </ThemeProvider>
    </Router>
  )
}

export default App
