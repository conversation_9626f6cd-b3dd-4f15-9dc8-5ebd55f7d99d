import React, { useEffect, useRef } from 'react'
import { Doughnut } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  ArcElement,
  Toolt<PERSON>,
  Legend,
} from 'chart.js'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt, HiTrendingDown, HiChartBar } from 'react-icons/hi'

ChartJS.register(ArcElement, Tooltip, Legend)

function UsageDial() {
  const { state, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme } = useTheme()
  const chartRef = useRef(null)

  const remainingUnits = state.currentUnits
  const usedUnits = usageSinceLastRecording
  const totalUnits = remainingUnits + usedUnits
  const usagePercentage = totalUnits > 0 ? (usedUnits / totalUnits) * 100 : 0

  // Create advanced gradient colors with modern styling
  useEffect(() => {
    const chart = chartRef.current
    if (chart) {
      const ctx = chart.ctx

      // Advanced gradient for remaining units (vibrant blue to purple)
      const remainingGradient = ctx.createRadialGradient(200, 200, 50, 200, 200, 150)
      remainingGradient.addColorStop(0, '#667eea') // Light purple
      remainingGradient.addColorStop(0.3, '#764ba2') // Medium purple
      remainingGradient.addColorStop(0.6, '#667eea') // Light purple
      remainingGradient.addColorStop(1, '#f093fb') // Pink

      // Advanced gradient for used units (warm orange to red)
      const usedGradient = ctx.createRadialGradient(200, 200, 50, 200, 200, 150)
      usedGradient.addColorStop(0, '#ff9a9e') // Light coral
      usedGradient.addColorStop(0.3, '#fecfef') // Light pink
      usedGradient.addColorStop(0.6, '#fecfef') // Light pink
      usedGradient.addColorStop(1, '#ffc3a0') // Peach

      chart.data.datasets[0].backgroundColor = [remainingGradient, usedGradient]
      chart.update()
    }
  }, [remainingUnits, usedUnits])

  const data = {
    labels: [`Remaining ${getDisplayUnitName()}`, `Used ${getDisplayUnitName()}`],
    datasets: [
      {
        data: [remainingUnits, usedUnits],
        backgroundColor: [
          'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Modern gradient fallback
          'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)',
        ],
        borderColor: [
          'rgba(255, 255, 255, 0.9)',
          'rgba(255, 255, 255, 0.9)',
        ],
        borderWidth: 4,
        cutout: '78%',
        borderRadius: 12,
        borderJoinStyle: 'round',
        hoverBorderWidth: 6,
        hoverBorderColor: [
          'rgba(255, 255, 255, 1)',
          'rgba(255, 255, 255, 1)',
        ],
        shadowOffsetX: 3,
        shadowOffsetY: 3,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // We'll create custom legend
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.parsed
            const percentage = totalUnits > 0 ? ((value / totalUnits) * 100).toFixed(1) : 0
            return `${label}: ${value.toFixed(2)} (${percentage}%)`
          }
        }
      }
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
      easing: 'easeInOutCubic',
      delay: (context) => context.dataIndex * 200,
    },
    interaction: {
      intersect: false,
      mode: 'nearest',
    },
    elements: {
      arc: {
        borderWidth: 4,
        hoverBorderWidth: 6,
        borderSkipped: false,
        borderAlign: 'inner',
      }
    },
    layout: {
      padding: {
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }
    }
  }

  return (
    <div className="relative">
      {/* Modern Chart Container with Glassmorphism */}
      <div className="relative h-96 p-8">
        {/* Multi-layer background with modern gradients */}
        <div className={`absolute inset-0 ${theme.isDark ? 'bg-gradient-to-br from-indigo-900/40 via-purple-900/30 to-pink-900/40' : 'bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100'} rounded-3xl opacity-60`}></div>
        <div className={`absolute inset-1 ${theme.isDark ? 'bg-gradient-to-tr from-gray-800/90 via-gray-800/70 to-gray-800/50 border-gray-600/20' : 'bg-gradient-to-tr from-white/90 via-white/70 to-white/50 border-white/20'} rounded-2xl backdrop-blur-lg shadow-2xl`}></div>
        <div className={`absolute inset-3 ${theme.isDark ? 'bg-gradient-to-bl from-gray-700/40' : 'bg-gradient-to-bl from-white/40'} to-transparent rounded-xl`}></div>

        {/* Floating orbs for ambient effect */}
        <div className="absolute top-4 left-4 w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-xl animate-pulse"></div>
        <div className="absolute bottom-4 right-4 w-20 h-20 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-15 blur-2xl animate-pulse" style={{animationDelay: '1s'}}></div>

        {/* Chart */}
        <div className="relative h-full flex items-center justify-center">
          <div className="w-full h-full max-w-sm max-h-sm">
            <Doughnut ref={chartRef} data={data} options={options} />
          </div>

          {/* Enhanced Center Content with Circular Design */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              {/* Outer glow ring */}
              <div className="absolute inset-0 w-40 h-40 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>

              {/* Main circular container */}
              <div className={`relative w-40 h-40 ${theme.isDark ? 'bg-gray-800/40 border-gray-600/30' : 'bg-white/40 border-white/30'} backdrop-blur-lg rounded-full shadow-2xl flex items-center justify-center`}>
                {/* Inner gradient ring */}
                <div className={`absolute inset-2 ${theme.isDark ? 'bg-gradient-to-br from-gray-700/60 via-gray-700/40 to-gray-700/20' : 'bg-gradient-to-br from-white/60 via-white/40 to-white/20'} rounded-full`}></div>

                {/* Content container */}
                <div className="relative text-center z-10">
                  {/* Animated icon with glow effect */}
                  <div className="mb-2 flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"></div>
                      <HiLightningBolt className="relative h-8 w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce" style={{animationDuration: '2s'}} />
                    </div>
                  </div>

                  {/* Main value with enhanced gradient text */}
                  <div className="relative mb-1">
                    <div className="text-3xl font-black bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent drop-shadow-lg">
                      {remainingUnits.toFixed(1)}
                    </div>
                    <div className={`text-xs font-semibold ${theme.isDark ? 'text-gray-300' : 'text-gray-600'} mt-1 tracking-wide`}>
                      {getDisplayUnitName()} Left
                    </div>
                  </div>

                  {/* Usage percentage with enhanced styling */}
                  <div className="mt-1">
                    <div className={`text-sm font-bold tracking-tight ${
                      usagePercentage > 70 ? 'text-red-500 drop-shadow-lg' :
                      usagePercentage > 40 ? 'text-amber-500 drop-shadow-lg' : 'text-emerald-500 drop-shadow-lg'
                    }`}>
                      {usagePercentage.toFixed(1)}% Used
                    </div>
                  </div>
                </div>

                {/* Decorative inner elements */}
                <div className="absolute top-3 right-3 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-pulse"></div>
                <div className="absolute bottom-3 left-3 w-1.5 h-1.5 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Legend Cards with Modern Styling */}
      <div className="mt-8 grid grid-cols-2 gap-6">
        <div className={`group relative overflow-hidden rounded-2xl ${theme.isDark ? 'bg-gradient-to-br from-indigo-900/30 via-blue-900/30 to-purple-900/30 border-gray-600/20' : 'bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 border-white/20'} p-6 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105`}>
          <div className={`absolute inset-0 ${theme.isDark ? 'bg-gradient-to-br from-gray-700/40' : 'bg-gradient-to-br from-white/40'} to-transparent`}></div>
          <div className="relative flex items-center space-x-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"></div>
              <div className="relative p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                <HiChartBar className="h-6 w-6 text-white" />
              </div>
            </div>
            <div>
              <div className="text-2xl font-black bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                {remainingUnits.toFixed(2)}
              </div>
              <div className={`text-sm font-semibold ${theme.isDark ? 'text-gray-300' : 'text-gray-600'} tracking-wide`}>
                {getDisplayUnitName()} Available
              </div>
            </div>
          </div>
        </div>

        <div className={`group relative overflow-hidden rounded-2xl ${theme.isDark ? 'bg-gradient-to-br from-orange-900/30 via-pink-900/30 to-red-900/30 border-gray-600/20' : 'bg-gradient-to-br from-orange-50 via-pink-50 to-red-50 border-white/20'} p-6 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105`}>
          <div className={`absolute inset-0 ${theme.isDark ? 'bg-gradient-to-br from-gray-700/40' : 'bg-gradient-to-br from-white/40'} to-transparent`}></div>
          <div className="relative flex items-center space-x-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"></div>
              <div className="relative p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg">
                <HiTrendingDown className="h-6 w-6 text-white" />
              </div>
            </div>
            <div>
              <div className="text-2xl font-black bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                {usedUnits.toFixed(2)}
              </div>
              <div className={`text-sm font-semibold ${theme.isDark ? 'text-gray-300' : 'text-gray-600'} tracking-wide`}>
                {getDisplayUnitName()} Consumed
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Cost Summary with Glassmorphism */}
      <div className={`mt-8 relative overflow-hidden rounded-2xl ${theme.isDark ? 'bg-gradient-to-br from-slate-800/50 via-gray-800/50 to-zinc-800/50 border-gray-600/30' : 'bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 border-white/30'} p-6 shadow-xl backdrop-blur-sm`}>
        <div className={`absolute inset-0 ${theme.isDark ? 'bg-gradient-to-br from-gray-700/60 to-gray-700/20' : 'bg-gradient-to-br from-white/60 to-white/20'}`}></div>
        <div className="relative flex justify-between items-center">
          <div className="space-y-2">
            <div className={`text-sm font-semibold ${theme.isDark ? 'text-gray-400' : 'text-gray-500'} tracking-wider uppercase`}>Total Cost</div>
            <div className={`text-3xl font-black ${theme.isDark ? 'bg-gradient-to-r from-slate-300 via-gray-200 to-zinc-300' : 'bg-gradient-to-r from-slate-700 via-gray-800 to-zinc-700'} bg-clip-text text-transparent`}>
              {state.currencySymbol || 'R'}{(usedUnits * state.unitCost).toFixed(2)}
            </div>
          </div>
          <div className="text-right space-y-2">
            <div className={`text-sm font-semibold ${theme.isDark ? 'text-gray-400' : 'text-gray-500'} tracking-wider uppercase`}>Rate</div>
            <div className={`text-xl font-bold ${theme.isDark ? 'bg-gradient-to-r from-slate-400 to-gray-300' : 'bg-gradient-to-r from-slate-600 to-gray-700'} bg-clip-text text-transparent`}>
              {state.currencySymbol || 'R'}{state.unitCost.toFixed(2)}/{getDisplayUnitName()}
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-2 right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-sm"></div>
        <div className="absolute bottom-2 left-2 w-6 h-6 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-15 blur-sm"></div>
      </div>

      {/* Modern Progress Bar with Enhanced Styling */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <span className={`text-sm font-bold ${theme.isDark ? 'text-gray-300' : 'text-gray-700'} tracking-wide uppercase`}>Usage Progress</span>
          <span className={`text-lg font-black px-3 py-1 rounded-full ${
            usagePercentage > 70 ? 'bg-gradient-to-r from-red-100 to-red-200 text-red-700' :
            usagePercentage > 40 ? 'bg-gradient-to-r from-amber-100 to-yellow-200 text-amber-700' :
            'bg-gradient-to-r from-emerald-100 to-green-200 text-emerald-700'
          }`}>
            {usagePercentage.toFixed(1)}%
          </span>
        </div>
        <div className="relative">
          {/* Background track with glassmorphism */}
          <div className={`w-full ${theme.isDark ? 'bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 border-gray-500/50' : 'bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 border-white/50'} rounded-full h-4 shadow-inner backdrop-blur-sm`}>
            {/* Progress fill with enhanced gradients */}
            <div
              className={`h-4 rounded-full transition-all duration-1500 ease-out shadow-lg ${
                usagePercentage > 70 ? 'bg-gradient-to-r from-red-400 via-red-500 to-red-600' :
                usagePercentage > 40 ? 'bg-gradient-to-r from-amber-400 via-yellow-500 to-orange-500' :
                'bg-gradient-to-r from-emerald-400 via-green-500 to-teal-500'
              }`}
              style={{
                width: `${Math.min(usagePercentage, 100)}%`
              }}
            />
          </div>

          {/* Multiple glow effects for depth */}
          <div
            className={`absolute top-0 h-4 rounded-full opacity-60 blur-sm transition-all duration-1500 ${
              usagePercentage > 70 ? 'bg-gradient-to-r from-red-300 to-red-500' :
              usagePercentage > 40 ? 'bg-gradient-to-r from-amber-300 to-orange-500' :
              'bg-gradient-to-r from-emerald-300 to-green-500'
            }`}
            style={{
              width: `${Math.min(usagePercentage, 100)}%`
            }}
          />

          {/* Outer glow */}
          <div
            className={`absolute top-0 h-4 rounded-full opacity-30 blur-md transition-all duration-1500 ${
              usagePercentage > 70 ? 'bg-gradient-to-r from-red-200 to-red-400' :
              usagePercentage > 40 ? 'bg-gradient-to-r from-amber-200 to-orange-400' :
              'bg-gradient-to-r from-emerald-200 to-green-400'
            }`}
            style={{
              width: `${Math.min(usagePercentage, 100)}%`
            }}
          />

          {/* Animated shimmer effect */}
          {usagePercentage > 0 && (
            <div
              className="absolute top-0 h-4 rounded-full bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse"
              style={{
                width: `${Math.min(usagePercentage, 100)}%`
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default UsageDial
