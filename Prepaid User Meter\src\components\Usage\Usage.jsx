import React, { useState } from 'react'
import { Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import UsageForm from './UsageForm'
import { HiTrendingUp, HiLightningBolt, HiCalendar, HiClock, HiChartBar } from 'react-icons/hi'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

function Usage() {
  const { state, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme } = useTheme()

  const totalUsage = state.usageHistory.reduce((total, entry) => total + entry.usage, 0)
  const averageUsage = state.usageHistory.length > 0 ? totalUsage / state.usageHistory.length : 0

  // Prepare data for the bar chart
  const last7Days = state.usageHistory.slice(-7).reverse()
  const chartData = {
    labels: last7Days.length > 0
      ? last7Days.map((entry, index) => {
          const date = new Date(entry.timestamp)
          return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        })
      : ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: `Daily Usage (${getDisplayUnitName()})`,
        data: last7Days.length > 0
          ? last7Days.map(entry => entry.usage)
          : [12.5, 15.2, 8.7, 22.1, 18.9, 14.3, 16.8],
        backgroundColor: [
          'rgba(99, 102, 241, 0.8)',   // Indigo
          'rgba(139, 92, 246, 0.8)',   // Violet
          'rgba(236, 72, 153, 0.8)',   // Pink
          'rgba(34, 197, 94, 0.8)',    // Green
          'rgba(251, 146, 60, 0.8)',   // Orange
          'rgba(14, 165, 233, 0.8)',   // Sky
          'rgba(168, 85, 247, 0.8)',   // Purple
        ],
        borderColor: [
          'rgba(99, 102, 241, 1)',
          'rgba(139, 92, 246, 1)',
          'rgba(236, 72, 153, 1)',
          'rgba(34, 197, 94, 1)',
          'rgba(251, 146, 60, 1)',
          'rgba(14, 165, 233, 1)',
          'rgba(168, 85, 247, 1)',
        ],
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Daily Usage Trend',
        font: {
          size: 16,
          weight: 'bold',
        },
        color: theme.text === 'text-gray-900' ? '#1f2937' : '#f9fafb',
        padding: 20,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            return `Usage: ${context.parsed.y.toFixed(2)} ${getDisplayUnitName()}`
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(156, 163, 175, 0.2)',
          drawBorder: false,
        },
        ticks: {
          color: theme.textSecondary === 'text-gray-600' ? '#6b7280' : '#9ca3af',
          font: {
            size: 12,
          },
          callback: function(value) {
            return value + ' ' + getDisplayUnitName()
          }
        }
      },
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: theme.textSecondary === 'text-gray-600' ? '#6b7280' : '#9ca3af',
          font: {
            size: 12,
          }
        }
      }
    },
    animation: {
      duration: 1500,
      easing: 'easeInOutQuart',
    }
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className={`text-3xl font-bold ${theme.text}`}>Usage Tracking</h1>
        <p className={`mt-2 ${theme.textSecondary}`}>
          Record your current meter readings and track electricity usage
        </p>
      </div>

      {/* Usage summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg">
              <HiLightningBolt className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Current Reading
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent`}>
                {state.currentUnits.toFixed(2)}
              </p>
              <p className="text-xs text-blue-500 font-medium">{getDisplayUnitName()}</p>
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-rose-400 to-pink-500 shadow-lg">
              <HiTrendingUp className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Usage Since Last
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent`}>
                {usageSinceLastRecording.toFixed(2)}
              </p>
              <p className="text-xs text-rose-500 font-medium">{getDisplayUnitName()}</p>
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg">
              <HiCalendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Total Usage
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent`}>
                {totalUsage.toFixed(2)}
              </p>
              <p className="text-xs text-emerald-500 font-medium">{getDisplayUnitName()}</p>
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg">
              <HiClock className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Average Usage
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent`}>
                {averageUsage.toFixed(2)}
              </p>
              <p className="text-xs text-violet-500 font-medium">{getDisplayUnitName()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Bar Chart */}
      <div className={`${theme.card} rounded-2xl shadow-lg p-8 border ${theme.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className={`text-2xl font-bold ${theme.text} flex items-center gap-3`}>
              <div className="p-3 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg">
                <HiChartBar className="h-6 w-6 text-white" />
              </div>
              Usage Analytics
            </h2>
            <p className={`mt-2 ${theme.textSecondary} opacity-80`}>
              Visual representation of your daily electricity consumption
            </p>
          </div>
        </div>
        <div className="h-80 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 rounded-xl backdrop-blur-sm"></div>
          <div className="relative h-full p-4">
            <Bar data={chartData} options={chartOptions} />
          </div>
        </div>
      </div>

      {/* Usage form and recent readings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Usage form */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
              <HiLightningBolt className="h-5 w-5 text-white" />
            </div>
            Record New Reading
          </h2>
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4">
            <UsageForm />
          </div>
        </div>

        {/* Recent readings */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md">
              <HiCalendar className="h-5 w-5 text-white" />
            </div>
            Recent Readings
          </h2>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {state.usageHistory.slice(0, 10).map((entry) => (
              <div
                key={entry.id}
                className={`p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className={`font-semibold ${theme.text} text-lg`}>
                      {entry.currentUnits.toFixed(2)} {getDisplayUnitName()}
                    </p>
                    <p className={`text-sm ${theme.textSecondary} opacity-80`}>
                      Previous: {entry.previousUnits.toFixed(2)} {getDisplayUnitName()}
                    </p>
                    <p className={`text-xs ${theme.textSecondary} mt-1 opacity-70`}>
                      {entry.timestamp}
                    </p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                      entry.usage > 0
                        ? 'bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200'
                        : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200'
                    }`}>
                      -{entry.usage.toFixed(2)} {getDisplayUnitName()}
                    </span>
                    <p className={`text-xs ${theme.textSecondary} mt-1 font-medium`}>
                      {state.currencySymbol || 'R'}{(entry.usage * state.unitCost).toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            ))}

            {state.usageHistory.length === 0 && (
              <div className="text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-gray-100 to-slate-200 w-fit mx-auto mb-4">
                  <HiTrendingUp className={`h-12 w-12 text-gray-400`} />
                </div>
                <p className={`text-sm ${theme.textSecondary} opacity-80 font-medium`}>
                  No usage records yet
                </p>
                <p className={`text-xs ${theme.textSecondary} opacity-60 mt-1`}>
                  Record your first reading above to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Usage calculation explanation */}
      <div className={`${theme.card} rounded-2xl shadow-lg p-8 border ${theme.border} bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50`}>
        <h2 className={`text-xl font-semibold ${theme.text} mb-6 flex items-center gap-3`}>
          <div className="p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md">
            <HiClock className="h-5 w-5 text-white" />
          </div>
          How Usage is Calculated
        </h2>
        <div className={`p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm`}>
          <div className="space-y-4 text-sm">
            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <span className={`${theme.textSecondary} font-medium`}>Previous Reading:</span>
              <span className={`${theme.text} font-bold text-lg`}>{state.previousUnits.toFixed(2)} {getDisplayUnitName()}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100">
              <span className={`${theme.textSecondary} font-medium`}>Current Reading:</span>
              <span className={`${theme.text} font-bold text-lg`}>{state.currentUnits.toFixed(2)} {getDisplayUnitName()}</span>
            </div>
            <div className="border-t border-gradient-to-r from-gray-200 to-slate-200 my-4"></div>
            <div className="flex justify-between items-center p-4 bg-gradient-to-r from-rose-50 to-pink-50 rounded-lg border border-rose-100">
              <span className={`${theme.text} font-semibold`}>Usage Since Last Recording:</span>
              <div className="text-right">
                <div className={`${theme.text} text-sm opacity-70 mb-1`}>
                  {state.previousUnits.toFixed(2)} - {state.currentUnits.toFixed(2)}
                </div>
                <span className={`${theme.text} font-bold text-xl bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent`}>
                  {usageSinceLastRecording.toFixed(2)} {getDisplayUnitName()}
                </span>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg border border-violet-100">
              <span className={`${theme.textSecondary} font-medium`}>Cost of Usage:</span>
              <span className={`${theme.text} font-bold text-lg bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent`}>
                {state.currencySymbol || 'R'}{(usageSinceLastRecording * state.unitCost).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Usage
