import React from 'react'
import { NavLink } from 'react-router-dom'
import { 
  HiH<PERSON>, 
  HiShoppingCart, 
  HiChartBar, 
  HiClipboardList, 
  HiCog,
  HiX 
} from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'
import Logo from '../Common/Logo'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HiHome },
  { name: 'Purchases', href: '/purchases', icon: HiShoppingCart },
  { name: 'Usage', href: '/usage', icon: HiChartBar },
  { name: 'History', href: '/history', icon: HiClipboardList },
  { name: 'Settings', href: '/settings', icon: HiCog },
]

function Sidebar({ isOpen, onClose }) {
  const { theme } = useTheme()

  return (
    <>
      {/* Desktop sidebar */}
      <div className={`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${theme.card} ${theme.border} border-r`}>
        <div className="flex flex-col w-full">
          {/* Logo section */}
          <div className={`flex items-center justify-center h-16 px-4 ${theme.primary} text-white`}>
            <Logo size="md" />
            <span className="ml-3 text-lg font-semibold">Prepaid Meter</span>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                    isActive
                      ? `${theme.primary} text-white`
                      : `${theme.text} hover:${theme.secondary}`
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </NavLink>
            ))}
          </nav>
        </div>
      </div>

      {/* Mobile sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${theme.card} transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full">
          {/* Header with close button */}
          <div className={`flex items-center justify-between h-16 px-4 ${theme.primary} text-white`}>
            <div className="flex items-center">
              <Logo size="md" />
              <span className="ml-3 text-lg font-semibold">Prepaid Meter</span>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-md hover:bg-white hover:bg-opacity-20 transition-colors"
            >
              <HiX className="h-6 w-6" />
            </button>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={onClose}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                    isActive
                      ? `${theme.primary} text-white`
                      : `${theme.text} hover:${theme.secondary}`
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </NavLink>
            ))}
          </nav>
        </div>
      </div>
    </>
  )
}

export default Sidebar
