Write-Host "========================================" -ForegroundColor Green
Write-Host "Finding Android SDK Location" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Common Android SDK locations
$commonPaths = @(
    "C:\Users\<USER>\AppData\Local\Android\Sdk",
    "C:\Android\Sdk",
    "C:\Program Files\Android\Sdk",
    "C:\Program Files (x86)\Android\Sdk"
)

Write-Host "Checking common Android SDK locations..." -ForegroundColor Cyan

$foundPath = $null
foreach ($path in $commonPaths) {
    Write-Host "Checking: $path" -ForegroundColor Yellow
    if (Test-Path $path) {
        Write-Host "Found Android SDK at: $path" -ForegroundColor Green
        $foundPath = $path
        break
    } else {
        Write-Host "Not found" -ForegroundColor Red
    }
}

if ($foundPath) {
    # Update local.properties with correct path
    $localPropertiesPath = "android\local.properties"
    $content = @"
# This file is automatically generated by Android Studio.
# Do not modify this file -- YOUR CHANGES WILL BE ERASED!
#
# This file should *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.

sdk.dir=$($foundPath -replace '\\', '\\')
"@
    
    $content | Out-File -FilePath $localPropertiesPath -Encoding UTF8
    Write-Host ""
    Write-Host "Updated android\local.properties with correct SDK path" -ForegroundColor Green
    Write-Host ""
    Write-Host "Now you can build the APK!" -ForegroundColor Cyan
    Write-Host "Run: powershell -ExecutionPolicy Bypass -File build-apk.ps1" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "Android SDK not found in common locations." -ForegroundColor Red
    Write-Host ""
    Write-Host "To find your Android SDK:" -ForegroundColor Yellow
    Write-Host "1. Open Android Studio" -ForegroundColor White
    Write-Host "2. Go to File > Settings (or Android Studio > Preferences on Mac)" -ForegroundColor White
    Write-Host "3. Go to Appearance & Behavior > System Settings > Android SDK" -ForegroundColor White
    Write-Host "4. Copy the 'Android SDK Location' path" -ForegroundColor White
    Write-Host "5. Edit android\local.properties and set:" -ForegroundColor White
    Write-Host "   sdk.dir=YOUR_SDK_PATH_HERE" -ForegroundColor White
    Write-Host ""
    Write-Host "Alternative: Build directly in Android Studio" -ForegroundColor Cyan
    Write-Host "1. Run: npx cap open android" -ForegroundColor White
    Write-Host "2. In Android Studio: Build > Build Bundle(s) / APK(s) > Build APK(s)" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
