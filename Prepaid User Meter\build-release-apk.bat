@echo off
echo ========================================
echo Building Prepaid Meter Release APK
echo ========================================

echo.
echo Step 1: Building React app for production...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build React app
    pause
    exit /b 1
)

echo.
echo Step 2: Copying web assets to Android...
call npx cap copy android
if %errorlevel% neq 0 (
    echo Error: Failed to copy assets
    pause
    exit /b 1
)

echo.
echo Step 3: Syncing Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo Error: Failed to sync Capacitor
    pause
    exit /b 1
)

echo.
echo Step 4: Building Release APK...
cd android
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo Error: Failed to build release APK
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo ========================================
echo SUCCESS! Release APK built successfully!
echo ========================================
echo.
echo Your release APK is located at:
echo android\app\build\outputs\apk\release\app-release-unsigned.apk
echo.
echo Note: This is an unsigned APK. For distribution, you'll need to sign it.
echo For personal use, the debug APK is recommended.
echo.
pause
