var kg=Object.defineProperty;var Ng=(t,e,n)=>e in t?kg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var L=(t,e,n)=>Ng(t,typeof e!="symbol"?e+"":e,n);function jg(t,e){for(var n=0;n<e.length;n++){const r=e[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in t)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(t,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Cg(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Zh={exports:{}},Io={},Jh={exports:{}},z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qi=Symbol.for("react.element"),Mg=Symbol.for("react.portal"),Pg=Symbol.for("react.fragment"),Eg=Symbol.for("react.strict_mode"),Tg=Symbol.for("react.profiler"),Rg=Symbol.for("react.provider"),Og=Symbol.for("react.context"),Dg=Symbol.for("react.forward_ref"),Lg=Symbol.for("react.suspense"),$g=Symbol.for("react.memo"),zg=Symbol.for("react.lazy"),pu=Symbol.iterator;function Fg(t){return t===null||typeof t!="object"?null:(t=pu&&t[pu]||t["@@iterator"],typeof t=="function"?t:null)}var ef={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},tf=Object.assign,nf={};function Lr(t,e,n){this.props=t,this.context=e,this.refs=nf,this.updater=n||ef}Lr.prototype.isReactComponent={};Lr.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Lr.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function rf(){}rf.prototype=Lr.prototype;function ic(t,e,n){this.props=t,this.context=e,this.refs=nf,this.updater=n||ef}var sc=ic.prototype=new rf;sc.constructor=ic;tf(sc,Lr.prototype);sc.isPureReactComponent=!0;var gu=Array.isArray,sf=Object.prototype.hasOwnProperty,oc={current:null},of={key:!0,ref:!0,__self:!0,__source:!0};function af(t,e,n){var r,i={},s=null,o=null;if(e!=null)for(r in e.ref!==void 0&&(o=e.ref),e.key!==void 0&&(s=""+e.key),e)sf.call(e,r)&&!of.hasOwnProperty(r)&&(i[r]=e[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(t&&t.defaultProps)for(r in a=t.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Qi,type:t,key:s,ref:o,props:i,_owner:oc.current}}function Ag(t,e){return{$$typeof:Qi,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function ac(t){return typeof t=="object"&&t!==null&&t.$$typeof===Qi}function Ig(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var xu=/\/+/g;function ua(t,e){return typeof t=="object"&&t!==null&&t.key!=null?Ig(""+t.key):e.toString(36)}function Ds(t,e,n,r,i){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var o=!1;if(t===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(t.$$typeof){case Qi:case Mg:o=!0}}if(o)return o=t,i=i(o),t=r===""?"."+ua(o,0):r,gu(i)?(n="",t!=null&&(n=t.replace(xu,"$&/")+"/"),Ds(i,e,n,"",function(u){return u})):i!=null&&(ac(i)&&(i=Ag(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(xu,"$&/")+"/")+t)),e.push(i)),1;if(o=0,r=r===""?".":r+":",gu(t))for(var a=0;a<t.length;a++){s=t[a];var l=r+ua(s,a);o+=Ds(s,e,n,l,i)}else if(l=Fg(t),typeof l=="function")for(t=l.call(t),a=0;!(s=t.next()).done;)s=s.value,l=r+ua(s,a++),o+=Ds(s,e,n,l,i);else if(s==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return o}function is(t,e,n){if(t==null)return t;var r=[],i=0;return Ds(t,r,"","",function(s){return e.call(n,s,i++)}),r}function Ug(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var De={current:null},Ls={transition:null},Hg={ReactCurrentDispatcher:De,ReactCurrentBatchConfig:Ls,ReactCurrentOwner:oc};function lf(){throw Error("act(...) is not supported in production builds of React.")}z.Children={map:is,forEach:function(t,e,n){is(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return is(t,function(){e++}),e},toArray:function(t){return is(t,function(e){return e})||[]},only:function(t){if(!ac(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};z.Component=Lr;z.Fragment=Pg;z.Profiler=Tg;z.PureComponent=ic;z.StrictMode=Eg;z.Suspense=Lg;z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hg;z.act=lf;z.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=tf({},t.props),i=t.key,s=t.ref,o=t._owner;if(e!=null){if(e.ref!==void 0&&(s=e.ref,o=oc.current),e.key!==void 0&&(i=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)sf.call(e,l)&&!of.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Qi,type:t.type,key:i,ref:s,props:r,_owner:o}};z.createContext=function(t){return t={$$typeof:Og,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Rg,_context:t},t.Consumer=t};z.createElement=af;z.createFactory=function(t){var e=af.bind(null,t);return e.type=t,e};z.createRef=function(){return{current:null}};z.forwardRef=function(t){return{$$typeof:Dg,render:t}};z.isValidElement=ac;z.lazy=function(t){return{$$typeof:zg,_payload:{_status:-1,_result:t},_init:Ug}};z.memo=function(t,e){return{$$typeof:$g,type:t,compare:e===void 0?null:e}};z.startTransition=function(t){var e=Ls.transition;Ls.transition={};try{t()}finally{Ls.transition=e}};z.unstable_act=lf;z.useCallback=function(t,e){return De.current.useCallback(t,e)};z.useContext=function(t){return De.current.useContext(t)};z.useDebugValue=function(){};z.useDeferredValue=function(t){return De.current.useDeferredValue(t)};z.useEffect=function(t,e){return De.current.useEffect(t,e)};z.useId=function(){return De.current.useId()};z.useImperativeHandle=function(t,e,n){return De.current.useImperativeHandle(t,e,n)};z.useInsertionEffect=function(t,e){return De.current.useInsertionEffect(t,e)};z.useLayoutEffect=function(t,e){return De.current.useLayoutEffect(t,e)};z.useMemo=function(t,e){return De.current.useMemo(t,e)};z.useReducer=function(t,e,n){return De.current.useReducer(t,e,n)};z.useRef=function(t){return De.current.useRef(t)};z.useState=function(t){return De.current.useState(t)};z.useSyncExternalStore=function(t,e,n){return De.current.useSyncExternalStore(t,e,n)};z.useTransition=function(){return De.current.useTransition()};z.version="18.3.1";Jh.exports=z;var k=Jh.exports;const pt=Cg(k),Wg=jg({__proto__:null,default:pt},[k]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bg=k,Vg=Symbol.for("react.element"),Yg=Symbol.for("react.fragment"),Xg=Object.prototype.hasOwnProperty,Qg=Bg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Kg={key:!0,ref:!0,__self:!0,__source:!0};function cf(t,e,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(o=e.ref);for(r in e)Xg.call(e,r)&&!Kg.hasOwnProperty(r)&&(i[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)i[r]===void 0&&(i[r]=e[r]);return{$$typeof:Vg,type:t,key:s,ref:o,props:i,_owner:Qg.current}}Io.Fragment=Yg;Io.jsx=cf;Io.jsxs=cf;Zh.exports=Io;var c=Zh.exports,Za={},uf={exports:{}},qe={},df={exports:{}},hf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(P,O){var $=P.length;P.push(O);e:for(;0<$;){var Q=$-1>>>1,q=P[Q];if(0<i(q,O))P[Q]=O,P[$]=q,$=Q;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var O=P[0],$=P.pop();if($!==O){P[0]=$;e:for(var Q=0,q=P.length,yt=q>>>1;Q<yt;){var Ee=2*(Q+1)-1,Pt=P[Ee],Te=Ee+1,rs=P[Te];if(0>i(Pt,$))Te<q&&0>i(rs,Pt)?(P[Q]=rs,P[Te]=$,Q=Te):(P[Q]=Pt,P[Ee]=$,Q=Ee);else if(Te<q&&0>i(rs,$))P[Q]=rs,P[Te]=$,Q=Te;else break e}}return O}function i(P,O){var $=P.sortIndex-O.sortIndex;return $!==0?$:P.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();t.unstable_now=function(){return o.now()-a}}var l=[],u=[],d=1,h=null,f=3,m=!1,v=!1,g=!1,b=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,x=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(P){for(var O=n(u);O!==null;){if(O.callback===null)r(u);else if(O.startTime<=P)r(u),O.sortIndex=O.expirationTime,e(l,O);else break;O=n(u)}}function w(P){if(g=!1,y(P),!v)if(n(l)!==null)v=!0,Y(S);else{var O=n(u);O!==null&&G(w,O.startTime-P)}}function S(P,O){v=!1,g&&(g=!1,p(j),j=-1),m=!0;var $=f;try{for(y(O),h=n(l);h!==null&&(!(h.expirationTime>O)||P&&!T());){var Q=h.callback;if(typeof Q=="function"){h.callback=null,f=h.priorityLevel;var q=Q(h.expirationTime<=O);O=t.unstable_now(),typeof q=="function"?h.callback=q:h===n(l)&&r(l),y(O)}else r(l);h=n(l)}if(h!==null)var yt=!0;else{var Ee=n(u);Ee!==null&&G(w,Ee.startTime-O),yt=!1}return yt}finally{h=null,f=$,m=!1}}var N=!1,_=null,j=-1,E=5,M=-1;function T(){return!(t.unstable_now()-M<E)}function D(){if(_!==null){var P=t.unstable_now();M=P;var O=!0;try{O=_(!0,P)}finally{O?V():(N=!1,_=null)}}else N=!1}var V;if(typeof x=="function")V=function(){x(D)};else if(typeof MessageChannel<"u"){var Se=new MessageChannel,U=Se.port2;Se.port1.onmessage=D,V=function(){U.postMessage(null)}}else V=function(){b(D,0)};function Y(P){_=P,N||(N=!0,V())}function G(P,O){j=b(function(){P(t.unstable_now())},O)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(P){P.callback=null},t.unstable_continueExecution=function(){v||m||(v=!0,Y(S))},t.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<P?Math.floor(1e3/P):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(P){switch(f){case 1:case 2:case 3:var O=3;break;default:O=f}var $=f;f=O;try{return P()}finally{f=$}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(P,O){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var $=f;f=P;try{return O()}finally{f=$}},t.unstable_scheduleCallback=function(P,O,$){var Q=t.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?Q+$:Q):$=Q,P){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=$+q,P={id:d++,callback:O,priorityLevel:P,startTime:$,expirationTime:q,sortIndex:-1},$>Q?(P.sortIndex=$,e(u,P),n(l)===null&&P===n(u)&&(g?(p(j),j=-1):g=!0,G(w,$-Q))):(P.sortIndex=q,e(l,P),v||m||(v=!0,Y(S))),P},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(P){var O=f;return function(){var $=f;f=O;try{return P.apply(this,arguments)}finally{f=$}}}})(hf);df.exports=hf;var Gg=df.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qg=k,Ge=Gg;function C(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ff=new Set,_i={};function qn(t,e){jr(t,e),jr(t+"Capture",e)}function jr(t,e){for(_i[t]=e,t=0;t<e.length;t++)ff.add(e[t])}var Ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ja=Object.prototype.hasOwnProperty,Zg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,vu={},yu={};function Jg(t){return Ja.call(yu,t)?!0:Ja.call(vu,t)?!1:Zg.test(t)?yu[t]=!0:(vu[t]=!0,!1)}function e0(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function t0(t,e,n,r){if(e===null||typeof e>"u"||e0(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Le(t,e,n,r,i,s,o){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=s,this.removeEmptyString=o}var we={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){we[t]=new Le(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];we[e]=new Le(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){we[t]=new Le(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){we[t]=new Le(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){we[t]=new Le(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){we[t]=new Le(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){we[t]=new Le(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){we[t]=new Le(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){we[t]=new Le(t,5,!1,t.toLowerCase(),null,!1,!1)});var lc=/[\-:]([a-z])/g;function cc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(lc,cc);we[e]=new Le(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(lc,cc);we[e]=new Le(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(lc,cc);we[e]=new Le(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){we[t]=new Le(t,1,!1,t.toLowerCase(),null,!1,!1)});we.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){we[t]=new Le(t,1,!1,t.toLowerCase(),null,!0,!0)});function uc(t,e,n,r){var i=we.hasOwnProperty(e)?we[e]:null;(i!==null?i.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(t0(e,n,i,r)&&(n=null),r||i===null?Jg(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=n===null?i.type===3?!1:"":n:(e=i.attributeName,r=i.attributeNamespace,n===null?t.removeAttribute(e):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var Yt=qg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ss=Symbol.for("react.element"),ir=Symbol.for("react.portal"),sr=Symbol.for("react.fragment"),dc=Symbol.for("react.strict_mode"),el=Symbol.for("react.profiler"),mf=Symbol.for("react.provider"),pf=Symbol.for("react.context"),hc=Symbol.for("react.forward_ref"),tl=Symbol.for("react.suspense"),nl=Symbol.for("react.suspense_list"),fc=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),gf=Symbol.for("react.offscreen"),bu=Symbol.iterator;function Ur(t){return t===null||typeof t!="object"?null:(t=bu&&t[bu]||t["@@iterator"],typeof t=="function"?t:null)}var se=Object.assign,da;function ni(t){if(da===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);da=e&&e[1]||""}return`
`+da+t}var ha=!1;function fa(t,e){if(!t||ha)return"";ha=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=o&&0<=a);break}}}finally{ha=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?ni(t):""}function n0(t){switch(t.tag){case 5:return ni(t.type);case 16:return ni("Lazy");case 13:return ni("Suspense");case 19:return ni("SuspenseList");case 0:case 2:case 15:return t=fa(t.type,!1),t;case 11:return t=fa(t.type.render,!1),t;case 1:return t=fa(t.type,!0),t;default:return""}}function rl(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case sr:return"Fragment";case ir:return"Portal";case el:return"Profiler";case dc:return"StrictMode";case tl:return"Suspense";case nl:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case pf:return(t.displayName||"Context")+".Consumer";case mf:return(t._context.displayName||"Context")+".Provider";case hc:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case fc:return e=t.displayName||null,e!==null?e:rl(t.type)||"Memo";case Gt:e=t._payload,t=t._init;try{return rl(t(e))}catch{}}return null}function r0(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return rl(e);case 8:return e===dc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function bn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function xf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function i0(t){var e=xf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function os(t){t._valueTracker||(t._valueTracker=i0(t))}function vf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=xf(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function Js(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function il(t,e){var n=e.checked;return se({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function wu(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=bn(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function yf(t,e){e=e.checked,e!=null&&uc(t,"checked",e,!1)}function sl(t,e){yf(t,e);var n=bn(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?ol(t,e.type,n):e.hasOwnProperty("defaultValue")&&ol(t,e.type,bn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function Su(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function ol(t,e,n){(e!=="number"||Js(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var ri=Array.isArray;function gr(t,e,n,r){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&r&&(t[n].defaultSelected=!0)}else{for(n=""+bn(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,r&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function al(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(C(91));return se({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function _u(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(C(92));if(ri(n)){if(1<n.length)throw Error(C(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:bn(n)}}function bf(t,e){var n=bn(e.value),r=bn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function ku(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function wf(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ll(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?wf(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var as,Sf=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,i){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,i)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(as=as||document.createElement("div"),as.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=as.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function ki(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var di={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},s0=["Webkit","ms","Moz","O"];Object.keys(di).forEach(function(t){s0.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),di[e]=di[t]})});function _f(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||di.hasOwnProperty(t)&&di[t]?(""+e).trim():e+"px"}function kf(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=_f(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,i):t[n]=i}}var o0=se({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function cl(t,e){if(e){if(o0[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(C(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(C(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(C(61))}if(e.style!=null&&typeof e.style!="object")throw Error(C(62))}}function ul(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var dl=null;function mc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var hl=null,xr=null,vr=null;function Nu(t){if(t=qi(t)){if(typeof hl!="function")throw Error(C(280));var e=t.stateNode;e&&(e=Vo(e),hl(t.stateNode,t.type,e))}}function Nf(t){xr?vr?vr.push(t):vr=[t]:xr=t}function jf(){if(xr){var t=xr,e=vr;if(vr=xr=null,Nu(t),e)for(t=0;t<e.length;t++)Nu(e[t])}}function Cf(t,e){return t(e)}function Mf(){}var ma=!1;function Pf(t,e,n){if(ma)return t(e,n);ma=!0;try{return Cf(t,e,n)}finally{ma=!1,(xr!==null||vr!==null)&&(Mf(),jf())}}function Ni(t,e){var n=t.stateNode;if(n===null)return null;var r=Vo(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(C(231,e,typeof n));return n}var fl=!1;if(Ut)try{var Hr={};Object.defineProperty(Hr,"passive",{get:function(){fl=!0}}),window.addEventListener("test",Hr,Hr),window.removeEventListener("test",Hr,Hr)}catch{fl=!1}function a0(t,e,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var hi=!1,eo=null,to=!1,ml=null,l0={onError:function(t){hi=!0,eo=t}};function c0(t,e,n,r,i,s,o,a,l){hi=!1,eo=null,a0.apply(l0,arguments)}function u0(t,e,n,r,i,s,o,a,l){if(c0.apply(this,arguments),hi){if(hi){var u=eo;hi=!1,eo=null}else throw Error(C(198));to||(to=!0,ml=u)}}function Zn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Ef(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function ju(t){if(Zn(t)!==t)throw Error(C(188))}function d0(t){var e=t.alternate;if(!e){if(e=Zn(t),e===null)throw Error(C(188));return e!==t?null:t}for(var n=t,r=e;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return ju(i),t;if(s===r)return ju(i),e;s=s.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?t:e}function Tf(t){return t=d0(t),t!==null?Rf(t):null}function Rf(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Rf(t);if(e!==null)return e;t=t.sibling}return null}var Of=Ge.unstable_scheduleCallback,Cu=Ge.unstable_cancelCallback,h0=Ge.unstable_shouldYield,f0=Ge.unstable_requestPaint,le=Ge.unstable_now,m0=Ge.unstable_getCurrentPriorityLevel,pc=Ge.unstable_ImmediatePriority,Df=Ge.unstable_UserBlockingPriority,no=Ge.unstable_NormalPriority,p0=Ge.unstable_LowPriority,Lf=Ge.unstable_IdlePriority,Uo=null,jt=null;function g0(t){if(jt&&typeof jt.onCommitFiberRoot=="function")try{jt.onCommitFiberRoot(Uo,t,void 0,(t.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:y0,x0=Math.log,v0=Math.LN2;function y0(t){return t>>>=0,t===0?32:31-(x0(t)/v0|0)|0}var ls=64,cs=4194304;function ii(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function ro(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,i=t.suspendedLanes,s=t.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=ii(a):(s&=o,s!==0&&(r=ii(s)))}else o=n&~i,o!==0?r=ii(o):s!==0&&(r=ii(s));if(r===0)return 0;if(e!==0&&e!==r&&!(e&i)&&(i=r&-r,s=e&-e,i>=s||i===16&&(s&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-gt(e),i=1<<n,r|=t[n],e&=~i;return r}function b0(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function w0(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,i=t.expirationTimes,s=t.pendingLanes;0<s;){var o=31-gt(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=b0(a,e)):l<=e&&(t.expiredLanes|=a),s&=~a}}function pl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function $f(){var t=ls;return ls<<=1,!(ls&4194240)&&(ls=64),t}function pa(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ki(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-gt(e),t[e]=n}function S0(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var i=31-gt(n),s=1<<i;e[i]=0,r[i]=-1,t[i]=-1,n&=~s}}function gc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-gt(n),i=1<<r;i&e|t[r]&e&&(t[r]|=e),n&=~i}}var B=0;function zf(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Ff,xc,Af,If,Uf,gl=!1,us=[],an=null,ln=null,cn=null,ji=new Map,Ci=new Map,Zt=[],_0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mu(t,e){switch(t){case"focusin":case"focusout":an=null;break;case"dragenter":case"dragleave":ln=null;break;case"mouseover":case"mouseout":cn=null;break;case"pointerover":case"pointerout":ji.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ci.delete(e.pointerId)}}function Wr(t,e,n,r,i,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},e!==null&&(e=qi(e),e!==null&&xc(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function k0(t,e,n,r,i){switch(e){case"focusin":return an=Wr(an,t,e,n,r,i),!0;case"dragenter":return ln=Wr(ln,t,e,n,r,i),!0;case"mouseover":return cn=Wr(cn,t,e,n,r,i),!0;case"pointerover":var s=i.pointerId;return ji.set(s,Wr(ji.get(s)||null,t,e,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Ci.set(s,Wr(Ci.get(s)||null,t,e,n,r,i)),!0}return!1}function Hf(t){var e=$n(t.target);if(e!==null){var n=Zn(e);if(n!==null){if(e=n.tag,e===13){if(e=Ef(n),e!==null){t.blockedOn=e,Uf(t.priority,function(){Af(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function $s(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=xl(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);dl=r,n.target.dispatchEvent(r),dl=null}else return e=qi(n),e!==null&&xc(e),t.blockedOn=n,!1;e.shift()}return!0}function Pu(t,e,n){$s(t)&&n.delete(e)}function N0(){gl=!1,an!==null&&$s(an)&&(an=null),ln!==null&&$s(ln)&&(ln=null),cn!==null&&$s(cn)&&(cn=null),ji.forEach(Pu),Ci.forEach(Pu)}function Br(t,e){t.blockedOn===e&&(t.blockedOn=null,gl||(gl=!0,Ge.unstable_scheduleCallback(Ge.unstable_NormalPriority,N0)))}function Mi(t){function e(i){return Br(i,t)}if(0<us.length){Br(us[0],t);for(var n=1;n<us.length;n++){var r=us[n];r.blockedOn===t&&(r.blockedOn=null)}}for(an!==null&&Br(an,t),ln!==null&&Br(ln,t),cn!==null&&Br(cn,t),ji.forEach(e),Ci.forEach(e),n=0;n<Zt.length;n++)r=Zt[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<Zt.length&&(n=Zt[0],n.blockedOn===null);)Hf(n),n.blockedOn===null&&Zt.shift()}var yr=Yt.ReactCurrentBatchConfig,io=!0;function j0(t,e,n,r){var i=B,s=yr.transition;yr.transition=null;try{B=1,vc(t,e,n,r)}finally{B=i,yr.transition=s}}function C0(t,e,n,r){var i=B,s=yr.transition;yr.transition=null;try{B=4,vc(t,e,n,r)}finally{B=i,yr.transition=s}}function vc(t,e,n,r){if(io){var i=xl(t,e,n,r);if(i===null)Na(t,e,r,so,n),Mu(t,r);else if(k0(i,t,e,n,r))r.stopPropagation();else if(Mu(t,r),e&4&&-1<_0.indexOf(t)){for(;i!==null;){var s=qi(i);if(s!==null&&Ff(s),s=xl(t,e,n,r),s===null&&Na(t,e,r,so,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Na(t,e,r,null,n)}}var so=null;function xl(t,e,n,r){if(so=null,t=mc(r),t=$n(t),t!==null)if(e=Zn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=Ef(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return so=t,null}function Wf(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(m0()){case pc:return 1;case Df:return 4;case no:case p0:return 16;case Lf:return 536870912;default:return 16}default:return 16}}var en=null,yc=null,zs=null;function Bf(){if(zs)return zs;var t,e=yc,n=e.length,r,i="value"in en?en.value:en.textContent,s=i.length;for(t=0;t<n&&e[t]===i[t];t++);var o=n-t;for(r=1;r<=o&&e[n-r]===i[s-r];r++);return zs=i.slice(t,1<r?1-r:void 0)}function Fs(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ds(){return!0}function Eu(){return!1}function Ze(t){function e(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ds:Eu,this.isPropagationStopped=Eu,this}return se(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ds)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ds)},persist:function(){},isPersistent:ds}),e}var $r={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},bc=Ze($r),Gi=se({},$r,{view:0,detail:0}),M0=Ze(Gi),ga,xa,Vr,Ho=se({},Gi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Vr&&(Vr&&t.type==="mousemove"?(ga=t.screenX-Vr.screenX,xa=t.screenY-Vr.screenY):xa=ga=0,Vr=t),ga)},movementY:function(t){return"movementY"in t?t.movementY:xa}}),Tu=Ze(Ho),P0=se({},Ho,{dataTransfer:0}),E0=Ze(P0),T0=se({},Gi,{relatedTarget:0}),va=Ze(T0),R0=se({},$r,{animationName:0,elapsedTime:0,pseudoElement:0}),O0=Ze(R0),D0=se({},$r,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),L0=Ze(D0),$0=se({},$r,{data:0}),Ru=Ze($0),z0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},F0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},A0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function I0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=A0[t])?!!e[t]:!1}function wc(){return I0}var U0=se({},Gi,{key:function(t){if(t.key){var e=z0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Fs(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?F0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wc,charCode:function(t){return t.type==="keypress"?Fs(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Fs(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),H0=Ze(U0),W0=se({},Ho,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ou=Ze(W0),B0=se({},Gi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wc}),V0=Ze(B0),Y0=se({},$r,{propertyName:0,elapsedTime:0,pseudoElement:0}),X0=Ze(Y0),Q0=se({},Ho,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),K0=Ze(Q0),G0=[9,13,27,32],Sc=Ut&&"CompositionEvent"in window,fi=null;Ut&&"documentMode"in document&&(fi=document.documentMode);var q0=Ut&&"TextEvent"in window&&!fi,Vf=Ut&&(!Sc||fi&&8<fi&&11>=fi),Du=" ",Lu=!1;function Yf(t,e){switch(t){case"keyup":return G0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Xf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var or=!1;function Z0(t,e){switch(t){case"compositionend":return Xf(e);case"keypress":return e.which!==32?null:(Lu=!0,Du);case"textInput":return t=e.data,t===Du&&Lu?null:t;default:return null}}function J0(t,e){if(or)return t==="compositionend"||!Sc&&Yf(t,e)?(t=Bf(),zs=yc=en=null,or=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Vf&&e.locale!=="ko"?null:e.data;default:return null}}var ex={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $u(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!ex[t.type]:e==="textarea"}function Qf(t,e,n,r){Nf(r),e=oo(e,"onChange"),0<e.length&&(n=new bc("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var mi=null,Pi=null;function tx(t){sm(t,0)}function Wo(t){var e=cr(t);if(vf(e))return t}function nx(t,e){if(t==="change")return e}var Kf=!1;if(Ut){var ya;if(Ut){var ba="oninput"in document;if(!ba){var zu=document.createElement("div");zu.setAttribute("oninput","return;"),ba=typeof zu.oninput=="function"}ya=ba}else ya=!1;Kf=ya&&(!document.documentMode||9<document.documentMode)}function Fu(){mi&&(mi.detachEvent("onpropertychange",Gf),Pi=mi=null)}function Gf(t){if(t.propertyName==="value"&&Wo(Pi)){var e=[];Qf(e,Pi,t,mc(t)),Pf(tx,e)}}function rx(t,e,n){t==="focusin"?(Fu(),mi=e,Pi=n,mi.attachEvent("onpropertychange",Gf)):t==="focusout"&&Fu()}function ix(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Wo(Pi)}function sx(t,e){if(t==="click")return Wo(e)}function ox(t,e){if(t==="input"||t==="change")return Wo(e)}function ax(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var vt=typeof Object.is=="function"?Object.is:ax;function Ei(t,e){if(vt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Ja.call(e,i)||!vt(t[i],e[i]))return!1}return!0}function Au(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Iu(t,e){var n=Au(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Au(n)}}function qf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?qf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Zf(){for(var t=window,e=Js();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Js(t.document)}return e}function _c(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function lx(t){var e=Zf(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&qf(n.ownerDocument.documentElement,n)){if(r!==null&&_c(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!t.extend&&s>r&&(i=r,r=s,s=i),i=Iu(n,s);var o=Iu(n,r);i&&o&&(t.rangeCount!==1||t.anchorNode!==i.node||t.anchorOffset!==i.offset||t.focusNode!==o.node||t.focusOffset!==o.offset)&&(e=e.createRange(),e.setStart(i.node,i.offset),t.removeAllRanges(),s>r?(t.addRange(e),t.extend(o.node,o.offset)):(e.setEnd(o.node,o.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var cx=Ut&&"documentMode"in document&&11>=document.documentMode,ar=null,vl=null,pi=null,yl=!1;function Uu(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;yl||ar==null||ar!==Js(r)||(r=ar,"selectionStart"in r&&_c(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),pi&&Ei(pi,r)||(pi=r,r=oo(vl,"onSelect"),0<r.length&&(e=new bc("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=ar)))}function hs(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var lr={animationend:hs("Animation","AnimationEnd"),animationiteration:hs("Animation","AnimationIteration"),animationstart:hs("Animation","AnimationStart"),transitionend:hs("Transition","TransitionEnd")},wa={},Jf={};Ut&&(Jf=document.createElement("div").style,"AnimationEvent"in window||(delete lr.animationend.animation,delete lr.animationiteration.animation,delete lr.animationstart.animation),"TransitionEvent"in window||delete lr.transitionend.transition);function Bo(t){if(wa[t])return wa[t];if(!lr[t])return t;var e=lr[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Jf)return wa[t]=e[n];return t}var em=Bo("animationend"),tm=Bo("animationiteration"),nm=Bo("animationstart"),rm=Bo("transitionend"),im=new Map,Hu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _n(t,e){im.set(t,e),qn(e,[t])}for(var Sa=0;Sa<Hu.length;Sa++){var _a=Hu[Sa],ux=_a.toLowerCase(),dx=_a[0].toUpperCase()+_a.slice(1);_n(ux,"on"+dx)}_n(em,"onAnimationEnd");_n(tm,"onAnimationIteration");_n(nm,"onAnimationStart");_n("dblclick","onDoubleClick");_n("focusin","onFocus");_n("focusout","onBlur");_n(rm,"onTransitionEnd");jr("onMouseEnter",["mouseout","mouseover"]);jr("onMouseLeave",["mouseout","mouseover"]);jr("onPointerEnter",["pointerout","pointerover"]);jr("onPointerLeave",["pointerout","pointerover"]);qn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));qn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));qn("onBeforeInput",["compositionend","keypress","textInput","paste"]);qn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));qn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var si="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),hx=new Set("cancel close invalid load scroll toggle".split(" ").concat(si));function Wu(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,u0(r,e,void 0,t),t.currentTarget=null}function sm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],i=r.event;r=r.listeners;e:{var s=void 0;if(e)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;Wu(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;Wu(i,a,u),s=l}}}if(to)throw t=ml,to=!1,ml=null,t}function Z(t,e){var n=e[kl];n===void 0&&(n=e[kl]=new Set);var r=t+"__bubble";n.has(r)||(om(e,t,2,!1),n.add(r))}function ka(t,e,n){var r=0;e&&(r|=4),om(n,t,r,e)}var fs="_reactListening"+Math.random().toString(36).slice(2);function Ti(t){if(!t[fs]){t[fs]=!0,ff.forEach(function(n){n!=="selectionchange"&&(hx.has(n)||ka(n,!1,t),ka(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[fs]||(e[fs]=!0,ka("selectionchange",!1,e))}}function om(t,e,n,r){switch(Wf(e)){case 1:var i=j0;break;case 4:i=C0;break;default:i=vc}n=i.bind(null,e,n,t),i=void 0,!fl||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),r?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Na(t,e,n,r,i){var s=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=$n(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}Pf(function(){var u=s,d=mc(n),h=[];e:{var f=im.get(t);if(f!==void 0){var m=bc,v=t;switch(t){case"keypress":if(Fs(n)===0)break e;case"keydown":case"keyup":m=H0;break;case"focusin":v="focus",m=va;break;case"focusout":v="blur",m=va;break;case"beforeblur":case"afterblur":m=va;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Tu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=E0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=V0;break;case em:case tm:case nm:m=O0;break;case rm:m=X0;break;case"scroll":m=M0;break;case"wheel":m=K0;break;case"copy":case"cut":case"paste":m=L0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Ou}var g=(e&4)!==0,b=!g&&t==="scroll",p=g?f!==null?f+"Capture":null:f;g=[];for(var x=u,y;x!==null;){y=x;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,p!==null&&(w=Ni(x,p),w!=null&&g.push(Ri(x,w,y)))),b)break;x=x.return}0<g.length&&(f=new m(f,v,null,n,d),h.push({event:f,listeners:g}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",f&&n!==dl&&(v=n.relatedTarget||n.fromElement)&&($n(v)||v[Ht]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(v=n.relatedTarget||n.toElement,m=u,v=v?$n(v):null,v!==null&&(b=Zn(v),v!==b||v.tag!==5&&v.tag!==6)&&(v=null)):(m=null,v=u),m!==v)){if(g=Tu,w="onMouseLeave",p="onMouseEnter",x="mouse",(t==="pointerout"||t==="pointerover")&&(g=Ou,w="onPointerLeave",p="onPointerEnter",x="pointer"),b=m==null?f:cr(m),y=v==null?f:cr(v),f=new g(w,x+"leave",m,n,d),f.target=b,f.relatedTarget=y,w=null,$n(d)===u&&(g=new g(p,x+"enter",v,n,d),g.target=y,g.relatedTarget=b,w=g),b=w,m&&v)t:{for(g=m,p=v,x=0,y=g;y;y=er(y))x++;for(y=0,w=p;w;w=er(w))y++;for(;0<x-y;)g=er(g),x--;for(;0<y-x;)p=er(p),y--;for(;x--;){if(g===p||p!==null&&g===p.alternate)break t;g=er(g),p=er(p)}g=null}else g=null;m!==null&&Bu(h,f,m,g,!1),v!==null&&b!==null&&Bu(h,b,v,g,!0)}}e:{if(f=u?cr(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var S=nx;else if($u(f))if(Kf)S=ox;else{S=ix;var N=rx}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=sx);if(S&&(S=S(t,u))){Qf(h,S,n,d);break e}N&&N(t,f,u),t==="focusout"&&(N=f._wrapperState)&&N.controlled&&f.type==="number"&&ol(f,"number",f.value)}switch(N=u?cr(u):window,t){case"focusin":($u(N)||N.contentEditable==="true")&&(ar=N,vl=u,pi=null);break;case"focusout":pi=vl=ar=null;break;case"mousedown":yl=!0;break;case"contextmenu":case"mouseup":case"dragend":yl=!1,Uu(h,n,d);break;case"selectionchange":if(cx)break;case"keydown":case"keyup":Uu(h,n,d)}var _;if(Sc)e:{switch(t){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else or?Yf(t,n)&&(j="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Vf&&n.locale!=="ko"&&(or||j!=="onCompositionStart"?j==="onCompositionEnd"&&or&&(_=Bf()):(en=d,yc="value"in en?en.value:en.textContent,or=!0)),N=oo(u,j),0<N.length&&(j=new Ru(j,t,null,n,d),h.push({event:j,listeners:N}),_?j.data=_:(_=Xf(n),_!==null&&(j.data=_)))),(_=q0?Z0(t,n):J0(t,n))&&(u=oo(u,"onBeforeInput"),0<u.length&&(d=new Ru("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=_))}sm(h,e)})}function Ri(t,e,n){return{instance:t,listener:e,currentTarget:n}}function oo(t,e){for(var n=e+"Capture",r=[];t!==null;){var i=t,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Ni(t,n),s!=null&&r.unshift(Ri(t,s,i)),s=Ni(t,e),s!=null&&r.push(Ri(t,s,i))),t=t.return}return r}function er(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Bu(t,e,n,r,i){for(var s=e._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Ni(n,s),l!=null&&o.unshift(Ri(n,l,a))):i||(l=Ni(n,s),l!=null&&o.push(Ri(n,l,a)))),n=n.return}o.length!==0&&t.push({event:e,listeners:o})}var fx=/\r\n?/g,mx=/\u0000|\uFFFD/g;function Vu(t){return(typeof t=="string"?t:""+t).replace(fx,`
`).replace(mx,"")}function ms(t,e,n){if(e=Vu(e),Vu(t)!==e&&n)throw Error(C(425))}function ao(){}var bl=null,wl=null;function Sl(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var _l=typeof setTimeout=="function"?setTimeout:void 0,px=typeof clearTimeout=="function"?clearTimeout:void 0,Yu=typeof Promise=="function"?Promise:void 0,gx=typeof queueMicrotask=="function"?queueMicrotask:typeof Yu<"u"?function(t){return Yu.resolve(null).then(t).catch(xx)}:_l;function xx(t){setTimeout(function(){throw t})}function ja(t,e){var n=e,r=0;do{var i=n.nextSibling;if(t.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){t.removeChild(i),Mi(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Mi(e)}function un(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function Xu(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var zr=Math.random().toString(36).slice(2),Nt="__reactFiber$"+zr,Oi="__reactProps$"+zr,Ht="__reactContainer$"+zr,kl="__reactEvents$"+zr,vx="__reactListeners$"+zr,yx="__reactHandles$"+zr;function $n(t){var e=t[Nt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ht]||n[Nt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Xu(t);t!==null;){if(n=t[Nt])return n;t=Xu(t)}return e}t=n,n=t.parentNode}return null}function qi(t){return t=t[Nt]||t[Ht],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function cr(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(C(33))}function Vo(t){return t[Oi]||null}var Nl=[],ur=-1;function kn(t){return{current:t}}function ee(t){0>ur||(t.current=Nl[ur],Nl[ur]=null,ur--)}function K(t,e){ur++,Nl[ur]=t.current,t.current=e}var wn={},Me=kn(wn),We=kn(!1),Wn=wn;function Cr(t,e){var n=t.type.contextTypes;if(!n)return wn;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=e[s];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function Be(t){return t=t.childContextTypes,t!=null}function lo(){ee(We),ee(Me)}function Qu(t,e,n){if(Me.current!==wn)throw Error(C(168));K(Me,e),K(We,n)}function am(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in e))throw Error(C(108,r0(t)||"Unknown",i));return se({},n,r)}function co(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||wn,Wn=Me.current,K(Me,t),K(We,We.current),!0}function Ku(t,e,n){var r=t.stateNode;if(!r)throw Error(C(169));n?(t=am(t,e,Wn),r.__reactInternalMemoizedMergedChildContext=t,ee(We),ee(Me),K(Me,t)):ee(We),K(We,n)}var Dt=null,Yo=!1,Ca=!1;function lm(t){Dt===null?Dt=[t]:Dt.push(t)}function bx(t){Yo=!0,lm(t)}function Nn(){if(!Ca&&Dt!==null){Ca=!0;var t=0,e=B;try{var n=Dt;for(B=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}Dt=null,Yo=!1}catch(i){throw Dt!==null&&(Dt=Dt.slice(t+1)),Of(pc,Nn),i}finally{B=e,Ca=!1}}return null}var dr=[],hr=0,uo=null,ho=0,et=[],tt=0,Bn=null,zt=1,Ft="";function Tn(t,e){dr[hr++]=ho,dr[hr++]=uo,uo=t,ho=e}function cm(t,e,n){et[tt++]=zt,et[tt++]=Ft,et[tt++]=Bn,Bn=t;var r=zt;t=Ft;var i=32-gt(r)-1;r&=~(1<<i),n+=1;var s=32-gt(e)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,zt=1<<32-gt(e)+i|n<<i|r,Ft=s+t}else zt=1<<s|n<<i|r,Ft=t}function kc(t){t.return!==null&&(Tn(t,1),cm(t,1,0))}function Nc(t){for(;t===uo;)uo=dr[--hr],dr[hr]=null,ho=dr[--hr],dr[hr]=null;for(;t===Bn;)Bn=et[--tt],et[tt]=null,Ft=et[--tt],et[tt]=null,zt=et[--tt],et[tt]=null}var Ke=null,Qe=null,ne=!1,ft=null;function um(t,e){var n=nt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function Gu(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Ke=t,Qe=un(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Ke=t,Qe=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Bn!==null?{id:zt,overflow:Ft}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=nt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Ke=t,Qe=null,!0):!1;default:return!1}}function jl(t){return(t.mode&1)!==0&&(t.flags&128)===0}function Cl(t){if(ne){var e=Qe;if(e){var n=e;if(!Gu(t,e)){if(jl(t))throw Error(C(418));e=un(n.nextSibling);var r=Ke;e&&Gu(t,e)?um(r,n):(t.flags=t.flags&-4097|2,ne=!1,Ke=t)}}else{if(jl(t))throw Error(C(418));t.flags=t.flags&-4097|2,ne=!1,Ke=t}}}function qu(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Ke=t}function ps(t){if(t!==Ke)return!1;if(!ne)return qu(t),ne=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!Sl(t.type,t.memoizedProps)),e&&(e=Qe)){if(jl(t))throw dm(),Error(C(418));for(;e;)um(t,e),e=un(e.nextSibling)}if(qu(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(C(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Qe=un(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Qe=null}}else Qe=Ke?un(t.stateNode.nextSibling):null;return!0}function dm(){for(var t=Qe;t;)t=un(t.nextSibling)}function Mr(){Qe=Ke=null,ne=!1}function jc(t){ft===null?ft=[t]:ft.push(t)}var wx=Yt.ReactCurrentBatchConfig;function Yr(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,t));var i=r,s=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===s?e.ref:(e=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},e._stringRef=s,e)}if(typeof t!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,t))}return t}function gs(t,e){throw t=Object.prototype.toString.call(e),Error(C(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Zu(t){var e=t._init;return e(t._payload)}function hm(t){function e(p,x){if(t){var y=p.deletions;y===null?(p.deletions=[x],p.flags|=16):y.push(x)}}function n(p,x){if(!t)return null;for(;x!==null;)e(p,x),x=x.sibling;return null}function r(p,x){for(p=new Map;x!==null;)x.key!==null?p.set(x.key,x):p.set(x.index,x),x=x.sibling;return p}function i(p,x){return p=mn(p,x),p.index=0,p.sibling=null,p}function s(p,x,y){return p.index=y,t?(y=p.alternate,y!==null?(y=y.index,y<x?(p.flags|=2,x):y):(p.flags|=2,x)):(p.flags|=1048576,x)}function o(p){return t&&p.alternate===null&&(p.flags|=2),p}function a(p,x,y,w){return x===null||x.tag!==6?(x=Da(y,p.mode,w),x.return=p,x):(x=i(x,y),x.return=p,x)}function l(p,x,y,w){var S=y.type;return S===sr?d(p,x,y.props.children,w,y.key):x!==null&&(x.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Gt&&Zu(S)===x.type)?(w=i(x,y.props),w.ref=Yr(p,x,y),w.return=p,w):(w=Vs(y.type,y.key,y.props,null,p.mode,w),w.ref=Yr(p,x,y),w.return=p,w)}function u(p,x,y,w){return x===null||x.tag!==4||x.stateNode.containerInfo!==y.containerInfo||x.stateNode.implementation!==y.implementation?(x=La(y,p.mode,w),x.return=p,x):(x=i(x,y.children||[]),x.return=p,x)}function d(p,x,y,w,S){return x===null||x.tag!==7?(x=Un(y,p.mode,w,S),x.return=p,x):(x=i(x,y),x.return=p,x)}function h(p,x,y){if(typeof x=="string"&&x!==""||typeof x=="number")return x=Da(""+x,p.mode,y),x.return=p,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case ss:return y=Vs(x.type,x.key,x.props,null,p.mode,y),y.ref=Yr(p,null,x),y.return=p,y;case ir:return x=La(x,p.mode,y),x.return=p,x;case Gt:var w=x._init;return h(p,w(x._payload),y)}if(ri(x)||Ur(x))return x=Un(x,p.mode,y,null),x.return=p,x;gs(p,x)}return null}function f(p,x,y,w){var S=x!==null?x.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return S!==null?null:a(p,x,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ss:return y.key===S?l(p,x,y,w):null;case ir:return y.key===S?u(p,x,y,w):null;case Gt:return S=y._init,f(p,x,S(y._payload),w)}if(ri(y)||Ur(y))return S!==null?null:d(p,x,y,w,null);gs(p,y)}return null}function m(p,x,y,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return p=p.get(y)||null,a(x,p,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ss:return p=p.get(w.key===null?y:w.key)||null,l(x,p,w,S);case ir:return p=p.get(w.key===null?y:w.key)||null,u(x,p,w,S);case Gt:var N=w._init;return m(p,x,y,N(w._payload),S)}if(ri(w)||Ur(w))return p=p.get(y)||null,d(x,p,w,S,null);gs(x,w)}return null}function v(p,x,y,w){for(var S=null,N=null,_=x,j=x=0,E=null;_!==null&&j<y.length;j++){_.index>j?(E=_,_=null):E=_.sibling;var M=f(p,_,y[j],w);if(M===null){_===null&&(_=E);break}t&&_&&M.alternate===null&&e(p,_),x=s(M,x,j),N===null?S=M:N.sibling=M,N=M,_=E}if(j===y.length)return n(p,_),ne&&Tn(p,j),S;if(_===null){for(;j<y.length;j++)_=h(p,y[j],w),_!==null&&(x=s(_,x,j),N===null?S=_:N.sibling=_,N=_);return ne&&Tn(p,j),S}for(_=r(p,_);j<y.length;j++)E=m(_,p,j,y[j],w),E!==null&&(t&&E.alternate!==null&&_.delete(E.key===null?j:E.key),x=s(E,x,j),N===null?S=E:N.sibling=E,N=E);return t&&_.forEach(function(T){return e(p,T)}),ne&&Tn(p,j),S}function g(p,x,y,w){var S=Ur(y);if(typeof S!="function")throw Error(C(150));if(y=S.call(y),y==null)throw Error(C(151));for(var N=S=null,_=x,j=x=0,E=null,M=y.next();_!==null&&!M.done;j++,M=y.next()){_.index>j?(E=_,_=null):E=_.sibling;var T=f(p,_,M.value,w);if(T===null){_===null&&(_=E);break}t&&_&&T.alternate===null&&e(p,_),x=s(T,x,j),N===null?S=T:N.sibling=T,N=T,_=E}if(M.done)return n(p,_),ne&&Tn(p,j),S;if(_===null){for(;!M.done;j++,M=y.next())M=h(p,M.value,w),M!==null&&(x=s(M,x,j),N===null?S=M:N.sibling=M,N=M);return ne&&Tn(p,j),S}for(_=r(p,_);!M.done;j++,M=y.next())M=m(_,p,j,M.value,w),M!==null&&(t&&M.alternate!==null&&_.delete(M.key===null?j:M.key),x=s(M,x,j),N===null?S=M:N.sibling=M,N=M);return t&&_.forEach(function(D){return e(p,D)}),ne&&Tn(p,j),S}function b(p,x,y,w){if(typeof y=="object"&&y!==null&&y.type===sr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ss:e:{for(var S=y.key,N=x;N!==null;){if(N.key===S){if(S=y.type,S===sr){if(N.tag===7){n(p,N.sibling),x=i(N,y.props.children),x.return=p,p=x;break e}}else if(N.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Gt&&Zu(S)===N.type){n(p,N.sibling),x=i(N,y.props),x.ref=Yr(p,N,y),x.return=p,p=x;break e}n(p,N);break}else e(p,N);N=N.sibling}y.type===sr?(x=Un(y.props.children,p.mode,w,y.key),x.return=p,p=x):(w=Vs(y.type,y.key,y.props,null,p.mode,w),w.ref=Yr(p,x,y),w.return=p,p=w)}return o(p);case ir:e:{for(N=y.key;x!==null;){if(x.key===N)if(x.tag===4&&x.stateNode.containerInfo===y.containerInfo&&x.stateNode.implementation===y.implementation){n(p,x.sibling),x=i(x,y.children||[]),x.return=p,p=x;break e}else{n(p,x);break}else e(p,x);x=x.sibling}x=La(y,p.mode,w),x.return=p,p=x}return o(p);case Gt:return N=y._init,b(p,x,N(y._payload),w)}if(ri(y))return v(p,x,y,w);if(Ur(y))return g(p,x,y,w);gs(p,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,x!==null&&x.tag===6?(n(p,x.sibling),x=i(x,y),x.return=p,p=x):(n(p,x),x=Da(y,p.mode,w),x.return=p,p=x),o(p)):n(p,x)}return b}var Pr=hm(!0),fm=hm(!1),fo=kn(null),mo=null,fr=null,Cc=null;function Mc(){Cc=fr=mo=null}function Pc(t){var e=fo.current;ee(fo),t._currentValue=e}function Ml(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function br(t,e){mo=t,Cc=fr=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Ie=!0),t.firstContext=null)}function ot(t){var e=t._currentValue;if(Cc!==t)if(t={context:t,memoizedValue:e,next:null},fr===null){if(mo===null)throw Error(C(308));fr=t,mo.dependencies={lanes:0,firstContext:t}}else fr=fr.next=t;return e}var zn=null;function Ec(t){zn===null?zn=[t]:zn.push(t)}function mm(t,e,n,r){var i=e.interleaved;return i===null?(n.next=n,Ec(e)):(n.next=i.next,i.next=n),e.interleaved=n,Wt(t,r)}function Wt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var qt=!1;function Tc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function pm(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function It(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function dn(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,F&2){var i=r.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),r.pending=e,Wt(t,n)}return i=r.interleaved,i===null?(e.next=e,Ec(r)):(e.next=i.next,i.next=e),r.interleaved=e,Wt(t,n)}function As(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,gc(t,n)}}function Ju(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=e:s=s.next=e}else i=s=e;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function po(t,e,n,r){var i=t.updateQueue;qt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var d=t.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(s!==null){var h=i.baseState;o=0,d=u=l=null,a=s;do{var f=a.lane,m=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=t,g=a;switch(f=e,m=n,g.tag){case 1:if(v=g.payload,typeof v=="function"){h=v.call(m,h,f);break e}h=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=g.payload,f=typeof v=="function"?v.call(m,h,f):v,f==null)break e;h=se({},h,f);break e;case 2:qt=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,f=i.effects,f===null?i.effects=[a]:f.push(a))}else m={eventTime:m,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=m,l=h):d=d.next=m,o|=f;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;f=a,a=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(d===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,e=i.shared.interleaved,e!==null){i=e;do o|=i.lane,i=i.next;while(i!==e)}else s===null&&(i.shared.lanes=0);Yn|=o,t.lanes=o,t.memoizedState=h}}function ed(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var Zi={},Ct=kn(Zi),Di=kn(Zi),Li=kn(Zi);function Fn(t){if(t===Zi)throw Error(C(174));return t}function Rc(t,e){switch(K(Li,e),K(Di,t),K(Ct,Zi),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:ll(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=ll(e,t)}ee(Ct),K(Ct,e)}function Er(){ee(Ct),ee(Di),ee(Li)}function gm(t){Fn(Li.current);var e=Fn(Ct.current),n=ll(e,t.type);e!==n&&(K(Di,t),K(Ct,n))}function Oc(t){Di.current===t&&(ee(Ct),ee(Di))}var re=kn(0);function go(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var Ma=[];function Dc(){for(var t=0;t<Ma.length;t++)Ma[t]._workInProgressVersionPrimary=null;Ma.length=0}var Is=Yt.ReactCurrentDispatcher,Pa=Yt.ReactCurrentBatchConfig,Vn=0,ie=null,fe=null,xe=null,xo=!1,gi=!1,$i=0,Sx=0;function _e(){throw Error(C(321))}function Lc(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!vt(t[n],e[n]))return!1;return!0}function $c(t,e,n,r,i,s){if(Vn=s,ie=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Is.current=t===null||t.memoizedState===null?jx:Cx,t=n(r,i),gi){s=0;do{if(gi=!1,$i=0,25<=s)throw Error(C(301));s+=1,xe=fe=null,e.updateQueue=null,Is.current=Mx,t=n(r,i)}while(gi)}if(Is.current=vo,e=fe!==null&&fe.next!==null,Vn=0,xe=fe=ie=null,xo=!1,e)throw Error(C(300));return t}function zc(){var t=$i!==0;return $i=0,t}function _t(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?ie.memoizedState=xe=t:xe=xe.next=t,xe}function at(){if(fe===null){var t=ie.alternate;t=t!==null?t.memoizedState:null}else t=fe.next;var e=xe===null?ie.memoizedState:xe.next;if(e!==null)xe=e,fe=t;else{if(t===null)throw Error(C(310));fe=t,t={memoizedState:fe.memoizedState,baseState:fe.baseState,baseQueue:fe.baseQueue,queue:fe.queue,next:null},xe===null?ie.memoizedState=xe=t:xe=xe.next=t}return xe}function zi(t,e){return typeof e=="function"?e(t):e}function Ea(t){var e=at(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=fe,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var d=u.lane;if((Vn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,o=r):l=l.next=h,ie.lanes|=d,Yn|=d}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,vt(r,e.memoizedState)||(Ie=!0),e.memoizedState=r,e.baseState=o,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){i=t;do s=i.lane,ie.lanes|=s,Yn|=s,i=i.next;while(i!==t)}else i===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function Ta(t){var e=at(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=n.dispatch,i=n.pending,s=e.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=t(s,o.action),o=o.next;while(o!==i);vt(s,e.memoizedState)||(Ie=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),n.lastRenderedState=s}return[s,r]}function xm(){}function vm(t,e){var n=ie,r=at(),i=e(),s=!vt(r.memoizedState,i);if(s&&(r.memoizedState=i,Ie=!0),r=r.queue,Fc(wm.bind(null,n,r,t),[t]),r.getSnapshot!==e||s||xe!==null&&xe.memoizedState.tag&1){if(n.flags|=2048,Fi(9,bm.bind(null,n,r,i,e),void 0,null),ve===null)throw Error(C(349));Vn&30||ym(n,e,i)}return i}function ym(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ie.updateQueue,e===null?(e={lastEffect:null,stores:null},ie.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function bm(t,e,n,r){e.value=n,e.getSnapshot=r,Sm(e)&&_m(t)}function wm(t,e,n){return n(function(){Sm(e)&&_m(t)})}function Sm(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!vt(t,n)}catch{return!0}}function _m(t){var e=Wt(t,1);e!==null&&xt(e,t,1,-1)}function td(t){var e=_t();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:zi,lastRenderedState:t},e.queue=t,t=t.dispatch=Nx.bind(null,ie,t),[e.memoizedState,t]}function Fi(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=ie.updateQueue,e===null?(e={lastEffect:null,stores:null},ie.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function km(){return at().memoizedState}function Us(t,e,n,r){var i=_t();ie.flags|=t,i.memoizedState=Fi(1|e,n,void 0,r===void 0?null:r)}function Xo(t,e,n,r){var i=at();r=r===void 0?null:r;var s=void 0;if(fe!==null){var o=fe.memoizedState;if(s=o.destroy,r!==null&&Lc(r,o.deps)){i.memoizedState=Fi(e,n,s,r);return}}ie.flags|=t,i.memoizedState=Fi(1|e,n,s,r)}function nd(t,e){return Us(8390656,8,t,e)}function Fc(t,e){return Xo(2048,8,t,e)}function Nm(t,e){return Xo(4,2,t,e)}function jm(t,e){return Xo(4,4,t,e)}function Cm(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Mm(t,e,n){return n=n!=null?n.concat([t]):null,Xo(4,4,Cm.bind(null,e,t),n)}function Ac(){}function Pm(t,e){var n=at();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Lc(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function Em(t,e){var n=at();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Lc(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function Tm(t,e,n){return Vn&21?(vt(n,e)||(n=$f(),ie.lanes|=n,Yn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Ie=!0),t.memoizedState=n)}function _x(t,e){var n=B;B=n!==0&&4>n?n:4,t(!0);var r=Pa.transition;Pa.transition={};try{t(!1),e()}finally{B=n,Pa.transition=r}}function Rm(){return at().memoizedState}function kx(t,e,n){var r=fn(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Om(t))Dm(e,n);else if(n=mm(t,e,n,r),n!==null){var i=Oe();xt(n,t,r,i),Lm(n,e,r)}}function Nx(t,e,n){var r=fn(t),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Om(t))Dm(e,i);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var o=e.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,vt(a,o)){var l=e.interleaved;l===null?(i.next=i,Ec(e)):(i.next=l.next,l.next=i),e.interleaved=i;return}}catch{}finally{}n=mm(t,e,i,r),n!==null&&(i=Oe(),xt(n,t,r,i),Lm(n,e,r))}}function Om(t){var e=t.alternate;return t===ie||e!==null&&e===ie}function Dm(t,e){gi=xo=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Lm(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,gc(t,n)}}var vo={readContext:ot,useCallback:_e,useContext:_e,useEffect:_e,useImperativeHandle:_e,useInsertionEffect:_e,useLayoutEffect:_e,useMemo:_e,useReducer:_e,useRef:_e,useState:_e,useDebugValue:_e,useDeferredValue:_e,useTransition:_e,useMutableSource:_e,useSyncExternalStore:_e,useId:_e,unstable_isNewReconciler:!1},jx={readContext:ot,useCallback:function(t,e){return _t().memoizedState=[t,e===void 0?null:e],t},useContext:ot,useEffect:nd,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Us(4194308,4,Cm.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Us(4194308,4,t,e)},useInsertionEffect:function(t,e){return Us(4,2,t,e)},useMemo:function(t,e){var n=_t();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=_t();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=kx.bind(null,ie,t),[r.memoizedState,t]},useRef:function(t){var e=_t();return t={current:t},e.memoizedState=t},useState:td,useDebugValue:Ac,useDeferredValue:function(t){return _t().memoizedState=t},useTransition:function(){var t=td(!1),e=t[0];return t=_x.bind(null,t[1]),_t().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=ie,i=_t();if(ne){if(n===void 0)throw Error(C(407));n=n()}else{if(n=e(),ve===null)throw Error(C(349));Vn&30||ym(r,e,n)}i.memoizedState=n;var s={value:n,getSnapshot:e};return i.queue=s,nd(wm.bind(null,r,s,t),[t]),r.flags|=2048,Fi(9,bm.bind(null,r,s,n,e),void 0,null),n},useId:function(){var t=_t(),e=ve.identifierPrefix;if(ne){var n=Ft,r=zt;n=(r&~(1<<32-gt(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=$i++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Sx++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Cx={readContext:ot,useCallback:Pm,useContext:ot,useEffect:Fc,useImperativeHandle:Mm,useInsertionEffect:Nm,useLayoutEffect:jm,useMemo:Em,useReducer:Ea,useRef:km,useState:function(){return Ea(zi)},useDebugValue:Ac,useDeferredValue:function(t){var e=at();return Tm(e,fe.memoizedState,t)},useTransition:function(){var t=Ea(zi)[0],e=at().memoizedState;return[t,e]},useMutableSource:xm,useSyncExternalStore:vm,useId:Rm,unstable_isNewReconciler:!1},Mx={readContext:ot,useCallback:Pm,useContext:ot,useEffect:Fc,useImperativeHandle:Mm,useInsertionEffect:Nm,useLayoutEffect:jm,useMemo:Em,useReducer:Ta,useRef:km,useState:function(){return Ta(zi)},useDebugValue:Ac,useDeferredValue:function(t){var e=at();return fe===null?e.memoizedState=t:Tm(e,fe.memoizedState,t)},useTransition:function(){var t=Ta(zi)[0],e=at().memoizedState;return[t,e]},useMutableSource:xm,useSyncExternalStore:vm,useId:Rm,unstable_isNewReconciler:!1};function dt(t,e){if(t&&t.defaultProps){e=se({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function Pl(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:se({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Qo={isMounted:function(t){return(t=t._reactInternals)?Zn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=Oe(),i=fn(t),s=It(r,i);s.payload=e,n!=null&&(s.callback=n),e=dn(t,s,i),e!==null&&(xt(e,t,i,r),As(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=Oe(),i=fn(t),s=It(r,i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=dn(t,s,i),e!==null&&(xt(e,t,i,r),As(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Oe(),r=fn(t),i=It(n,r);i.tag=2,e!=null&&(i.callback=e),e=dn(t,i,r),e!==null&&(xt(e,t,r,n),As(e,t,r))}};function rd(t,e,n,r,i,s,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,s,o):e.prototype&&e.prototype.isPureReactComponent?!Ei(n,r)||!Ei(i,s):!0}function $m(t,e,n){var r=!1,i=wn,s=e.contextType;return typeof s=="object"&&s!==null?s=ot(s):(i=Be(e)?Wn:Me.current,r=e.contextTypes,s=(r=r!=null)?Cr(t,i):wn),e=new e(n,s),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Qo,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=s),e}function id(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Qo.enqueueReplaceState(e,e.state,null)}function El(t,e,n,r){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs={},Tc(t);var s=e.contextType;typeof s=="object"&&s!==null?i.context=ot(s):(s=Be(e)?Wn:Me.current,i.context=Cr(t,s)),i.state=t.memoizedState,s=e.getDerivedStateFromProps,typeof s=="function"&&(Pl(t,e,s,n),i.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(e=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),e!==i.state&&Qo.enqueueReplaceState(i,i.state,null),po(t,n,i,r),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308)}function Tr(t,e){try{var n="",r=e;do n+=n0(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:t,source:e,stack:i,digest:null}}function Ra(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Tl(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Px=typeof WeakMap=="function"?WeakMap:Map;function zm(t,e,n){n=It(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){bo||(bo=!0,Ul=r),Tl(t,e)},n}function Fm(t,e,n){n=It(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var i=e.value;n.payload=function(){return r(i)},n.callback=function(){Tl(t,e)}}var s=t.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Tl(t,e),typeof r!="function"&&(hn===null?hn=new Set([this]):hn.add(this));var o=e.stack;this.componentDidCatch(e.value,{componentStack:o!==null?o:""})}),n}function sd(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Px;var i=new Set;r.set(e,i)}else i=r.get(e),i===void 0&&(i=new Set,r.set(e,i));i.has(n)||(i.add(n),t=Wx.bind(null,t,e,n),e.then(t,t))}function od(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function ad(t,e,n,r,i){return t.mode&1?(t.flags|=65536,t.lanes=i,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=It(-1,1),e.tag=2,dn(n,e,1))),n.lanes|=1),t)}var Ex=Yt.ReactCurrentOwner,Ie=!1;function Re(t,e,n,r){e.child=t===null?fm(e,null,n,r):Pr(e,t.child,n,r)}function ld(t,e,n,r,i){n=n.render;var s=e.ref;return br(e,i),r=$c(t,e,n,r,s,i),n=zc(),t!==null&&!Ie?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Bt(t,e,i)):(ne&&n&&kc(e),e.flags|=1,Re(t,e,r,i),e.child)}function cd(t,e,n,r,i){if(t===null){var s=n.type;return typeof s=="function"&&!Xc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=s,Am(t,e,s,r,i)):(t=Vs(n.type,null,r,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!(t.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Ei,n(o,r)&&t.ref===e.ref)return Bt(t,e,i)}return e.flags|=1,t=mn(s,r),t.ref=e.ref,t.return=e,e.child=t}function Am(t,e,n,r,i){if(t!==null){var s=t.memoizedProps;if(Ei(s,r)&&t.ref===e.ref)if(Ie=!1,e.pendingProps=r=s,(t.lanes&i)!==0)t.flags&131072&&(Ie=!0);else return e.lanes=t.lanes,Bt(t,e,i)}return Rl(t,e,n,r,i)}function Im(t,e,n){var r=e.pendingProps,i=r.children,s=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},K(pr,Xe),Xe|=n;else{if(!(n&1073741824))return t=s!==null?s.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,K(pr,Xe),Xe|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,K(pr,Xe),Xe|=r}else s!==null?(r=s.baseLanes|n,e.memoizedState=null):r=n,K(pr,Xe),Xe|=r;return Re(t,e,i,n),e.child}function Um(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function Rl(t,e,n,r,i){var s=Be(n)?Wn:Me.current;return s=Cr(e,s),br(e,i),n=$c(t,e,n,r,s,i),r=zc(),t!==null&&!Ie?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Bt(t,e,i)):(ne&&r&&kc(e),e.flags|=1,Re(t,e,n,i),e.child)}function ud(t,e,n,r,i){if(Be(n)){var s=!0;co(e)}else s=!1;if(br(e,i),e.stateNode===null)Hs(t,e),$m(e,n,r),El(e,n,r,i),r=!0;else if(t===null){var o=e.stateNode,a=e.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=Be(n)?Wn:Me.current,u=Cr(e,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&id(e,o,r,u),qt=!1;var f=e.memoizedState;o.state=f,po(e,r,o,i),l=e.memoizedState,a!==r||f!==l||We.current||qt?(typeof d=="function"&&(Pl(e,n,d,r),l=e.memoizedState),(a=qt||rd(e,n,a,r,f,l,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{o=e.stateNode,pm(t,e),a=e.memoizedProps,u=e.type===e.elementType?a:dt(e.type,a),o.props=u,h=e.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=ot(l):(l=Be(n)?Wn:Me.current,l=Cr(e,l));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==l)&&id(e,o,r,l),qt=!1,f=e.memoizedState,o.state=f,po(e,r,o,i);var v=e.memoizedState;a!==h||f!==v||We.current||qt?(typeof m=="function"&&(Pl(e,n,m,r),v=e.memoizedState),(u=qt||rd(e,n,u,r,f,v,l)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),r=!1)}return Ol(t,e,n,r,s,i)}function Ol(t,e,n,r,i,s){Um(t,e);var o=(e.flags&128)!==0;if(!r&&!o)return i&&Ku(e,n,!1),Bt(t,e,s);r=e.stateNode,Ex.current=e;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&o?(e.child=Pr(e,t.child,null,s),e.child=Pr(e,null,a,s)):Re(t,e,a,s),e.memoizedState=r.state,i&&Ku(e,n,!0),e.child}function Hm(t){var e=t.stateNode;e.pendingContext?Qu(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Qu(t,e.context,!1),Rc(t,e.containerInfo)}function dd(t,e,n,r,i){return Mr(),jc(i),e.flags|=256,Re(t,e,n,r),e.child}var Dl={dehydrated:null,treeContext:null,retryLane:0};function Ll(t){return{baseLanes:t,cachePool:null,transitions:null}}function Wm(t,e,n){var r=e.pendingProps,i=re.current,s=!1,o=(e.flags&128)!==0,a;if((a=o)||(a=t!==null&&t.memoizedState===null?!1:(i&2)!==0),a?(s=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(i|=1),K(re,i&1),t===null)return Cl(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(o=r.children,t=r.fallback,s?(r=e.mode,s=e.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=qo(o,r,0,null),t=Un(t,r,n,null),s.return=e,t.return=e,s.sibling=t,e.child=s,e.child.memoizedState=Ll(n),e.memoizedState=Dl,t):Ic(e,o));if(i=t.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Tx(t,e,o,r,a,i,n);if(s){s=r.fallback,o=e.mode,i=t.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&e.child!==i?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=mn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=mn(a,s):(s=Un(s,o,n,null),s.flags|=2),s.return=e,r.return=e,r.sibling=s,e.child=r,r=s,s=e.child,o=t.child.memoizedState,o=o===null?Ll(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=t.childLanes&~n,e.memoizedState=Dl,r}return s=t.child,t=s.sibling,r=mn(s,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function Ic(t,e){return e=qo({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function xs(t,e,n,r){return r!==null&&jc(r),Pr(e,t.child,null,n),t=Ic(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Tx(t,e,n,r,i,s,o){if(n)return e.flags&256?(e.flags&=-257,r=Ra(Error(C(422))),xs(t,e,o,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(s=r.fallback,i=e.mode,r=qo({mode:"visible",children:r.children},i,0,null),s=Un(s,i,o,null),s.flags|=2,r.return=e,s.return=e,r.sibling=s,e.child=r,e.mode&1&&Pr(e,t.child,null,o),e.child.memoizedState=Ll(o),e.memoizedState=Dl,s);if(!(e.mode&1))return xs(t,e,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(C(419)),r=Ra(s,r,void 0),xs(t,e,o,r)}if(a=(o&t.childLanes)!==0,Ie||a){if(r=ve,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Wt(t,i),xt(r,t,i,-1))}return Yc(),r=Ra(Error(C(421))),xs(t,e,o,r)}return i.data==="$?"?(e.flags|=128,e.child=t.child,e=Bx.bind(null,t),i._reactRetry=e,null):(t=s.treeContext,Qe=un(i.nextSibling),Ke=e,ne=!0,ft=null,t!==null&&(et[tt++]=zt,et[tt++]=Ft,et[tt++]=Bn,zt=t.id,Ft=t.overflow,Bn=e),e=Ic(e,r.children),e.flags|=4096,e)}function hd(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Ml(t.return,e,n)}function Oa(t,e,n,r,i){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Bm(t,e,n){var r=e.pendingProps,i=r.revealOrder,s=r.tail;if(Re(t,e,r.children,n),r=re.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&hd(t,n,e);else if(t.tag===19)hd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(K(re,r),!(e.mode&1))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&go(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),Oa(e,!1,i,n,s);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&go(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}Oa(e,!0,n,null,s);break;case"together":Oa(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Hs(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Bt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Yn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(C(153));if(e.child!==null){for(t=e.child,n=mn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=mn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Rx(t,e,n){switch(e.tag){case 3:Hm(e),Mr();break;case 5:gm(e);break;case 1:Be(e.type)&&co(e);break;case 4:Rc(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,i=e.memoizedProps.value;K(fo,r._currentValue),r._currentValue=i;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(K(re,re.current&1),e.flags|=128,null):n&e.child.childLanes?Wm(t,e,n):(K(re,re.current&1),t=Bt(t,e,n),t!==null?t.sibling:null);K(re,re.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return Bm(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),K(re,re.current),r)break;return null;case 22:case 23:return e.lanes=0,Im(t,e,n)}return Bt(t,e,n)}var Vm,$l,Ym,Xm;Vm=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};$l=function(){};Ym=function(t,e,n,r){var i=t.memoizedProps;if(i!==r){t=e.stateNode,Fn(Ct.current);var s=null;switch(n){case"input":i=il(t,i),r=il(t,r),s=[];break;case"select":i=se({},i,{value:void 0}),r=se({},r,{value:void 0}),s=[];break;case"textarea":i=al(t,i),r=al(t,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=ao)}cl(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(_i.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(_i.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Z("scroll",t),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(e.updateQueue=u)&&(e.flags|=4)}};Xm=function(t,e,n,r){n!==r&&(e.flags|=4)};function Xr(t,e){if(!ne)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function ke(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function Ox(t,e,n){var r=e.pendingProps;switch(Nc(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ke(e),null;case 1:return Be(e.type)&&lo(),ke(e),null;case 3:return r=e.stateNode,Er(),ee(We),ee(Me),Dc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(ps(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,ft!==null&&(Bl(ft),ft=null))),$l(t,e),ke(e),null;case 5:Oc(e);var i=Fn(Li.current);if(n=e.type,t!==null&&e.stateNode!=null)Ym(t,e,n,r,i),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(C(166));return ke(e),null}if(t=Fn(Ct.current),ps(e)){r=e.stateNode,n=e.type;var s=e.memoizedProps;switch(r[Nt]=e,r[Oi]=s,t=(e.mode&1)!==0,n){case"dialog":Z("cancel",r),Z("close",r);break;case"iframe":case"object":case"embed":Z("load",r);break;case"video":case"audio":for(i=0;i<si.length;i++)Z(si[i],r);break;case"source":Z("error",r);break;case"img":case"image":case"link":Z("error",r),Z("load",r);break;case"details":Z("toggle",r);break;case"input":wu(r,s),Z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Z("invalid",r);break;case"textarea":_u(r,s),Z("invalid",r)}cl(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&ms(r.textContent,a,t),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&ms(r.textContent,a,t),i=["children",""+a]):_i.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&Z("scroll",r)}switch(n){case"input":os(r),Su(r,s,!0);break;case"textarea":os(r),ku(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ao)}r=i,e.updateQueue=r,r!==null&&(e.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=wf(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=o.createElement(n,{is:r.is}):(t=o.createElement(n),n==="select"&&(o=t,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):t=o.createElementNS(t,n),t[Nt]=e,t[Oi]=r,Vm(t,e,!1,!1),e.stateNode=t;e:{switch(o=ul(n,r),n){case"dialog":Z("cancel",t),Z("close",t),i=r;break;case"iframe":case"object":case"embed":Z("load",t),i=r;break;case"video":case"audio":for(i=0;i<si.length;i++)Z(si[i],t);i=r;break;case"source":Z("error",t),i=r;break;case"img":case"image":case"link":Z("error",t),Z("load",t),i=r;break;case"details":Z("toggle",t),i=r;break;case"input":wu(t,r),i=il(t,r),Z("invalid",t);break;case"option":i=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},i=se({},r,{value:void 0}),Z("invalid",t);break;case"textarea":_u(t,r),i=al(t,r),Z("invalid",t);break;default:i=r}cl(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?kf(t,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Sf(t,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ki(t,l):typeof l=="number"&&ki(t,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(_i.hasOwnProperty(s)?l!=null&&s==="onScroll"&&Z("scroll",t):l!=null&&uc(t,s,l,o))}switch(n){case"input":os(t),Su(t,r,!1);break;case"textarea":os(t),ku(t);break;case"option":r.value!=null&&t.setAttribute("value",""+bn(r.value));break;case"select":t.multiple=!!r.multiple,s=r.value,s!=null?gr(t,!!r.multiple,s,!1):r.defaultValue!=null&&gr(t,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(t.onclick=ao)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return ke(e),null;case 6:if(t&&e.stateNode!=null)Xm(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(C(166));if(n=Fn(Li.current),Fn(Ct.current),ps(e)){if(r=e.stateNode,n=e.memoizedProps,r[Nt]=e,(s=r.nodeValue!==n)&&(t=Ke,t!==null))switch(t.tag){case 3:ms(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&ms(r.nodeValue,n,(t.mode&1)!==0)}s&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Nt]=e,e.stateNode=r}return ke(e),null;case 13:if(ee(re),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(ne&&Qe!==null&&e.mode&1&&!(e.flags&128))dm(),Mr(),e.flags|=98560,s=!1;else if(s=ps(e),r!==null&&r.dehydrated!==null){if(t===null){if(!s)throw Error(C(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(C(317));s[Nt]=e}else Mr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;ke(e),s=!1}else ft!==null&&(Bl(ft),ft=null),s=!0;if(!s)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||re.current&1?pe===0&&(pe=3):Yc())),e.updateQueue!==null&&(e.flags|=4),ke(e),null);case 4:return Er(),$l(t,e),t===null&&Ti(e.stateNode.containerInfo),ke(e),null;case 10:return Pc(e.type._context),ke(e),null;case 17:return Be(e.type)&&lo(),ke(e),null;case 19:if(ee(re),s=e.memoizedState,s===null)return ke(e),null;if(r=(e.flags&128)!==0,o=s.rendering,o===null)if(r)Xr(s,!1);else{if(pe!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(o=go(t),o!==null){for(e.flags|=128,Xr(s,!1),r=o.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)s=n,t=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=t,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,t=o.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return K(re,re.current&1|2),e.child}t=t.sibling}s.tail!==null&&le()>Rr&&(e.flags|=128,r=!0,Xr(s,!1),e.lanes=4194304)}else{if(!r)if(t=go(o),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),Xr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!ne)return ke(e),null}else 2*le()-s.renderingStartTime>Rr&&n!==1073741824&&(e.flags|=128,r=!0,Xr(s,!1),e.lanes=4194304);s.isBackwards?(o.sibling=e.child,e.child=o):(n=s.last,n!==null?n.sibling=o:e.child=o,s.last=o)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=le(),e.sibling=null,n=re.current,K(re,r?n&1|2:n&1),e):(ke(e),null);case 22:case 23:return Vc(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?Xe&1073741824&&(ke(e),e.subtreeFlags&6&&(e.flags|=8192)):ke(e),null;case 24:return null;case 25:return null}throw Error(C(156,e.tag))}function Dx(t,e){switch(Nc(e),e.tag){case 1:return Be(e.type)&&lo(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Er(),ee(We),ee(Me),Dc(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return Oc(e),null;case 13:if(ee(re),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(C(340));Mr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return ee(re),null;case 4:return Er(),null;case 10:return Pc(e.type._context),null;case 22:case 23:return Vc(),null;case 24:return null;default:return null}}var vs=!1,je=!1,Lx=typeof WeakSet=="function"?WeakSet:Set,R=null;function mr(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(t,e,r)}else n.current=null}function zl(t,e,n){try{n()}catch(r){oe(t,e,r)}}var fd=!1;function $x(t,e){if(bl=io,t=Zf(),_c(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,d=0,h=t,f=null;t:for(;;){for(var m;h!==n||i!==0&&h.nodeType!==3||(a=o+i),h!==s||r!==0&&h.nodeType!==3||(l=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(m=h.firstChild)!==null;)f=h,h=m;for(;;){if(h===t)break t;if(f===n&&++u===i&&(a=o),f===s&&++d===r&&(l=o),(m=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=m}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(wl={focusedElem:t,selectionRange:n},io=!1,R=e;R!==null;)if(e=R,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,R=t;else for(;R!==null;){e=R;try{var v=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var g=v.memoizedProps,b=v.memoizedState,p=e.stateNode,x=p.getSnapshotBeforeUpdate(e.elementType===e.type?g:dt(e.type,g),b);p.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var y=e.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){oe(e,e.return,w)}if(t=e.sibling,t!==null){t.return=e.return,R=t;break}R=e.return}return v=fd,fd=!1,v}function xi(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&t)===t){var s=i.destroy;i.destroy=void 0,s!==void 0&&zl(e,n,s)}i=i.next}while(i!==r)}}function Ko(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function Fl(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function Qm(t){var e=t.alternate;e!==null&&(t.alternate=null,Qm(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[Nt],delete e[Oi],delete e[kl],delete e[vx],delete e[yx])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Km(t){return t.tag===5||t.tag===3||t.tag===4}function md(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Km(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Al(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=ao));else if(r!==4&&(t=t.child,t!==null))for(Al(t,e,n),t=t.sibling;t!==null;)Al(t,e,n),t=t.sibling}function Il(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(Il(t,e,n),t=t.sibling;t!==null;)Il(t,e,n),t=t.sibling}var ye=null,ht=!1;function Xt(t,e,n){for(n=n.child;n!==null;)Gm(t,e,n),n=n.sibling}function Gm(t,e,n){if(jt&&typeof jt.onCommitFiberUnmount=="function")try{jt.onCommitFiberUnmount(Uo,n)}catch{}switch(n.tag){case 5:je||mr(n,e);case 6:var r=ye,i=ht;ye=null,Xt(t,e,n),ye=r,ht=i,ye!==null&&(ht?(t=ye,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):ye.removeChild(n.stateNode));break;case 18:ye!==null&&(ht?(t=ye,n=n.stateNode,t.nodeType===8?ja(t.parentNode,n):t.nodeType===1&&ja(t,n),Mi(t)):ja(ye,n.stateNode));break;case 4:r=ye,i=ht,ye=n.stateNode.containerInfo,ht=!0,Xt(t,e,n),ye=r,ht=i;break;case 0:case 11:case 14:case 15:if(!je&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&zl(n,e,o),i=i.next}while(i!==r)}Xt(t,e,n);break;case 1:if(!je&&(mr(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){oe(n,e,a)}Xt(t,e,n);break;case 21:Xt(t,e,n);break;case 22:n.mode&1?(je=(r=je)||n.memoizedState!==null,Xt(t,e,n),je=r):Xt(t,e,n);break;default:Xt(t,e,n)}}function pd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Lx),e.forEach(function(r){var i=Vx.bind(null,t,r);n.has(r)||(n.add(r),r.then(i,i))})}}function ut(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=t,o=e,a=o;e:for(;a!==null;){switch(a.tag){case 5:ye=a.stateNode,ht=!1;break e;case 3:ye=a.stateNode.containerInfo,ht=!0;break e;case 4:ye=a.stateNode.containerInfo,ht=!0;break e}a=a.return}if(ye===null)throw Error(C(160));Gm(s,o,i),ye=null,ht=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){oe(i,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)qm(e,t),e=e.sibling}function qm(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(ut(e,t),bt(t),r&4){try{xi(3,t,t.return),Ko(3,t)}catch(g){oe(t,t.return,g)}try{xi(5,t,t.return)}catch(g){oe(t,t.return,g)}}break;case 1:ut(e,t),bt(t),r&512&&n!==null&&mr(n,n.return);break;case 5:if(ut(e,t),bt(t),r&512&&n!==null&&mr(n,n.return),t.flags&32){var i=t.stateNode;try{ki(i,"")}catch(g){oe(t,t.return,g)}}if(r&4&&(i=t.stateNode,i!=null)){var s=t.memoizedProps,o=n!==null?n.memoizedProps:s,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&yf(i,s),ul(a,o);var u=ul(a,s);for(o=0;o<l.length;o+=2){var d=l[o],h=l[o+1];d==="style"?kf(i,h):d==="dangerouslySetInnerHTML"?Sf(i,h):d==="children"?ki(i,h):uc(i,d,h,u)}switch(a){case"input":sl(i,s);break;case"textarea":bf(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var m=s.value;m!=null?gr(i,!!s.multiple,m,!1):f!==!!s.multiple&&(s.defaultValue!=null?gr(i,!!s.multiple,s.defaultValue,!0):gr(i,!!s.multiple,s.multiple?[]:"",!1))}i[Oi]=s}catch(g){oe(t,t.return,g)}}break;case 6:if(ut(e,t),bt(t),r&4){if(t.stateNode===null)throw Error(C(162));i=t.stateNode,s=t.memoizedProps;try{i.nodeValue=s}catch(g){oe(t,t.return,g)}}break;case 3:if(ut(e,t),bt(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Mi(e.containerInfo)}catch(g){oe(t,t.return,g)}break;case 4:ut(e,t),bt(t);break;case 13:ut(e,t),bt(t),i=t.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Wc=le())),r&4&&pd(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(je=(u=je)||d,ut(e,t),je=u):ut(e,t),bt(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(R=t,d=t.child;d!==null;){for(h=R=d;R!==null;){switch(f=R,m=f.child,f.tag){case 0:case 11:case 14:case 15:xi(4,f,f.return);break;case 1:mr(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{e=r,v.props=e.memoizedProps,v.state=e.memoizedState,v.componentWillUnmount()}catch(g){oe(r,n,g)}}break;case 5:mr(f,f.return);break;case 22:if(f.memoizedState!==null){xd(h);continue}}m!==null?(m.return=f,R=m):xd(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=_f("display",o))}catch(g){oe(t,t.return,g)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(g){oe(t,t.return,g)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:ut(e,t),bt(t),r&4&&pd(t);break;case 21:break;default:ut(e,t),bt(t)}}function bt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(Km(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(ki(i,""),r.flags&=-33);var s=md(t);Il(t,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=md(t);Al(t,a,o);break;default:throw Error(C(161))}}catch(l){oe(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function zx(t,e,n){R=t,Zm(t)}function Zm(t,e,n){for(var r=(t.mode&1)!==0;R!==null;){var i=R,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||vs;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||je;a=vs;var u=je;if(vs=o,(je=l)&&!u)for(R=i;R!==null;)o=R,l=o.child,o.tag===22&&o.memoizedState!==null?vd(i):l!==null?(l.return=o,R=l):vd(i);for(;s!==null;)R=s,Zm(s),s=s.sibling;R=i,vs=a,je=u}gd(t)}else i.subtreeFlags&8772&&s!==null?(s.return=i,R=s):gd(t)}}function gd(t){for(;R!==null;){var e=R;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:je||Ko(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!je)if(n===null)r.componentDidMount();else{var i=e.elementType===e.type?n.memoizedProps:dt(e.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=e.updateQueue;s!==null&&ed(e,s,r);break;case 3:var o=e.updateQueue;if(o!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}ed(e,o,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Mi(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}je||e.flags&512&&Fl(e)}catch(f){oe(e,e.return,f)}}if(e===t){R=null;break}if(n=e.sibling,n!==null){n.return=e.return,R=n;break}R=e.return}}function xd(t){for(;R!==null;){var e=R;if(e===t){R=null;break}var n=e.sibling;if(n!==null){n.return=e.return,R=n;break}R=e.return}}function vd(t){for(;R!==null;){var e=R;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Ko(4,e)}catch(l){oe(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var i=e.return;try{r.componentDidMount()}catch(l){oe(e,i,l)}}var s=e.return;try{Fl(e)}catch(l){oe(e,s,l)}break;case 5:var o=e.return;try{Fl(e)}catch(l){oe(e,o,l)}}}catch(l){oe(e,e.return,l)}if(e===t){R=null;break}var a=e.sibling;if(a!==null){a.return=e.return,R=a;break}R=e.return}}var Fx=Math.ceil,yo=Yt.ReactCurrentDispatcher,Uc=Yt.ReactCurrentOwner,it=Yt.ReactCurrentBatchConfig,F=0,ve=null,ue=null,be=0,Xe=0,pr=kn(0),pe=0,Ai=null,Yn=0,Go=0,Hc=0,vi=null,Fe=null,Wc=0,Rr=1/0,Ot=null,bo=!1,Ul=null,hn=null,ys=!1,tn=null,wo=0,yi=0,Hl=null,Ws=-1,Bs=0;function Oe(){return F&6?le():Ws!==-1?Ws:Ws=le()}function fn(t){return t.mode&1?F&2&&be!==0?be&-be:wx.transition!==null?(Bs===0&&(Bs=$f()),Bs):(t=B,t!==0||(t=window.event,t=t===void 0?16:Wf(t.type)),t):1}function xt(t,e,n,r){if(50<yi)throw yi=0,Hl=null,Error(C(185));Ki(t,n,r),(!(F&2)||t!==ve)&&(t===ve&&(!(F&2)&&(Go|=n),pe===4&&Jt(t,be)),Ve(t,r),n===1&&F===0&&!(e.mode&1)&&(Rr=le()+500,Yo&&Nn()))}function Ve(t,e){var n=t.callbackNode;w0(t,e);var r=ro(t,t===ve?be:0);if(r===0)n!==null&&Cu(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&Cu(n),e===1)t.tag===0?bx(yd.bind(null,t)):lm(yd.bind(null,t)),gx(function(){!(F&6)&&Nn()}),n=null;else{switch(zf(r)){case 1:n=pc;break;case 4:n=Df;break;case 16:n=no;break;case 536870912:n=Lf;break;default:n=no}n=op(n,Jm.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function Jm(t,e){if(Ws=-1,Bs=0,F&6)throw Error(C(327));var n=t.callbackNode;if(wr()&&t.callbackNode!==n)return null;var r=ro(t,t===ve?be:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=So(t,r);else{e=r;var i=F;F|=2;var s=tp();(ve!==t||be!==e)&&(Ot=null,Rr=le()+500,In(t,e));do try{Ux();break}catch(a){ep(t,a)}while(!0);Mc(),yo.current=s,F=i,ue!==null?e=0:(ve=null,be=0,e=pe)}if(e!==0){if(e===2&&(i=pl(t),i!==0&&(r=i,e=Wl(t,i))),e===1)throw n=Ai,In(t,0),Jt(t,r),Ve(t,le()),n;if(e===6)Jt(t,r);else{if(i=t.current.alternate,!(r&30)&&!Ax(i)&&(e=So(t,r),e===2&&(s=pl(t),s!==0&&(r=s,e=Wl(t,s))),e===1))throw n=Ai,In(t,0),Jt(t,r),Ve(t,le()),n;switch(t.finishedWork=i,t.finishedLanes=r,e){case 0:case 1:throw Error(C(345));case 2:Rn(t,Fe,Ot);break;case 3:if(Jt(t,r),(r&130023424)===r&&(e=Wc+500-le(),10<e)){if(ro(t,0)!==0)break;if(i=t.suspendedLanes,(i&r)!==r){Oe(),t.pingedLanes|=t.suspendedLanes&i;break}t.timeoutHandle=_l(Rn.bind(null,t,Fe,Ot),e);break}Rn(t,Fe,Ot);break;case 4:if(Jt(t,r),(r&4194240)===r)break;for(e=t.eventTimes,i=-1;0<r;){var o=31-gt(r);s=1<<o,o=e[o],o>i&&(i=o),r&=~s}if(r=i,r=le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Fx(r/1960))-r,10<r){t.timeoutHandle=_l(Rn.bind(null,t,Fe,Ot),r);break}Rn(t,Fe,Ot);break;case 5:Rn(t,Fe,Ot);break;default:throw Error(C(329))}}}return Ve(t,le()),t.callbackNode===n?Jm.bind(null,t):null}function Wl(t,e){var n=vi;return t.current.memoizedState.isDehydrated&&(In(t,e).flags|=256),t=So(t,e),t!==2&&(e=Fe,Fe=n,e!==null&&Bl(e)),t}function Bl(t){Fe===null?Fe=t:Fe.push.apply(Fe,t)}function Ax(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!vt(s(),i))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Jt(t,e){for(e&=~Hc,e&=~Go,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-gt(e),r=1<<n;t[n]=-1,e&=~r}}function yd(t){if(F&6)throw Error(C(327));wr();var e=ro(t,0);if(!(e&1))return Ve(t,le()),null;var n=So(t,e);if(t.tag!==0&&n===2){var r=pl(t);r!==0&&(e=r,n=Wl(t,r))}if(n===1)throw n=Ai,In(t,0),Jt(t,e),Ve(t,le()),n;if(n===6)throw Error(C(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,Rn(t,Fe,Ot),Ve(t,le()),null}function Bc(t,e){var n=F;F|=1;try{return t(e)}finally{F=n,F===0&&(Rr=le()+500,Yo&&Nn())}}function Xn(t){tn!==null&&tn.tag===0&&!(F&6)&&wr();var e=F;F|=1;var n=it.transition,r=B;try{if(it.transition=null,B=1,t)return t()}finally{B=r,it.transition=n,F=e,!(F&6)&&Nn()}}function Vc(){Xe=pr.current,ee(pr)}function In(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,px(n)),ue!==null)for(n=ue.return;n!==null;){var r=n;switch(Nc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&lo();break;case 3:Er(),ee(We),ee(Me),Dc();break;case 5:Oc(r);break;case 4:Er();break;case 13:ee(re);break;case 19:ee(re);break;case 10:Pc(r.type._context);break;case 22:case 23:Vc()}n=n.return}if(ve=t,ue=t=mn(t.current,null),be=Xe=e,pe=0,Ai=null,Hc=Go=Yn=0,Fe=vi=null,zn!==null){for(e=0;e<zn.length;e++)if(n=zn[e],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}zn=null}return t}function ep(t,e){do{var n=ue;try{if(Mc(),Is.current=vo,xo){for(var r=ie.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}xo=!1}if(Vn=0,xe=fe=ie=null,gi=!1,$i=0,Uc.current=null,n===null||n.return===null){pe=1,Ai=e,ue=null;break}e:{var s=t,o=n.return,a=n,l=e;if(e=be,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=od(o);if(m!==null){m.flags&=-257,ad(m,o,a,s,e),m.mode&1&&sd(s,u,e),e=m,l=u;var v=e.updateQueue;if(v===null){var g=new Set;g.add(l),e.updateQueue=g}else v.add(l);break e}else{if(!(e&1)){sd(s,u,e),Yc();break e}l=Error(C(426))}}else if(ne&&a.mode&1){var b=od(o);if(b!==null){!(b.flags&65536)&&(b.flags|=256),ad(b,o,a,s,e),jc(Tr(l,a));break e}}s=l=Tr(l,a),pe!==4&&(pe=2),vi===null?vi=[s]:vi.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,e&=-e,s.lanes|=e;var p=zm(s,l,e);Ju(s,p);break e;case 1:a=l;var x=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof x.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(hn===null||!hn.has(y)))){s.flags|=65536,e&=-e,s.lanes|=e;var w=Fm(s,a,e);Ju(s,w);break e}}s=s.return}while(s!==null)}rp(n)}catch(S){e=S,ue===n&&n!==null&&(ue=n=n.return);continue}break}while(!0)}function tp(){var t=yo.current;return yo.current=vo,t===null?vo:t}function Yc(){(pe===0||pe===3||pe===2)&&(pe=4),ve===null||!(Yn&268435455)&&!(Go&268435455)||Jt(ve,be)}function So(t,e){var n=F;F|=2;var r=tp();(ve!==t||be!==e)&&(Ot=null,In(t,e));do try{Ix();break}catch(i){ep(t,i)}while(!0);if(Mc(),F=n,yo.current=r,ue!==null)throw Error(C(261));return ve=null,be=0,pe}function Ix(){for(;ue!==null;)np(ue)}function Ux(){for(;ue!==null&&!h0();)np(ue)}function np(t){var e=sp(t.alternate,t,Xe);t.memoizedProps=t.pendingProps,e===null?rp(t):ue=e,Uc.current=null}function rp(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Dx(n,e),n!==null){n.flags&=32767,ue=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{pe=6,ue=null;return}}else if(n=Ox(n,e,Xe),n!==null){ue=n;return}if(e=e.sibling,e!==null){ue=e;return}ue=e=t}while(e!==null);pe===0&&(pe=5)}function Rn(t,e,n){var r=B,i=it.transition;try{it.transition=null,B=1,Hx(t,e,n,r)}finally{it.transition=i,B=r}return null}function Hx(t,e,n,r){do wr();while(tn!==null);if(F&6)throw Error(C(327));n=t.finishedWork;var i=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(C(177));t.callbackNode=null,t.callbackPriority=0;var s=n.lanes|n.childLanes;if(S0(t,s),t===ve&&(ue=ve=null,be=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ys||(ys=!0,op(no,function(){return wr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=it.transition,it.transition=null;var o=B;B=1;var a=F;F|=4,Uc.current=null,$x(t,n),qm(n,t),lx(wl),io=!!bl,wl=bl=null,t.current=n,zx(n),f0(),F=a,B=o,it.transition=s}else t.current=n;if(ys&&(ys=!1,tn=t,wo=i),s=t.pendingLanes,s===0&&(hn=null),g0(n.stateNode),Ve(t,le()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)i=e[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(bo)throw bo=!1,t=Ul,Ul=null,t;return wo&1&&t.tag!==0&&wr(),s=t.pendingLanes,s&1?t===Hl?yi++:(yi=0,Hl=t):yi=0,Nn(),null}function wr(){if(tn!==null){var t=zf(wo),e=it.transition,n=B;try{if(it.transition=null,B=16>t?16:t,tn===null)var r=!1;else{if(t=tn,tn=null,wo=0,F&6)throw Error(C(331));var i=F;for(F|=4,R=t.current;R!==null;){var s=R,o=s.child;if(R.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(R=u;R!==null;){var d=R;switch(d.tag){case 0:case 11:case 15:xi(8,d,s)}var h=d.child;if(h!==null)h.return=d,R=h;else for(;R!==null;){d=R;var f=d.sibling,m=d.return;if(Qm(d),d===u){R=null;break}if(f!==null){f.return=m,R=f;break}R=m}}}var v=s.alternate;if(v!==null){var g=v.child;if(g!==null){v.child=null;do{var b=g.sibling;g.sibling=null,g=b}while(g!==null)}}R=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,R=o;else e:for(;R!==null;){if(s=R,s.flags&2048)switch(s.tag){case 0:case 11:case 15:xi(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,R=p;break e}R=s.return}}var x=t.current;for(R=x;R!==null;){o=R;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,R=y;else e:for(o=x;R!==null;){if(a=R,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ko(9,a)}}catch(S){oe(a,a.return,S)}if(a===o){R=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,R=w;break e}R=a.return}}if(F=i,Nn(),jt&&typeof jt.onPostCommitFiberRoot=="function")try{jt.onPostCommitFiberRoot(Uo,t)}catch{}r=!0}return r}finally{B=n,it.transition=e}}return!1}function bd(t,e,n){e=Tr(n,e),e=zm(t,e,1),t=dn(t,e,1),e=Oe(),t!==null&&(Ki(t,1,e),Ve(t,e))}function oe(t,e,n){if(t.tag===3)bd(t,t,n);else for(;e!==null;){if(e.tag===3){bd(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(hn===null||!hn.has(r))){t=Tr(n,t),t=Fm(e,t,1),e=dn(e,t,1),t=Oe(),e!==null&&(Ki(e,1,t),Ve(e,t));break}}e=e.return}}function Wx(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=Oe(),t.pingedLanes|=t.suspendedLanes&n,ve===t&&(be&n)===n&&(pe===4||pe===3&&(be&130023424)===be&&500>le()-Wc?In(t,0):Hc|=n),Ve(t,e)}function ip(t,e){e===0&&(t.mode&1?(e=cs,cs<<=1,!(cs&130023424)&&(cs=4194304)):e=1);var n=Oe();t=Wt(t,e),t!==null&&(Ki(t,e,n),Ve(t,n))}function Bx(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),ip(t,n)}function Vx(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(e),ip(t,n)}var sp;sp=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||We.current)Ie=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Ie=!1,Rx(t,e,n);Ie=!!(t.flags&131072)}else Ie=!1,ne&&e.flags&1048576&&cm(e,ho,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;Hs(t,e),t=e.pendingProps;var i=Cr(e,Me.current);br(e,n),i=$c(null,e,r,t,i,n);var s=zc();return e.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Be(r)?(s=!0,co(e)):s=!1,e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Tc(e),i.updater=Qo,e.stateNode=i,i._reactInternals=e,El(e,r,t,n),e=Ol(null,e,r,!0,s,n)):(e.tag=0,ne&&s&&kc(e),Re(null,e,i,n),e=e.child),e;case 16:r=e.elementType;e:{switch(Hs(t,e),t=e.pendingProps,i=r._init,r=i(r._payload),e.type=r,i=e.tag=Xx(r),t=dt(r,t),i){case 0:e=Rl(null,e,r,t,n);break e;case 1:e=ud(null,e,r,t,n);break e;case 11:e=ld(null,e,r,t,n);break e;case 14:e=cd(null,e,r,dt(r.type,t),n);break e}throw Error(C(306,r,""))}return e;case 0:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),Rl(t,e,r,i,n);case 1:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),ud(t,e,r,i,n);case 3:e:{if(Hm(e),t===null)throw Error(C(387));r=e.pendingProps,s=e.memoizedState,i=s.element,pm(t,e),po(e,r,null,n);var o=e.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){i=Tr(Error(C(423)),e),e=dd(t,e,r,n,i);break e}else if(r!==i){i=Tr(Error(C(424)),e),e=dd(t,e,r,n,i);break e}else for(Qe=un(e.stateNode.containerInfo.firstChild),Ke=e,ne=!0,ft=null,n=fm(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Mr(),r===i){e=Bt(t,e,n);break e}Re(t,e,r,n)}e=e.child}return e;case 5:return gm(e),t===null&&Cl(e),r=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,o=i.children,Sl(r,i)?o=null:s!==null&&Sl(r,s)&&(e.flags|=32),Um(t,e),Re(t,e,o,n),e.child;case 6:return t===null&&Cl(e),null;case 13:return Wm(t,e,n);case 4:return Rc(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Pr(e,null,r,n):Re(t,e,r,n),e.child;case 11:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),ld(t,e,r,i,n);case 7:return Re(t,e,e.pendingProps,n),e.child;case 8:return Re(t,e,e.pendingProps.children,n),e.child;case 12:return Re(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,i=e.pendingProps,s=e.memoizedProps,o=i.value,K(fo,r._currentValue),r._currentValue=o,s!==null)if(vt(s.value,o)){if(s.children===i.children&&!We.current){e=Bt(t,e,n);break e}}else for(s=e.child,s!==null&&(s.return=e);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=It(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ml(s.return,n,e),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===e.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(C(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ml(o,n,e),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===e){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}Re(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,r=e.pendingProps.children,br(e,n),i=ot(i),r=r(i),e.flags|=1,Re(t,e,r,n),e.child;case 14:return r=e.type,i=dt(r,e.pendingProps),i=dt(r.type,i),cd(t,e,r,i,n);case 15:return Am(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),Hs(t,e),e.tag=1,Be(r)?(t=!0,co(e)):t=!1,br(e,n),$m(e,r,i),El(e,r,i,n),Ol(null,e,r,!0,t,n);case 19:return Bm(t,e,n);case 22:return Im(t,e,n)}throw Error(C(156,e.tag))};function op(t,e){return Of(t,e)}function Yx(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(t,e,n,r){return new Yx(t,e,n,r)}function Xc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Xx(t){if(typeof t=="function")return Xc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===hc)return 11;if(t===fc)return 14}return 2}function mn(t,e){var n=t.alternate;return n===null?(n=nt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Vs(t,e,n,r,i,s){var o=2;if(r=t,typeof t=="function")Xc(t)&&(o=1);else if(typeof t=="string")o=5;else e:switch(t){case sr:return Un(n.children,i,s,e);case dc:o=8,i|=8;break;case el:return t=nt(12,n,e,i|2),t.elementType=el,t.lanes=s,t;case tl:return t=nt(13,n,e,i),t.elementType=tl,t.lanes=s,t;case nl:return t=nt(19,n,e,i),t.elementType=nl,t.lanes=s,t;case gf:return qo(n,i,s,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case mf:o=10;break e;case pf:o=9;break e;case hc:o=11;break e;case fc:o=14;break e;case Gt:o=16,r=null;break e}throw Error(C(130,t==null?t:typeof t,""))}return e=nt(o,n,e,i),e.elementType=t,e.type=r,e.lanes=s,e}function Un(t,e,n,r){return t=nt(7,t,r,e),t.lanes=n,t}function qo(t,e,n,r){return t=nt(22,t,r,e),t.elementType=gf,t.lanes=n,t.stateNode={isHidden:!1},t}function Da(t,e,n){return t=nt(6,t,null,e),t.lanes=n,t}function La(t,e,n){return e=nt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function Qx(t,e,n,r,i){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=pa(0),this.expirationTimes=pa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=pa(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Qc(t,e,n,r,i,s,o,a,l){return t=new Qx(t,e,n,a,l),e===1?(e=1,s===!0&&(e|=8)):e=0,s=nt(3,null,null,e),t.current=s,s.stateNode=t,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Tc(s),t}function Kx(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ir,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function ap(t){if(!t)return wn;t=t._reactInternals;e:{if(Zn(t)!==t||t.tag!==1)throw Error(C(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Be(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(C(171))}if(t.tag===1){var n=t.type;if(Be(n))return am(t,n,e)}return e}function lp(t,e,n,r,i,s,o,a,l){return t=Qc(n,r,!0,t,i,s,o,a,l),t.context=ap(null),n=t.current,r=Oe(),i=fn(n),s=It(r,i),s.callback=e??null,dn(n,s,i),t.current.lanes=i,Ki(t,i,r),Ve(t,r),t}function Zo(t,e,n,r){var i=e.current,s=Oe(),o=fn(i);return n=ap(n),e.context===null?e.context=n:e.pendingContext=n,e=It(s,o),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=dn(i,e,o),t!==null&&(xt(t,i,o,s),As(t,i,o)),o}function _o(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function wd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Kc(t,e){wd(t,e),(t=t.alternate)&&wd(t,e)}function Gx(){return null}var cp=typeof reportError=="function"?reportError:function(t){console.error(t)};function Gc(t){this._internalRoot=t}Jo.prototype.render=Gc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(C(409));Zo(t,e,null,null)};Jo.prototype.unmount=Gc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Xn(function(){Zo(null,t,null,null)}),e[Ht]=null}};function Jo(t){this._internalRoot=t}Jo.prototype.unstable_scheduleHydration=function(t){if(t){var e=If();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Zt.length&&e!==0&&e<Zt[n].priority;n++);Zt.splice(n,0,t),n===0&&Hf(t)}};function qc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function ea(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Sd(){}function qx(t,e,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=_o(o);s.call(u)}}var o=lp(e,r,t,0,null,!1,!1,"",Sd);return t._reactRootContainer=o,t[Ht]=o.current,Ti(t.nodeType===8?t.parentNode:t),Xn(),o}for(;i=t.lastChild;)t.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=_o(l);a.call(u)}}var l=Qc(t,0,!1,null,null,!1,!1,"",Sd);return t._reactRootContainer=l,t[Ht]=l.current,Ti(t.nodeType===8?t.parentNode:t),Xn(function(){Zo(e,l,n,r)}),l}function ta(t,e,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=_o(o);a.call(l)}}Zo(e,o,t,i)}else o=qx(n,e,t,i,r);return _o(o)}Ff=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=ii(e.pendingLanes);n!==0&&(gc(e,n|1),Ve(e,le()),!(F&6)&&(Rr=le()+500,Nn()))}break;case 13:Xn(function(){var r=Wt(t,1);if(r!==null){var i=Oe();xt(r,t,1,i)}}),Kc(t,1)}};xc=function(t){if(t.tag===13){var e=Wt(t,134217728);if(e!==null){var n=Oe();xt(e,t,134217728,n)}Kc(t,134217728)}};Af=function(t){if(t.tag===13){var e=fn(t),n=Wt(t,e);if(n!==null){var r=Oe();xt(n,t,e,r)}Kc(t,e)}};If=function(){return B};Uf=function(t,e){var n=B;try{return B=t,e()}finally{B=n}};hl=function(t,e,n){switch(e){case"input":if(sl(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var i=Vo(r);if(!i)throw Error(C(90));vf(r),sl(r,i)}}}break;case"textarea":bf(t,n);break;case"select":e=n.value,e!=null&&gr(t,!!n.multiple,e,!1)}};Cf=Bc;Mf=Xn;var Zx={usingClientEntryPoint:!1,Events:[qi,cr,Vo,Nf,jf,Bc]},Qr={findFiberByHostInstance:$n,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Jx={bundleType:Qr.bundleType,version:Qr.version,rendererPackageName:Qr.rendererPackageName,rendererConfig:Qr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Yt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Tf(t),t===null?null:t.stateNode},findFiberByHostInstance:Qr.findFiberByHostInstance||Gx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bs.isDisabled&&bs.supportsFiber)try{Uo=bs.inject(Jx),jt=bs}catch{}}qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zx;qe.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!qc(e))throw Error(C(200));return Kx(t,e,null,n)};qe.createRoot=function(t,e){if(!qc(t))throw Error(C(299));var n=!1,r="",i=cp;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(i=e.onRecoverableError)),e=Qc(t,1,!1,null,null,n,!1,r,i),t[Ht]=e.current,Ti(t.nodeType===8?t.parentNode:t),new Gc(e)};qe.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(C(188)):(t=Object.keys(t).join(","),Error(C(268,t)));return t=Tf(e),t=t===null?null:t.stateNode,t};qe.flushSync=function(t){return Xn(t)};qe.hydrate=function(t,e,n){if(!ea(e))throw Error(C(200));return ta(null,t,e,!0,n)};qe.hydrateRoot=function(t,e,n){if(!qc(t))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=cp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),e=lp(e,null,t,1,n??null,i,!1,s,o),t[Ht]=e.current,Ti(t),r)for(t=0;t<r.length;t++)n=r[t],i=n._getVersion,i=i(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,i]:e.mutableSourceEagerHydrationData.push(n,i);return new Jo(e)};qe.render=function(t,e,n){if(!ea(e))throw Error(C(200));return ta(null,t,e,!1,n)};qe.unmountComponentAtNode=function(t){if(!ea(t))throw Error(C(40));return t._reactRootContainer?(Xn(function(){ta(null,null,t,!1,function(){t._reactRootContainer=null,t[Ht]=null})}),!0):!1};qe.unstable_batchedUpdates=Bc;qe.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!ea(n))throw Error(C(200));if(t==null||t._reactInternals===void 0)throw Error(C(38));return ta(t,e,n,!1,r)};qe.version="18.3.1-next-f1338f8080-20240426";function up(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(up)}catch(t){console.error(t)}}up(),uf.exports=qe;var ev=uf.exports,_d=ev;Za.createRoot=_d.createRoot,Za.hydrateRoot=_d.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ii(){return Ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ii.apply(this,arguments)}var nn;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(nn||(nn={}));const kd="popstate";function tv(t){t===void 0&&(t={});function e(r,i){let{pathname:s,search:o,hash:a}=r.location;return Vl("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:ko(i)}return rv(e,n,null,t)}function ae(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function dp(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function nv(){return Math.random().toString(36).substr(2,8)}function Nd(t,e){return{usr:t.state,key:t.key,idx:e}}function Vl(t,e,n,r){return n===void 0&&(n=null),Ii({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Fr(e):e,{state:n,key:e&&e.key||r||nv()})}function ko(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Fr(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function rv(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a=nn.Pop,l=null,u=d();u==null&&(u=0,o.replaceState(Ii({},o.state,{idx:u}),""));function d(){return(o.state||{idx:null}).idx}function h(){a=nn.Pop;let b=d(),p=b==null?null:b-u;u=b,l&&l({action:a,location:g.location,delta:p})}function f(b,p){a=nn.Push;let x=Vl(g.location,b,p);u=d()+1;let y=Nd(x,u),w=g.createHref(x);try{o.pushState(y,"",w)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;i.location.assign(w)}s&&l&&l({action:a,location:g.location,delta:1})}function m(b,p){a=nn.Replace;let x=Vl(g.location,b,p);u=d();let y=Nd(x,u),w=g.createHref(x);o.replaceState(y,"",w),s&&l&&l({action:a,location:g.location,delta:0})}function v(b){let p=i.location.origin!=="null"?i.location.origin:i.location.href,x=typeof b=="string"?b:ko(b);return x=x.replace(/ $/,"%20"),ae(p,"No window.location.(origin|href) available to create URL for href: "+x),new URL(x,p)}let g={get action(){return a},get location(){return t(i,o)},listen(b){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(kd,h),l=b,()=>{i.removeEventListener(kd,h),l=null}},createHref(b){return e(i,b)},createURL:v,encodeLocation(b){let p=v(b);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:f,replace:m,go(b){return o.go(b)}};return g}var jd;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(jd||(jd={}));function iv(t,e,n){return n===void 0&&(n="/"),sv(t,e,n)}function sv(t,e,n,r){let i=typeof e=="string"?Fr(e):e,s=Or(i.pathname||"/",n);if(s==null)return null;let o=hp(t);ov(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=xv(s);a=pv(o[l],u)}return a}function hp(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(ae(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=pn([r,l.relativePath]),d=n.concat(l);s.children&&s.children.length>0&&(ae(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),hp(s.children,e,d,u)),!(s.path==null&&!s.index)&&e.push({path:u,score:fv(u,s.index),routesMeta:d})};return t.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of fp(s.path))i(s,o,l)}),e}function fp(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=fp(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>t.startsWith("/")&&l===""?"/":l)}function ov(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:mv(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const av=/^:[\w-]+$/,lv=3,cv=2,uv=1,dv=10,hv=-2,Cd=t=>t==="*";function fv(t,e){let n=t.split("/"),r=n.length;return n.some(Cd)&&(r+=hv),e&&(r+=cv),n.filter(i=>!Cd(i)).reduce((i,s)=>i+(av.test(s)?lv:s===""?uv:dv),r)}function mv(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function pv(t,e,n){let{routesMeta:r}=t,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,d=s==="/"?e:e.slice(s.length)||"/",h=Yl({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!h)return null;Object.assign(i,h.params),o.push({params:i,pathname:pn([s,h.pathname]),pathnameBase:wv(pn([s,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(s=pn([s,h.pathnameBase]))}return o}function Yl(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=gv(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:m}=d;if(f==="*"){let g=a[h]||"";o=s.slice(0,s.length-g.length).replace(/(.)\/+$/,"$1")}const v=a[h];return m&&!v?u[f]=void 0:u[f]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:t}}function gv(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),dp(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function xv(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return dp(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Or(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function vv(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?Fr(t):t;return{pathname:n?n.startsWith("/")?n:yv(n,e):e,search:Sv(r),hash:_v(i)}}function yv(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function $a(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function bv(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function mp(t,e){let n=bv(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function pp(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=Fr(t):(i=Ii({},t),ae(!i.pathname||!i.pathname.includes("?"),$a("?","pathname","search",i)),ae(!i.pathname||!i.pathname.includes("#"),$a("#","pathname","hash",i)),ae(!i.search||!i.search.includes("#"),$a("#","search","hash",i)));let s=t===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let h=e.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}a=h>=0?e[h]:"/"}let l=vv(i,a),u=o&&o!=="/"&&o.endsWith("/"),d=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const pn=t=>t.join("/").replace(/\/\/+/g,"/"),wv=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Sv=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,_v=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function kv(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const gp=["post","put","patch","delete"];new Set(gp);const Nv=["get",...gp];new Set(Nv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ui(){return Ui=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ui.apply(this,arguments)}const na=k.createContext(null),xp=k.createContext(null),jn=k.createContext(null),ra=k.createContext(null),Jn=k.createContext({outlet:null,matches:[],isDataRoute:!1}),vp=k.createContext(null);function jv(t,e){let{relative:n}=e===void 0?{}:e;Ji()||ae(!1);let{basename:r,navigator:i}=k.useContext(jn),{hash:s,pathname:o,search:a}=sa(t,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:pn([r,o])),i.createHref({pathname:l,search:a,hash:s})}function Ji(){return k.useContext(ra)!=null}function es(){return Ji()||ae(!1),k.useContext(ra).location}function yp(t){k.useContext(jn).static||k.useLayoutEffect(t)}function ia(){let{isDataRoute:t}=k.useContext(Jn);return t?Av():Cv()}function Cv(){Ji()||ae(!1);let t=k.useContext(na),{basename:e,future:n,navigator:r}=k.useContext(jn),{matches:i}=k.useContext(Jn),{pathname:s}=es(),o=JSON.stringify(mp(i,n.v7_relativeSplatPath)),a=k.useRef(!1);return yp(()=>{a.current=!0}),k.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let h=pp(u,JSON.parse(o),s,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:pn([e,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[e,r,o,s,t])}function sa(t,e){let{relative:n}=e===void 0?{}:e,{future:r}=k.useContext(jn),{matches:i}=k.useContext(Jn),{pathname:s}=es(),o=JSON.stringify(mp(i,r.v7_relativeSplatPath));return k.useMemo(()=>pp(t,JSON.parse(o),s,n==="path"),[t,o,s,n])}function Mv(t,e){return Pv(t,e)}function Pv(t,e,n,r){Ji()||ae(!1);let{navigator:i}=k.useContext(jn),{matches:s}=k.useContext(Jn),o=s[s.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=es(),d;if(e){var h;let b=typeof e=="string"?Fr(e):e;l==="/"||(h=b.pathname)!=null&&h.startsWith(l)||ae(!1),d=b}else d=u;let f=d.pathname||"/",m=f;if(l!=="/"){let b=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(b.length).join("/")}let v=iv(t,{pathname:m}),g=Dv(v&&v.map(b=>Object.assign({},b,{params:Object.assign({},a,b.params),pathname:pn([l,i.encodeLocation?i.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?l:pn([l,i.encodeLocation?i.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),s,n,r);return e&&g?k.createElement(ra.Provider,{value:{location:Ui({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:nn.Pop}},g):g}function Ev(){let t=Fv(),e=kv(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return k.createElement(k.Fragment,null,k.createElement("h2",null,"Unexpected Application Error!"),k.createElement("h3",{style:{fontStyle:"italic"}},e),n?k.createElement("pre",{style:i},n):null,null)}const Tv=k.createElement(Ev,null);class Rv extends k.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?k.createElement(Jn.Provider,{value:this.props.routeContext},k.createElement(vp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ov(t){let{routeContext:e,match:n,children:r}=t,i=k.useContext(na);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),k.createElement(Jn.Provider,{value:e},r)}function Dv(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var s;if(!n)return null;if(n.errors)t=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let o=t,a=(i=n)==null?void 0:i.errors;if(a!=null){let d=o.findIndex(h=>h.route.id&&(a==null?void 0:a[h.route.id])!==void 0);d>=0||ae(!1),o=o.slice(0,Math.min(o.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<o.length;d++){let h=o[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:m}=n,v=h.route.loader&&f[h.route.id]===void 0&&(!m||m[h.route.id]===void 0);if(h.route.lazy||v){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((d,h,f)=>{let m,v=!1,g=null,b=null;n&&(m=a&&h.route.id?a[h.route.id]:void 0,g=h.route.errorElement||Tv,l&&(u<0&&f===0?(Iv("route-fallback"),v=!0,b=null):u===f&&(v=!0,b=h.route.hydrateFallbackElement||null)));let p=e.concat(o.slice(0,f+1)),x=()=>{let y;return m?y=g:v?y=b:h.route.Component?y=k.createElement(h.route.Component,null):h.route.element?y=h.route.element:y=d,k.createElement(Ov,{match:h,routeContext:{outlet:d,matches:p,isDataRoute:n!=null},children:y})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?k.createElement(Rv,{location:n.location,revalidation:n.revalidation,component:g,error:m,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()},null)}var bp=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(bp||{}),wp=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(wp||{});function Lv(t){let e=k.useContext(na);return e||ae(!1),e}function $v(t){let e=k.useContext(xp);return e||ae(!1),e}function zv(t){let e=k.useContext(Jn);return e||ae(!1),e}function Sp(t){let e=zv(),n=e.matches[e.matches.length-1];return n.route.id||ae(!1),n.route.id}function Fv(){var t;let e=k.useContext(vp),n=$v(),r=Sp();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function Av(){let{router:t}=Lv(bp.UseNavigateStable),e=Sp(wp.UseNavigateStable),n=k.useRef(!1);return yp(()=>{n.current=!0}),k.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Ui({fromRouteId:e},s)))},[t,e])}const Md={};function Iv(t,e,n){Md[t]||(Md[t]=!0)}function Uv(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function On(t){ae(!1)}function Hv(t){let{basename:e="/",children:n=null,location:r,navigationType:i=nn.Pop,navigator:s,static:o=!1,future:a}=t;Ji()&&ae(!1);let l=e.replace(/^\/*/,"/"),u=k.useMemo(()=>({basename:l,navigator:s,static:o,future:Ui({v7_relativeSplatPath:!1},a)}),[l,a,s,o]);typeof r=="string"&&(r=Fr(r));let{pathname:d="/",search:h="",hash:f="",state:m=null,key:v="default"}=r,g=k.useMemo(()=>{let b=Or(d,l);return b==null?null:{location:{pathname:b,search:h,hash:f,state:m,key:v},navigationType:i}},[l,d,h,f,m,v,i]);return g==null?null:k.createElement(jn.Provider,{value:u},k.createElement(ra.Provider,{children:n,value:g}))}function Wv(t){let{children:e,location:n}=t;return Mv(Xl(e),n)}new Promise(()=>{});function Xl(t,e){e===void 0&&(e=[]);let n=[];return k.Children.forEach(t,(r,i)=>{if(!k.isValidElement(r))return;let s=[...e,i];if(r.type===k.Fragment){n.push.apply(n,Xl(r.props.children,s));return}r.type!==On&&ae(!1),!r.props.index||!r.props.children||ae(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Xl(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function No(){return No=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},No.apply(this,arguments)}function _p(t,e){if(t==null)return{};var n={},r=Object.keys(t),i,s;for(s=0;s<r.length;s++)i=r[s],!(e.indexOf(i)>=0)&&(n[i]=t[i]);return n}function Bv(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function Vv(t,e){return t.button===0&&(!e||e==="_self")&&!Bv(t)}const Yv=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Xv=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],Qv="6";try{window.__reactRouterVersion=Qv}catch{}const Kv=k.createContext({isTransitioning:!1}),Gv="startTransition",Pd=Wg[Gv];function qv(t){let{basename:e,children:n,future:r,window:i}=t,s=k.useRef();s.current==null&&(s.current=tv({window:i,v5Compat:!0}));let o=s.current,[a,l]=k.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},d=k.useCallback(h=>{u&&Pd?Pd(()=>l(h)):l(h)},[l,u]);return k.useLayoutEffect(()=>o.listen(d),[o,d]),k.useEffect(()=>Uv(r),[r]),k.createElement(Hv,{basename:e,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const Zv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Jv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ey=k.forwardRef(function(e,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:d,viewTransition:h}=e,f=_p(e,Yv),{basename:m}=k.useContext(jn),v,g=!1;if(typeof u=="string"&&Jv.test(u)&&(v=u,Zv))try{let y=new URL(window.location.href),w=u.startsWith("//")?new URL(y.protocol+u):new URL(u),S=Or(w.pathname,m);w.origin===y.origin&&S!=null?u=S+w.search+w.hash:g=!0}catch{}let b=jv(u,{relative:i}),p=ny(u,{replace:o,state:a,target:l,preventScrollReset:d,relative:i,viewTransition:h});function x(y){r&&r(y),y.defaultPrevented||p(y)}return k.createElement("a",No({},f,{href:v||b,onClick:g||s?r:x,ref:n,target:l}))}),Ed=k.forwardRef(function(e,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:o=!1,style:a,to:l,viewTransition:u,children:d}=e,h=_p(e,Xv),f=sa(l,{relative:h.relative}),m=es(),v=k.useContext(xp),{navigator:g,basename:b}=k.useContext(jn),p=v!=null&&ry(f)&&u===!0,x=g.encodeLocation?g.encodeLocation(f).pathname:f.pathname,y=m.pathname,w=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;i||(y=y.toLowerCase(),w=w?w.toLowerCase():null,x=x.toLowerCase()),w&&b&&(w=Or(w,b)||w);const S=x!=="/"&&x.endsWith("/")?x.length-1:x.length;let N=y===x||!o&&y.startsWith(x)&&y.charAt(S)==="/",_=w!=null&&(w===x||!o&&w.startsWith(x)&&w.charAt(x.length)==="/"),j={isActive:N,isPending:_,isTransitioning:p},E=N?r:void 0,M;typeof s=="function"?M=s(j):M=[s,N?"active":null,_?"pending":null,p?"transitioning":null].filter(Boolean).join(" ");let T=typeof a=="function"?a(j):a;return k.createElement(ey,No({},h,{"aria-current":E,className:M,ref:n,style:T,to:l,viewTransition:u}),typeof d=="function"?d(j):d)});var Ql;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Ql||(Ql={}));var Td;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Td||(Td={}));function ty(t){let e=k.useContext(na);return e||ae(!1),e}function ny(t,e){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:a}=e===void 0?{}:e,l=ia(),u=es(),d=sa(t,{relative:o});return k.useCallback(h=>{if(Vv(h,n)){h.preventDefault();let f=r!==void 0?r:ko(u)===ko(d);l(t,{replace:f,state:i,preventScrollReset:s,relative:o,viewTransition:a})}},[u,l,d,r,i,n,t,s,o,a])}function ry(t,e){e===void 0&&(e={});let n=k.useContext(Kv);n==null&&ae(!1);let{basename:r}=ty(Ql.useViewTransitionState),i=sa(t,{relative:e.relative});if(!n.isTransitioning)return!1;let s=Or(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=Or(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Yl(i.pathname,o)!=null||Yl(i.pathname,s)!=null}function jo(t){"@babel/helpers - typeof";return jo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jo(t)}function Qn(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function $e(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function Mt(t){$e(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||jo(t)==="object"&&e==="[object Date]"?new Date(t.getTime()):typeof t=="number"||e==="[object Number]"?new Date(t):((typeof t=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function iy(t,e){$e(2,arguments);var n=Mt(t).getTime(),r=Qn(e);return new Date(n+r)}var sy={};function oa(){return sy}function oy(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}function ay(t){return $e(1,arguments),t instanceof Date||jo(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function ly(t){if($e(1,arguments),!ay(t)&&typeof t!="number")return!1;var e=Mt(t);return!isNaN(Number(e))}function cy(t,e){$e(2,arguments);var n=Qn(e);return iy(t,-n)}var uy=864e5;function dy(t){$e(1,arguments);var e=Mt(t),n=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var r=e.getTime(),i=n-r;return Math.floor(i/uy)+1}function Co(t){$e(1,arguments);var e=1,n=Mt(t),r=n.getUTCDay(),i=(r<e?7:0)+r-e;return n.setUTCDate(n.getUTCDate()-i),n.setUTCHours(0,0,0,0),n}function kp(t){$e(1,arguments);var e=Mt(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var i=Co(r),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var o=Co(s);return e.getTime()>=i.getTime()?n+1:e.getTime()>=o.getTime()?n:n-1}function hy(t){$e(1,arguments);var e=kp(t),n=new Date(0);n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0);var r=Co(n);return r}var fy=6048e5;function my(t){$e(1,arguments);var e=Mt(t),n=Co(e).getTime()-hy(e).getTime();return Math.round(n/fy)+1}function Mo(t,e){var n,r,i,s,o,a,l,u;$e(1,arguments);var d=oa(),h=Qn((n=(r=(i=(s=e==null?void 0:e.weekStartsOn)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&n!==void 0?n:0);if(!(h>=0&&h<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var f=Mt(t),m=f.getUTCDay(),v=(m<h?7:0)+m-h;return f.setUTCDate(f.getUTCDate()-v),f.setUTCHours(0,0,0,0),f}function Np(t,e){var n,r,i,s,o,a,l,u;$e(1,arguments);var d=Mt(t),h=d.getUTCFullYear(),f=oa(),m=Qn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&i!==void 0?i:f.firstWeekContainsDate)!==null&&r!==void 0?r:(l=f.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(m>=1&&m<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var v=new Date(0);v.setUTCFullYear(h+1,0,m),v.setUTCHours(0,0,0,0);var g=Mo(v,e),b=new Date(0);b.setUTCFullYear(h,0,m),b.setUTCHours(0,0,0,0);var p=Mo(b,e);return d.getTime()>=g.getTime()?h+1:d.getTime()>=p.getTime()?h:h-1}function py(t,e){var n,r,i,s,o,a,l,u;$e(1,arguments);var d=oa(),h=Qn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(o=e.locale)===null||o===void 0||(a=o.options)===null||a===void 0?void 0:a.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1),f=Np(t,e),m=new Date(0);m.setUTCFullYear(f,0,h),m.setUTCHours(0,0,0,0);var v=Mo(m,e);return v}var gy=6048e5;function xy(t,e){$e(1,arguments);var n=Mt(t),r=Mo(n,e).getTime()-py(n,e).getTime();return Math.round(r/gy)+1}function H(t,e){for(var n=t<0?"-":"",r=Math.abs(t).toString();r.length<e;)r="0"+r;return n+r}var Qt={y:function(e,n){var r=e.getUTCFullYear(),i=r>0?r:1-r;return H(n==="yy"?i%100:i,n.length)},M:function(e,n){var r=e.getUTCMonth();return n==="M"?String(r+1):H(r+1,2)},d:function(e,n){return H(e.getUTCDate(),n.length)},a:function(e,n){var r=e.getUTCHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(e,n){return H(e.getUTCHours()%12||12,n.length)},H:function(e,n){return H(e.getUTCHours(),n.length)},m:function(e,n){return H(e.getUTCMinutes(),n.length)},s:function(e,n){return H(e.getUTCSeconds(),n.length)},S:function(e,n){var r=n.length,i=e.getUTCMilliseconds(),s=Math.floor(i*Math.pow(10,r-3));return H(s,n.length)}},tr={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},vy={G:function(e,n,r){var i=e.getUTCFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(e,n,r){if(n==="yo"){var i=e.getUTCFullYear(),s=i>0?i:1-i;return r.ordinalNumber(s,{unit:"year"})}return Qt.y(e,n)},Y:function(e,n,r,i){var s=Np(e,i),o=s>0?s:1-s;if(n==="YY"){var a=o%100;return H(a,2)}return n==="Yo"?r.ordinalNumber(o,{unit:"year"}):H(o,n.length)},R:function(e,n){var r=kp(e);return H(r,n.length)},u:function(e,n){var r=e.getUTCFullYear();return H(r,n.length)},Q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"Q":return String(i);case"QQ":return H(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"q":return String(i);case"qq":return H(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(e,n,r){var i=e.getUTCMonth();switch(n){case"M":case"MM":return Qt.M(e,n);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(e,n,r){var i=e.getUTCMonth();switch(n){case"L":return String(i+1);case"LL":return H(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(e,n,r,i){var s=xy(e,i);return n==="wo"?r.ordinalNumber(s,{unit:"week"}):H(s,n.length)},I:function(e,n,r){var i=my(e);return n==="Io"?r.ordinalNumber(i,{unit:"week"}):H(i,n.length)},d:function(e,n,r){return n==="do"?r.ordinalNumber(e.getUTCDate(),{unit:"date"}):Qt.d(e,n)},D:function(e,n,r){var i=dy(e);return n==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):H(i,n.length)},E:function(e,n,r){var i=e.getUTCDay();switch(n){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(e,n,r,i){var s=e.getUTCDay(),o=(s-i.weekStartsOn+8)%7||7;switch(n){case"e":return String(o);case"ee":return H(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(s,{width:"short",context:"formatting"});case"eeee":default:return r.day(s,{width:"wide",context:"formatting"})}},c:function(e,n,r,i){var s=e.getUTCDay(),o=(s-i.weekStartsOn+8)%7||7;switch(n){case"c":return String(o);case"cc":return H(o,n.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(s,{width:"narrow",context:"standalone"});case"cccccc":return r.day(s,{width:"short",context:"standalone"});case"cccc":default:return r.day(s,{width:"wide",context:"standalone"})}},i:function(e,n,r){var i=e.getUTCDay(),s=i===0?7:i;switch(n){case"i":return String(s);case"ii":return H(s,n.length);case"io":return r.ordinalNumber(s,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(e,n,r){var i=e.getUTCHours(),s=i/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,n,r){var i=e.getUTCHours(),s;switch(i===12?s=tr.noon:i===0?s=tr.midnight:s=i/12>=1?"pm":"am",n){case"b":case"bb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,n,r){var i=e.getUTCHours(),s;switch(i>=17?s=tr.evening:i>=12?s=tr.afternoon:i>=4?s=tr.morning:s=tr.night,n){case"B":case"BB":case"BBB":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,n,r){if(n==="ho"){var i=e.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return Qt.h(e,n)},H:function(e,n,r){return n==="Ho"?r.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Qt.H(e,n)},K:function(e,n,r){var i=e.getUTCHours()%12;return n==="Ko"?r.ordinalNumber(i,{unit:"hour"}):H(i,n.length)},k:function(e,n,r){var i=e.getUTCHours();return i===0&&(i=24),n==="ko"?r.ordinalNumber(i,{unit:"hour"}):H(i,n.length)},m:function(e,n,r){return n==="mo"?r.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):Qt.m(e,n)},s:function(e,n,r){return n==="so"?r.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):Qt.s(e,n)},S:function(e,n){return Qt.S(e,n)},X:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();if(o===0)return"Z";switch(n){case"X":return Od(o);case"XXXX":case"XX":return Dn(o);case"XXXXX":case"XXX":default:return Dn(o,":")}},x:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"x":return Od(o);case"xxxx":case"xx":return Dn(o);case"xxxxx":case"xxx":default:return Dn(o,":")}},O:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Rd(o,":");case"OOOO":default:return"GMT"+Dn(o,":")}},z:function(e,n,r,i){var s=i._originalDate||e,o=s.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Rd(o,":");case"zzzz":default:return"GMT"+Dn(o,":")}},t:function(e,n,r,i){var s=i._originalDate||e,o=Math.floor(s.getTime()/1e3);return H(o,n.length)},T:function(e,n,r,i){var s=i._originalDate||e,o=s.getTime();return H(o,n.length)}};function Rd(t,e){var n=t>0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=r%60;if(s===0)return n+String(i);var o=e;return n+String(i)+o+H(s,2)}function Od(t,e){if(t%60===0){var n=t>0?"-":"+";return n+H(Math.abs(t)/60,2)}return Dn(t,e)}function Dn(t,e){var n=e||"",r=t>0?"-":"+",i=Math.abs(t),s=H(Math.floor(i/60),2),o=H(i%60,2);return r+s+n+o}var Dd=function(e,n){switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},jp=function(e,n){switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},yy=function(e,n){var r=e.match(/(P+)(p+)?/)||[],i=r[1],s=r[2];if(!s)return Dd(e,n);var o;switch(i){case"P":o=n.dateTime({width:"short"});break;case"PP":o=n.dateTime({width:"medium"});break;case"PPP":o=n.dateTime({width:"long"});break;case"PPPP":default:o=n.dateTime({width:"full"});break}return o.replace("{{date}}",Dd(i,n)).replace("{{time}}",jp(s,n))},by={p:jp,P:yy},wy=["D","DD"],Sy=["YY","YYYY"];function _y(t){return wy.indexOf(t)!==-1}function ky(t){return Sy.indexOf(t)!==-1}function Ld(t,e,n){if(t==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Ny={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},jy=function(e,n,r){var i,s=Ny[e];return typeof s=="string"?i=s:n===1?i=s.one:i=s.other.replace("{{count}}",n.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};function za(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth,r=t.formats[n]||t.formats[t.defaultWidth];return r}}var Cy={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},My={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Py={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ey={date:za({formats:Cy,defaultWidth:"full"}),time:za({formats:My,defaultWidth:"full"}),dateTime:za({formats:Py,defaultWidth:"full"})},Ty={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Ry=function(e,n,r,i){return Ty[e]};function Kr(t){return function(e,n){var r=n!=null&&n.context?String(n.context):"standalone",i;if(r==="formatting"&&t.formattingValues){var s=t.defaultFormattingWidth||t.defaultWidth,o=n!=null&&n.width?String(n.width):s;i=t.formattingValues[o]||t.formattingValues[s]}else{var a=t.defaultWidth,l=n!=null&&n.width?String(n.width):t.defaultWidth;i=t.values[l]||t.values[a]}var u=t.argumentCallback?t.argumentCallback(e):e;return i[u]}}var Oy={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Dy={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Ly={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},$y={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},zy={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Fy={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Ay=function(e,n){var r=Number(e),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},Iy={ordinalNumber:Ay,era:Kr({values:Oy,defaultWidth:"wide"}),quarter:Kr({values:Dy,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:Kr({values:Ly,defaultWidth:"wide"}),day:Kr({values:$y,defaultWidth:"wide"}),dayPeriod:Kr({values:zy,defaultWidth:"wide",formattingValues:Fy,defaultFormattingWidth:"wide"})};function Gr(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(i);if(!s)return null;var o=s[0],a=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(a)?Hy(a,function(h){return h.test(o)}):Uy(a,function(h){return h.test(o)}),u;u=t.valueCallback?t.valueCallback(l):l,u=n.valueCallback?n.valueCallback(u):u;var d=e.slice(o.length);return{value:u,rest:d}}}function Uy(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}function Hy(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}function Wy(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=e.match(t.matchPattern);if(!r)return null;var i=r[0],s=e.match(t.parsePattern);if(!s)return null;var o=t.valueCallback?t.valueCallback(s[0]):s[0];o=n.valueCallback?n.valueCallback(o):o;var a=e.slice(i.length);return{value:o,rest:a}}}var By=/^(\d+)(th|st|nd|rd)?/i,Vy=/\d+/i,Yy={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Xy={any:[/^b/i,/^(a|c)/i]},Qy={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Ky={any:[/1/i,/2/i,/3/i,/4/i]},Gy={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},qy={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Zy={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Jy={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},e1={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},t1={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},n1={ordinalNumber:Wy({matchPattern:By,parsePattern:Vy,valueCallback:function(e){return parseInt(e,10)}}),era:Gr({matchPatterns:Yy,defaultMatchWidth:"wide",parsePatterns:Xy,defaultParseWidth:"any"}),quarter:Gr({matchPatterns:Qy,defaultMatchWidth:"wide",parsePatterns:Ky,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:Gr({matchPatterns:Gy,defaultMatchWidth:"wide",parsePatterns:qy,defaultParseWidth:"any"}),day:Gr({matchPatterns:Zy,defaultMatchWidth:"wide",parsePatterns:Jy,defaultParseWidth:"any"}),dayPeriod:Gr({matchPatterns:e1,defaultMatchWidth:"any",parsePatterns:t1,defaultParseWidth:"any"})},r1={code:"en-US",formatDistance:jy,formatLong:Ey,formatRelative:Ry,localize:Iy,match:n1,options:{weekStartsOn:0,firstWeekContainsDate:1}},i1=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,s1=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,o1=/^'([^]*?)'?$/,a1=/''/g,l1=/[a-zA-Z]/;function $d(t,e,n){var r,i,s,o,a,l,u,d,h,f,m,v,g,b;$e(2,arguments);var p=String(e),x=oa(),y=(r=(i=void 0)!==null&&i!==void 0?i:x.locale)!==null&&r!==void 0?r:r1,w=Qn((s=(o=(a=(l=void 0)!==null&&l!==void 0?l:void 0)!==null&&a!==void 0?a:x.firstWeekContainsDate)!==null&&o!==void 0?o:(u=x.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&s!==void 0?s:1);if(!(w>=1&&w<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var S=Qn((h=(f=(m=(v=void 0)!==null&&v!==void 0?v:void 0)!==null&&m!==void 0?m:x.weekStartsOn)!==null&&f!==void 0?f:(g=x.locale)===null||g===void 0||(b=g.options)===null||b===void 0?void 0:b.weekStartsOn)!==null&&h!==void 0?h:0);if(!(S>=0&&S<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!y.localize)throw new RangeError("locale must contain localize property");if(!y.formatLong)throw new RangeError("locale must contain formatLong property");var N=Mt(t);if(!ly(N))throw new RangeError("Invalid time value");var _=oy(N),j=cy(N,_),E={firstWeekContainsDate:w,weekStartsOn:S,locale:y,_originalDate:N},M=p.match(s1).map(function(T){var D=T[0];if(D==="p"||D==="P"){var V=by[D];return V(T,y.formatLong)}return T}).join("").match(i1).map(function(T){if(T==="''")return"'";var D=T[0];if(D==="'")return c1(T);var V=vy[D];if(V)return ky(T)&&Ld(T,e,String(t)),_y(T)&&Ld(T,e,String(t)),V(j,T,y.localize,E);if(D.match(l1))throw new RangeError("Format string contains an unescaped latin alphabet character `"+D+"`");return T}).join("");return M}function c1(t){var e=t.match(o1);return e?e[1].replace(a1,"'"):t}const Cp=k.createContext(),Kl={version:"1.1.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null};function u1(t,e){switch(e.type){case"INITIALIZE_APP":return{...t,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:t.unitCost,timestamp:$d(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,purchases:[n,...t.purchases],currentUnits:t.currentUnits+e.payload.units};case"UPDATE_USAGE":const r={id:Date.now(),date:new Date().toISOString(),previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usage:t.currentUnits-e.payload.currentUnits,timestamp:$d(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[r,...t.usageHistory]};case"UPDATE_SETTINGS":return{...t,...e.payload};case"FACTORY_RESET":return{...Kl,unitCost:t.unitCost};case"DASHBOARD_RESET":return{...t,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...t,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...t,...e.payload};default:return t}}function d1({children:t}){const[e,n]=k.useReducer(u1,Kl);k.useEffect(()=>{const u="prepaid-meter-app-v1.1",d=localStorage.getItem(u);if(localStorage.removeItem("prepaid-meter-app"),d)try{const h=JSON.parse(d);if(!h.version||h.version!==Kl.version){console.log("Version mismatch detected, clearing old data"),localStorage.removeItem(u);return}n({type:"LOAD_STATE",payload:h})}catch(h){console.error("Error loading saved state:",h),localStorage.removeItem(u)}},[]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-app-v1.1",JSON.stringify(e))},[e]),k.useEffect(()=>{const u=new Date,d=u.getMonth(),h=u.getFullYear();if(e.lastMonthlyReset){const f=new Date(e.lastMonthlyReset),m=f.getMonth(),v=f.getFullYear();(h>v||h===v&&d>m)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:u.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]);const r=e.previousUnits-e.currentUnits,i=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,s=e.purchases.reduce((u,d)=>u+d.currency,0),o=e.usageHistory.reduce((u,d)=>u+d.usage,0),l={state:e,dispatch:n,initializeApp:u=>{n({type:"INITIALIZE_APP",payload:{initialUnits:u}})},addPurchase:(u,d)=>{n({type:"ADD_PURCHASE",payload:{currency:u,units:d}})},updateUsage:u=>{n({type:"UPDATE_USAGE",payload:{currentUnits:u}})},updateSettings:u=>{n({type:"UPDATE_SETTINGS",payload:u})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:r,isThresholdExceeded:i,totalPurchases:s,totalUnitsUsed:o,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName};return c.jsx(Cp.Provider,{value:l,children:t})}function Ye(){const t=k.useContext(Cp);if(!t)throw new Error("useApp must be used within an AppProvider");return t}const Mp=k.createContext(),mt={electric:{name:"Electric Blue",primary:"bg-blue-600",secondary:"bg-blue-100",accent:"bg-yellow-400",background:"bg-gray-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-gray-200",card:"bg-white",gradient:"from-blue-500 to-blue-700"},dark:{name:"Dark Mode",primary:"bg-gray-800",secondary:"bg-gray-700",accent:"bg-blue-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-700 to-gray-900"},green:{name:"Eco Green",primary:"bg-green-600",secondary:"bg-green-100",accent:"bg-lime-400",background:"bg-green-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-green-200",card:"bg-white",gradient:"from-green-500 to-green-700"},purple:{name:"Royal Purple",primary:"bg-purple-600",secondary:"bg-purple-100",accent:"bg-pink-400",background:"bg-purple-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-purple-200",card:"bg-white",gradient:"from-purple-500 to-purple-700"},orange:{name:"Sunset Orange",primary:"bg-orange-600",secondary:"bg-orange-100",accent:"bg-yellow-400",background:"bg-orange-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-orange-200",card:"bg-white",gradient:"from-orange-500 to-red-600"},teal:{name:"Ocean Teal",primary:"bg-teal-600",secondary:"bg-teal-100",accent:"bg-cyan-400",background:"bg-teal-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-teal-200",card:"bg-white",gradient:"from-teal-500 to-blue-600"},red:{name:"Fire Red",primary:"bg-red-600",secondary:"bg-red-100",accent:"bg-orange-400",background:"bg-red-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-red-200",card:"bg-white",gradient:"from-red-500 to-red-700"},indigo:{name:"Deep Indigo",primary:"bg-indigo-600",secondary:"bg-indigo-100",accent:"bg-purple-400",background:"bg-indigo-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-indigo-200",card:"bg-white",gradient:"from-indigo-500 to-purple-600"},pink:{name:"Rose Pink",primary:"bg-pink-600",secondary:"bg-pink-100",accent:"bg-rose-400",background:"bg-pink-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-pink-200",card:"bg-white",gradient:"from-pink-500 to-rose-600"},slate:{name:"Modern Slate",primary:"bg-slate-600",secondary:"bg-slate-100",accent:"bg-blue-400",background:"bg-slate-50",text:"text-gray-900",textSecondary:"text-gray-600",border:"border-slate-200",card:"bg-white",gradient:"from-slate-500 to-slate-700"}},Lt={inter:{name:"Inter",class:"font-inter",import:'@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");',fallback:"Inter, system-ui, -apple-system, sans-serif"},roboto:{name:"Roboto",class:"font-roboto",import:'@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");',fallback:"Roboto, system-ui, sans-serif"},opensans:{name:"Open Sans",class:"font-opensans",import:'@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Open Sans, system-ui, sans-serif"},lato:{name:"Lato",class:"font-lato",import:'@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap");',fallback:"Lato, system-ui, sans-serif"},poppins:{name:"Poppins",class:"font-poppins",import:'@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");',fallback:"Poppins, system-ui, sans-serif"},nunito:{name:"Nunito",class:"font-nunito",import:'@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700&display=swap");',fallback:"Nunito, system-ui, sans-serif"},sourcesans:{name:"Source Sans Pro",class:"font-sourcesans",import:'@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap");',fallback:"Source Sans Pro, system-ui, sans-serif"},ubuntu:{name:"Ubuntu",class:"font-ubuntu",import:'@import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap");',fallback:"Ubuntu, system-ui, sans-serif"},raleway:{name:"Raleway",class:"font-raleway",import:'@import url("https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap");',fallback:"Raleway, system-ui, sans-serif"},montserrat:{name:"Montserrat",class:"font-montserrat",import:'@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap");',fallback:"Montserrat, system-ui, sans-serif"},worksans:{name:"Work Sans",class:"font-worksans",import:'@import url("https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Work Sans, system-ui, sans-serif"},firasans:{name:"Fira Sans",class:"font-firasans",import:'@import url("https://fonts.googleapis.com/css2?family=Fira+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Fira Sans, system-ui, sans-serif"},dmsans:{name:"DM Sans",class:"font-dmsans",import:'@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");',fallback:"DM Sans, system-ui, sans-serif"},lexend:{name:"Lexend",class:"font-lexend",import:'@import url("https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap");',fallback:"Lexend, system-ui, sans-serif"},karla:{name:"Karla",class:"font-karla",import:'@import url("https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700&display=swap");',fallback:"Karla, system-ui, sans-serif"},rubik:{name:"Rubik",class:"font-rubik",import:'@import url("https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&display=swap");',fallback:"Rubik, system-ui, sans-serif"},manrope:{name:"Manrope",class:"font-manrope",import:'@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap");',fallback:"Manrope, system-ui, sans-serif"},plusjakarta:{name:"Plus Jakarta Sans",class:"font-plusjakarta",import:'@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap");',fallback:"Plus Jakarta Sans, system-ui, sans-serif"},outfit:{name:"Outfit",class:"font-outfit",import:'@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap");',fallback:"Outfit, system-ui, sans-serif"},system:{name:"System Default",class:"font-system",import:"",fallback:"system-ui, -apple-system, BlinkMacSystemFont, sans-serif"}},rn={xs:{name:"Extra Small",class:"text-xs",size:"12px"},sm:{name:"Small",class:"text-sm",size:"14px"},base:{name:"Medium",class:"text-base",size:"16px"},lg:{name:"Large",class:"text-lg",size:"18px"},xl:{name:"Extra Large",class:"text-xl",size:"20px"},"2xl":{name:"XXL",class:"text-2xl",size:"24px"}};function h1({children:t}){const[e,n]=k.useState("electric"),[r,i]=k.useState("base"),[s,o]=k.useState("inter"),[a,l]=k.useState({});k.useEffect(()=>{const d=localStorage.getItem("prepaid-meter-theme"),h=localStorage.getItem("prepaid-meter-font-size"),f=localStorage.getItem("prepaid-meter-font-family"),m=localStorage.getItem("prepaid-meter-custom-colors");if(d&&mt[d]&&n(d),h&&rn[h]&&i(h),f&&Lt[f]&&o(f),m)try{l(JSON.parse(m))}catch(v){console.error("Error loading custom colors:",v)}},[]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-theme",e)},[e]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-font-size",r)},[r]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-font-family",s)},[s]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-custom-colors",JSON.stringify(a))},[a]),k.useEffect(()=>{const d=Lt[s];if(d&&d.import){document.querySelectorAll("link[data-font-import]").forEach(m=>m.remove());const f=document.createElement("link");f.rel="stylesheet",f.href=d.import.replace('@import url("',"").replace('");',""),f.setAttribute("data-font-import","true"),document.head.appendChild(f)}},[s]);const u={currentTheme:e,setCurrentTheme:n,fontSize:r,setFontSize:i,fontFamily:s,setFontFamily:o,customColors:a,setCustomColors:l,theme:mt[e],themes:mt,fontSizes:rn,fontFamilies:Lt,currentFontSize:rn[r],currentFontFamily:Lt[s]};return c.jsx(Mp.Provider,{value:u,children:c.jsx("div",{className:`${mt[e].background} ${mt[e].text} ${Lt[s].class} ${rn[r].class} min-h-screen transition-all duration-300`,style:{fontFamily:Lt[s].fallback},children:t})})}function Pe(){const t=k.useContext(Mp);if(!t)throw new Error("useTheme must be used within a ThemeProvider");return t}var Pp={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},zd=pt.createContext&&pt.createContext(Pp),gn=function(){return gn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},gn.apply(this,arguments)},f1=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n};function Ep(t){return t&&t.map(function(e,n){return pt.createElement(e.tag,gn({key:n},e.attr),Ep(e.child))})}function te(t){return function(e){return pt.createElement(m1,gn({attr:gn({},t.attr)},e),Ep(t.child))}}function m1(t){var e=function(n){var r=t.attr,i=t.size,s=t.title,o=f1(t,["attr","size","title"]),a=i||n.size||"1em",l;return n.className&&(l=n.className),t.className&&(l=(l?l+" ":"")+t.className),pt.createElement("svg",gn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,o,{className:l,style:gn(gn({color:t.color||n.color},n.style),t.style),height:a,width:a,xmlns:"http://www.w3.org/2000/svg"}),s&&pt.createElement("title",null,s),t.children)};return zd!==void 0?pt.createElement(zd.Consumer,null,function(n){return e(n)}):e(Pp)}function p1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function g1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Tp(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z",clipRule:"evenodd"}}]})(t)}function Sr(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(t)}function Zc(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(t)}function Fa(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function Aa(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function Ys(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",clipRule:"evenodd"}}]})(t)}function Fd(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(t)}function Xs(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(t)}function Rp(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z",clipRule:"evenodd"}}]})(t)}function st(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(t)}function Hi(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function x1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(t)}function v1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z",clipRule:"evenodd"}}]})(t)}function y1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"}}]})(t)}function Ue(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(t)}function b1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(t)}function Op(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(t)}function w1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"}}]})(t)}function S1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function _1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(t)}function xn(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(t)}function k1(t){return te({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function N1(t){return te({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"}}]})(t)}function Po({size:t="md",animated:e=!0}){const n={sm:"h-6 w-6",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"};return c.jsx("div",{className:`${e?"lightning-animation":""} flex items-center justify-center`,children:c.jsx(Ue,{className:`${n[t]} text-yellow-400 drop-shadow-lg`})})}function j1({onMenuClick:t}){const{theme:e}=Pe(),{state:n}=Ye();return c.jsxs("header",{className:`${e.card} ${e.border} border-b px-4 py-3 flex items-center justify-between shadow-sm`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("button",{onClick:t,className:`lg:hidden p-2 rounded-md ${e.primary} text-white hover:opacity-80 transition-opacity`,children:c.jsx(b1,{className:"h-6 w-6"})}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Po,{size:"sm"}),c.jsxs("div",{children:[c.jsx("h1",{className:`text-xl font-bold ${e.text}`,children:"Prepaid Meter App"}),c.jsx("p",{className:`text-sm ${e.textSecondary}`,children:"Electricity Usage Tracker"})]})]})]}),c.jsxs("div",{className:`hidden sm:flex items-center space-x-4 ${e.card} rounded-lg px-4 py-2 border ${e.border}`,children:[c.jsxs("div",{className:"text-right",children:[c.jsx("p",{className:`text-sm ${e.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-lg font-bold ${e.text}`,children:n.currentUnits.toFixed(2)})]}),c.jsx("div",{className:`w-3 h-3 rounded-full ${n.currentUnits>n.thresholdLimit?"bg-red-500":"bg-green-500"} pulse-glow`})]})]})}const Ad=[{name:"Dashboard",href:"/dashboard",icon:y1},{name:"Purchases",href:"/purchases",icon:w1},{name:"Usage",href:"/usage",icon:Zc},{name:"History",href:"/history",icon:Ys},{name:"Settings",href:"/settings",icon:Xs}];function C1({isOpen:t,onClose:e}){const{theme:n}=Pe();return c.jsxs(c.Fragment,{children:[c.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${n.card} ${n.border} border-r`,children:c.jsxs("div",{className:"flex flex-col w-full",children:[c.jsxs("div",{className:`flex items-center justify-center h-16 px-4 ${n.primary} text-white`,children:[c.jsx(Po,{size:"md"}),c.jsx("span",{className:"ml-3 text-lg font-semibold",children:"Prepaid Meter"})]}),c.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2",children:Ad.map(r=>c.jsxs(Ed,{to:r.href,className:({isActive:i})=>`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${i?`${n.primary} text-white`:`${n.text} hover:${n.secondary}`}`,children:[c.jsx(r.icon,{className:"mr-3 h-5 w-5"}),r.name]},r.name))})]})}),c.jsx("div",{className:`lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${n.card} transform transition-transform duration-300 ease-in-out ${t?"translate-x-0":"-translate-x-full"}`,children:c.jsxs("div",{className:"flex flex-col h-full",children:[c.jsxs("div",{className:`flex items-center justify-between h-16 px-4 ${n.primary} text-white`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Po,{size:"md"}),c.jsx("span",{className:"ml-3 text-lg font-semibold",children:"Prepaid Meter"})]}),c.jsx("button",{onClick:e,className:"p-2 rounded-md hover:bg-white hover:bg-opacity-20 transition-colors",children:c.jsx(k1,{className:"h-6 w-6"})})]}),c.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2",children:Ad.map(r=>c.jsxs(Ed,{to:r.href,onClick:e,className:({isActive:i})=>`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${i?`${n.primary} text-white`:`${n.text} hover:${n.secondary}`}`,children:[c.jsx(r.icon,{className:"mr-3 h-5 w-5"}),r.name]},r.name))})]})})]})}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function ts(t){return t+.5|0}const sn=(t,e,n)=>Math.max(Math.min(t,n),e);function oi(t){return sn(ts(t*2.55),0,255)}function vn(t){return sn(ts(t*255),0,255)}function $t(t){return sn(ts(t/2.55)/100,0,1)}function Id(t){return sn(ts(t*100),0,100)}const Je={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Gl=[..."0123456789ABCDEF"],M1=t=>Gl[t&15],P1=t=>Gl[(t&240)>>4]+Gl[t&15],ws=t=>(t&240)>>4===(t&15),E1=t=>ws(t.r)&&ws(t.g)&&ws(t.b)&&ws(t.a);function T1(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&Je[t[1]]*17,g:255&Je[t[2]]*17,b:255&Je[t[3]]*17,a:e===5?Je[t[4]]*17:255}:(e===7||e===9)&&(n={r:Je[t[1]]<<4|Je[t[2]],g:Je[t[3]]<<4|Je[t[4]],b:Je[t[5]]<<4|Je[t[6]],a:e===9?Je[t[7]]<<4|Je[t[8]]:255})),n}const R1=(t,e)=>t<255?e(t):"";function O1(t){var e=E1(t)?M1:P1;return t?"#"+e(t.r)+e(t.g)+e(t.b)+R1(t.a,e):void 0}const D1=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Dp(t,e,n){const r=e*Math.min(n,1-n),i=(s,o=(s+t/30)%12)=>n-r*Math.max(Math.min(o-3,9-o,1),-1);return[i(0),i(8),i(4)]}function L1(t,e,n){const r=(i,s=(i+t/60)%6)=>n-n*e*Math.max(Math.min(s,4-s,1),0);return[r(5),r(3),r(1)]}function $1(t,e,n){const r=Dp(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)r[i]*=1-e-n,r[i]+=e;return r}function z1(t,e,n,r,i){return t===i?(e-n)/r+(e<n?6:0):e===i?(n-t)/r+2:(t-e)/r+4}function Jc(t){const n=t.r/255,r=t.g/255,i=t.b/255,s=Math.max(n,r,i),o=Math.min(n,r,i),a=(s+o)/2;let l,u,d;return s!==o&&(d=s-o,u=a>.5?d/(2-s-o):d/(s+o),l=z1(n,r,i,d,s),l=l*60+.5),[l|0,u||0,a]}function eu(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(vn)}function tu(t,e,n){return eu(Dp,t,e,n)}function F1(t,e,n){return eu($1,t,e,n)}function A1(t,e,n){return eu(L1,t,e,n)}function Lp(t){return(t%360+360)%360}function I1(t){const e=D1.exec(t);let n=255,r;if(!e)return;e[5]!==r&&(n=e[6]?oi(+e[5]):vn(+e[5]));const i=Lp(+e[2]),s=+e[3]/100,o=+e[4]/100;return e[1]==="hwb"?r=F1(i,s,o):e[1]==="hsv"?r=A1(i,s,o):r=tu(i,s,o),{r:r[0],g:r[1],b:r[2],a:n}}function U1(t,e){var n=Jc(t);n[0]=Lp(n[0]+e),n=tu(n),t.r=n[0],t.g=n[1],t.b=n[2]}function H1(t){if(!t)return;const e=Jc(t),n=e[0],r=Id(e[1]),i=Id(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${i}%, ${$t(t.a)})`:`hsl(${n}, ${r}%, ${i}%)`}const Ud={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Hd={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function W1(){const t={},e=Object.keys(Hd),n=Object.keys(Ud);let r,i,s,o,a;for(r=0;r<e.length;r++){for(o=a=e[r],i=0;i<n.length;i++)s=n[i],a=a.replace(s,Ud[s]);s=parseInt(Hd[o],16),t[a]=[s>>16&255,s>>8&255,s&255]}return t}let Ss;function B1(t){Ss||(Ss=W1(),Ss.transparent=[0,0,0,0]);const e=Ss[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const V1=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Y1(t){const e=V1.exec(t);let n=255,r,i,s;if(e){if(e[7]!==r){const o=+e[7];n=e[8]?oi(o):sn(o*255,0,255)}return r=+e[1],i=+e[3],s=+e[5],r=255&(e[2]?oi(r):sn(r,0,255)),i=255&(e[4]?oi(i):sn(i,0,255)),s=255&(e[6]?oi(s):sn(s,0,255)),{r,g:i,b:s,a:n}}}function X1(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${$t(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Ia=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,nr=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function Q1(t,e,n){const r=nr($t(t.r)),i=nr($t(t.g)),s=nr($t(t.b));return{r:vn(Ia(r+n*(nr($t(e.r))-r))),g:vn(Ia(i+n*(nr($t(e.g))-i))),b:vn(Ia(s+n*(nr($t(e.b))-s))),a:t.a+n*(e.a-t.a)}}function _s(t,e,n){if(t){let r=Jc(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,e===0?360:1)),r=tu(r),t.r=r[0],t.g=r[1],t.b=r[2]}}function $p(t,e){return t&&Object.assign(e||{},t)}function Wd(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=vn(t[3]))):(e=$p(t,{r:0,g:0,b:0,a:1}),e.a=vn(e.a)),e}function K1(t){return t.charAt(0)==="r"?Y1(t):I1(t)}class Wi{constructor(e){if(e instanceof Wi)return e;const n=typeof e;let r;n==="object"?r=Wd(e):n==="string"&&(r=T1(e)||B1(e)||K1(e)),this._rgb=r,this._valid=!!r}get valid(){return this._valid}get rgb(){var e=$p(this._rgb);return e&&(e.a=$t(e.a)),e}set rgb(e){this._rgb=Wd(e)}rgbString(){return this._valid?X1(this._rgb):void 0}hexString(){return this._valid?O1(this._rgb):void 0}hslString(){return this._valid?H1(this._rgb):void 0}mix(e,n){if(e){const r=this.rgb,i=e.rgb;let s;const o=n===s?.5:n,a=2*o-1,l=r.a-i.a,u=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;s=1-u,r.r=255&u*r.r+s*i.r+.5,r.g=255&u*r.g+s*i.g+.5,r.b=255&u*r.b+s*i.b+.5,r.a=o*r.a+(1-o)*i.a,this.rgb=r}return this}interpolate(e,n){return e&&(this._rgb=Q1(this._rgb,e._rgb,n)),this}clone(){return new Wi(this.rgb)}alpha(e){return this._rgb.a=vn(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=ts(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return _s(this._rgb,2,e),this}darken(e){return _s(this._rgb,2,-e),this}saturate(e){return _s(this._rgb,1,e),this}desaturate(e){return _s(this._rgb,1,-e),this}rotate(e){return U1(this._rgb,e),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Et(){}const G1=(()=>{let t=0;return()=>t++})();function X(t){return t==null}function me(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function A(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function lt(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function wt(t,e){return lt(t)?t:e}function I(t,e){return typeof t>"u"?e:t}const q1=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,zp=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function J(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function W(t,e,n,r){let i,s,o;if(me(t))for(s=t.length,i=0;i<s;i++)e.call(n,t[i],i);else if(A(t))for(o=Object.keys(t),s=o.length,i=0;i<s;i++)e.call(n,t[o[i]],o[i])}function Eo(t,e){let n,r,i,s;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(i=t[n],s=e[n],i.datasetIndex!==s.datasetIndex||i.index!==s.index)return!1;return!0}function To(t){if(me(t))return t.map(To);if(A(t)){const e=Object.create(null),n=Object.keys(t),r=n.length;let i=0;for(;i<r;++i)e[n[i]]=To(t[n[i]]);return e}return t}function Fp(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function Z1(t,e,n,r){if(!Fp(t))return;const i=e[t],s=n[t];A(i)&&A(s)?Bi(i,s,r):e[t]=To(s)}function Bi(t,e,n){const r=me(e)?e:[e],i=r.length;if(!A(t))return t;n=n||{};const s=n.merger||Z1;let o;for(let a=0;a<i;++a){if(o=r[a],!A(o))continue;const l=Object.keys(o);for(let u=0,d=l.length;u<d;++u)s(l[u],t,o,n)}return t}function bi(t,e){return Bi(t,e,{merger:J1})}function J1(t,e,n){if(!Fp(t))return;const r=e[t],i=n[t];A(r)&&A(i)?bi(r,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=To(i))}const Bd={"":t=>t,x:t=>t.x,y:t=>t.y};function eb(t){const e=t.split("."),n=[];let r="";for(const i of e)r+=i,r.endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}function tb(t){const e=eb(t);return n=>{for(const r of e){if(r==="")break;n=n&&n[r]}return n}}function Kn(t,e){return(Bd[e]||(Bd[e]=tb(e)))(t)}function nu(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Vi=t=>typeof t<"u",Sn=t=>typeof t=="function",Vd=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function nb(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const he=Math.PI,de=2*he,Ro=Number.POSITIVE_INFINITY,rb=he/180,ge=he/2,Cn=he/4,Yd=he*2/3,Ap=Math.log10,yn=Math.sign;function Qs(t,e,n){return Math.abs(t-e)<n}function Xd(t){const e=Math.round(t);t=Qs(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(Ap(t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function ib(t){const e=[],n=Math.sqrt(t);let r;for(r=1;r<n;r++)t%r===0&&(e.push(r),e.push(t/r));return n===(n|0)&&e.push(n),e.sort((i,s)=>i-s).pop(),e}function sb(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Oo(t){return!sb(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function ob(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function ab(t,e,n){let r,i,s;for(r=0,i=t.length;r<i;r++)s=t[r][n],isNaN(s)||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function At(t){return t*(he/180)}function lb(t){return t*(180/he)}function Qd(t){if(!lt(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Ip(t,e){const n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r);let s=Math.atan2(r,n);return s<-.5*he&&(s+=de),{angle:s,distance:i}}function cb(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Mn(t){return(t%de+de)%de}function Do(t,e,n,r){const i=Mn(t),s=Mn(e),o=Mn(n),a=Mn(s-i),l=Mn(o-i),u=Mn(i-s),d=Mn(i-o);return i===s||i===o||r&&s===o||a>l&&u<d}function He(t,e,n){return Math.max(e,Math.min(n,t))}function ub(t){return He(t,-32768,32767)}function An(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function ru(t,e,n){n=n||(o=>t[o]<e);let r=t.length-1,i=0,s;for(;r-i>1;)s=i+r>>1,n(s)?i=s:r=s;return{lo:i,hi:r}}const ql=(t,e,n,r)=>ru(t,n,r?i=>{const s=t[i][e];return s<n||s===n&&t[i+1][e]===n}:i=>t[i][e]<n),db=(t,e,n)=>ru(t,n,r=>t[r][e]>=n);function hb(t,e,n){let r=0,i=t.length;for(;r<i&&t[r]<e;)r++;for(;i>r&&t[i-1]>n;)i--;return r>0||i<t.length?t.slice(r,i):t}const Up=["push","pop","shift","splice","unshift"];function fb(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),Up.forEach(n=>{const r="_onData"+nu(n),i=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...s){const o=i.apply(this,s);return t._chartjs.listeners.forEach(a=>{typeof a[r]=="function"&&a[r](...s)}),o}})})}function Kd(t,e){const n=t._chartjs;if(!n)return;const r=n.listeners,i=r.indexOf(e);i!==-1&&r.splice(i,1),!(r.length>0)&&(Up.forEach(s=>{delete t[s]}),delete t._chartjs)}function Hp(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Wp=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function Bp(t,e){let n=[],r=!1;return function(...i){n=i,r||(r=!0,Wp.call(window,()=>{r=!1,t.apply(e,n)}))}}function mb(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}const iu=t=>t==="start"?"left":t==="end"?"right":"center",Ne=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,pb=(t,e,n,r)=>t===(r?"left":"right")?n:t==="center"?(e+n)/2:e,ks=t=>t===0||t===1,Gd=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*de/n)),qd=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*de/n)+1,wi={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*ge)+1,easeOutSine:t=>Math.sin(t*ge),easeInOutSine:t=>-.5*(Math.cos(he*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>ks(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>ks(t)?t:Gd(t,.075,.3),easeOutElastic:t=>ks(t)?t:qd(t,.075,.3),easeInOutElastic(t){return ks(t)?t:t<.5?.5*Gd(t*2,.1125,.45):.5+.5*qd(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-wi.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?wi.easeInBounce(t*2)*.5:wi.easeOutBounce(t*2-1)*.5+.5};function Vp(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function Zd(t){return Vp(t)?t:new Wi(t)}function Ua(t){return Vp(t)?t:new Wi(t).saturate(.5).darken(.1).hexString()}const gb=["x","y","borderWidth","radius","tension"],xb=["color","borderColor","backgroundColor"];function vb(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:xb},numbers:{type:"number",properties:gb}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function yb(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Jd=new Map;function bb(t,e){e=e||{};const n=t+JSON.stringify(e);let r=Jd.get(n);return r||(r=new Intl.NumberFormat(t,e),Jd.set(n,r)),r}function su(t,e,n){return bb(e,n).format(t)}const wb={values(t){return me(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const r=this.chart.options.locale;let i,s=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(i="scientific"),s=Sb(t,n)}const o=Ap(Math.abs(s)),a=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),su(t,r,l)}};function Sb(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var Yp={formatters:wb};function _b(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Yp.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Gn=Object.create(null),Zl=Object.create(null);function Si(t,e){if(!e)return t;const n=e.split(".");for(let r=0,i=n.length;r<i;++r){const s=n[r];t=t[s]||(t[s]=Object.create(null))}return t}function Ha(t,e,n){return typeof e=="string"?Bi(Si(t,e),n):Bi(Si(t,""),e)}class kb{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=r=>r.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(r,i)=>Ua(i.backgroundColor),this.hoverBorderColor=(r,i)=>Ua(i.borderColor),this.hoverColor=(r,i)=>Ua(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return Ha(this,e,n)}get(e){return Si(this,e)}describe(e,n){return Ha(Zl,e,n)}override(e,n){return Ha(Gn,e,n)}route(e,n,r,i){const s=Si(this,e),o=Si(this,r),a="_"+n;Object.defineProperties(s,{[a]:{value:s[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],u=o[i];return A(l)?Object.assign({},u,l):I(l,u)},set(l){this[a]=l}}})}apply(e){e.forEach(n=>n(this))}}var ce=new kb({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[vb,yb,_b]);function Nb(t){return!t||X(t.size)||X(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function eh(t,e,n,r,i){let s=e[i];return s||(s=e[i]=t.measureText(i).width,n.push(i)),s>r&&(r=s),r}function Pn(t,e,n){const r=t.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((e-i)*r)/r+i}function th(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function nh(t,e,n,r){Xp(t,e,n,r,null)}function Xp(t,e,n,r,i){let s,o,a,l,u,d,h,f;const m=e.pointStyle,v=e.rotation,g=e.radius;let b=(v||0)*rb;if(m&&typeof m=="object"&&(s=m.toString(),s==="[object HTMLImageElement]"||s==="[object HTMLCanvasElement]")){t.save(),t.translate(n,r),t.rotate(b),t.drawImage(m,-m.width/2,-m.height/2,m.width,m.height),t.restore();return}if(!(isNaN(g)||g<=0)){switch(t.beginPath(),m){default:i?t.ellipse(n,r,i/2,g,0,0,de):t.arc(n,r,g,0,de),t.closePath();break;case"triangle":d=i?i/2:g,t.moveTo(n+Math.sin(b)*d,r-Math.cos(b)*g),b+=Yd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*g),b+=Yd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*g),t.closePath();break;case"rectRounded":u=g*.516,l=g-u,o=Math.cos(b+Cn)*l,h=Math.cos(b+Cn)*(i?i/2-u:l),a=Math.sin(b+Cn)*l,f=Math.sin(b+Cn)*(i?i/2-u:l),t.arc(n-h,r-a,u,b-he,b-ge),t.arc(n+f,r-o,u,b-ge,b),t.arc(n+h,r+a,u,b,b+ge),t.arc(n-f,r+o,u,b+ge,b+he),t.closePath();break;case"rect":if(!v){l=Math.SQRT1_2*g,d=i?i/2:l,t.rect(n-d,r-l,2*d,2*l);break}b+=Cn;case"rectRot":h=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,a=Math.sin(b)*g,f=Math.sin(b)*(i?i/2:g),t.moveTo(n-h,r-a),t.lineTo(n+f,r-o),t.lineTo(n+h,r+a),t.lineTo(n-f,r+o),t.closePath();break;case"crossRot":b+=Cn;case"cross":h=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,a=Math.sin(b)*g,f=Math.sin(b)*(i?i/2:g),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o);break;case"star":h=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,a=Math.sin(b)*g,f=Math.sin(b)*(i?i/2:g),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o),b+=Cn,h=Math.cos(b)*(i?i/2:g),o=Math.cos(b)*g,a=Math.sin(b)*g,f=Math.sin(b)*(i?i/2:g),t.moveTo(n-h,r-a),t.lineTo(n+h,r+a),t.moveTo(n+f,r-o),t.lineTo(n-f,r+o);break;case"line":o=i?i/2:Math.cos(b)*g,a=Math.sin(b)*g,t.moveTo(n-o,r-a),t.lineTo(n+o,r+a);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(b)*(i?i/2:g),r+Math.sin(b)*g);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function Qp(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function ou(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function au(t){t.restore()}function jb(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),X(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function Cb(t,e,n,r,i){if(i.strikethrough||i.underline){const s=t.measureText(r),o=e-s.actualBoundingBoxLeft,a=e+s.actualBoundingBoxRight,l=n-s.actualBoundingBoxAscent,u=n+s.actualBoundingBoxDescent,d=i.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(o,d),t.lineTo(a,d),t.stroke()}}function Mb(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Yi(t,e,n,r,i,s={}){const o=me(e)?e:[e],a=s.strokeWidth>0&&s.strokeColor!=="";let l,u;for(t.save(),t.font=i.string,jb(t,s),l=0;l<o.length;++l)u=o[l],s.backdrop&&Mb(t,s.backdrop),a&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),X(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(u,n,r,s.maxWidth)),t.fillText(u,n,r,s.maxWidth),Cb(t,n,r,u,s),r+=Number(i.lineHeight);t.restore()}function Lo(t,e){const{x:n,y:r,w:i,h:s,radius:o}=e;t.arc(n+o.topLeft,r+o.topLeft,o.topLeft,1.5*he,he,!0),t.lineTo(n,r+s-o.bottomLeft),t.arc(n+o.bottomLeft,r+s-o.bottomLeft,o.bottomLeft,he,ge,!0),t.lineTo(n+i-o.bottomRight,r+s),t.arc(n+i-o.bottomRight,r+s-o.bottomRight,o.bottomRight,ge,0,!0),t.lineTo(n+i,r+o.topRight),t.arc(n+i-o.topRight,r+o.topRight,o.topRight,0,-ge,!0),t.lineTo(n+o.topLeft,r)}const Pb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Eb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Tb(t,e){const n=(""+t).match(Pb);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const Rb=t=>+t||0;function lu(t,e){const n={},r=A(e),i=r?Object.keys(e):e,s=A(t)?r?o=>I(t[o],t[e[o]]):o=>t[o]:()=>t;for(const o of i)n[o]=Rb(s(o));return n}function Kp(t){return lu(t,{top:"y",right:"x",bottom:"y",left:"x"})}function _r(t){return lu(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ct(t){const e=Kp(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Ce(t,e){t=t||{},e=e||ce.font;let n=I(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let r=I(t.style,e.style);r&&!(""+r).match(Eb)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);const i={family:I(t.family,e.family),lineHeight:Tb(I(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:I(t.weight,e.weight),string:""};return i.string=Nb(i),i}function Ns(t,e,n,r){let i,s,o;for(i=0,s=t.length;i<s;++i)if(o=t[i],o!==void 0&&o!==void 0)return o}function Ob(t,e,n){const{min:r,max:i}=t,s=zp(e,(i-r)/2),o=(a,l)=>n&&a===0?0:a+l;return{min:o(r,-Math.abs(s)),max:o(i,s)}}function Ar(t,e){return Object.assign(Object.create(t),e)}function cu(t,e=[""],n,r,i=()=>t[0]){const s=n||t;typeof r>"u"&&(r=Jp("_fallback",t));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:r,_getTarget:i,override:a=>cu([a,...t],e,s,r)};return new Proxy(o,{deleteProperty(a,l){return delete a[l],delete a._keys,delete t[0][l],!0},get(a,l){return qp(a,l,()=>Ub(l,e,t,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(a,l){return ih(a).includes(l)},ownKeys(a){return ih(a)},set(a,l,u){const d=a._storage||(a._storage=i());return a[l]=d[l]=u,delete a._keys,!0}})}function Dr(t,e,n,r){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:Gp(t,r),setContext:s=>Dr(t,s,n,r),override:s=>Dr(t.override(s),e,n,r)};return new Proxy(i,{deleteProperty(s,o){return delete s[o],delete t[o],!0},get(s,o,a){return qp(s,o,()=>Lb(s,o,a))},getOwnPropertyDescriptor(s,o){return s._descriptors.allKeys?Reflect.has(t,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,o)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(s,o){return Reflect.has(t,o)},ownKeys(){return Reflect.ownKeys(t)},set(s,o,a){return t[o]=a,delete s[o],!0}})}function Gp(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:r,isScriptable:Sn(n)?n:()=>n,isIndexable:Sn(r)?r:()=>r}}const Db=(t,e)=>t?t+nu(e):e,uu=(t,e)=>A(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function qp(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const r=n();return t[e]=r,r}function Lb(t,e,n){const{_proxy:r,_context:i,_subProxy:s,_descriptors:o}=t;let a=r[e];return Sn(a)&&o.isScriptable(e)&&(a=$b(e,a,t,n)),me(a)&&a.length&&(a=zb(e,a,t,o.isIndexable)),uu(e,a)&&(a=Dr(a,i,s&&s[e],o)),a}function $b(t,e,n,r){const{_proxy:i,_context:s,_subProxy:o,_stack:a}=n;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(s,o||r);return a.delete(t),uu(t,l)&&(l=du(i._scopes,i,t,l)),l}function zb(t,e,n,r){const{_proxy:i,_context:s,_subProxy:o,_descriptors:a}=n;if(typeof s.index<"u"&&r(t))return e[s.index%e.length];if(A(e[0])){const l=e,u=i._scopes.filter(d=>d!==l);e=[];for(const d of l){const h=du(u,i,t,d);e.push(Dr(h,s,o&&o[t],a))}}return e}function Zp(t,e,n){return Sn(t)?t(e,n):t}const Fb=(t,e)=>t===!0?e:typeof t=="string"?Kn(e,t):void 0;function Ab(t,e,n,r,i){for(const s of e){const o=Fb(n,s);if(o){t.add(o);const a=Zp(o._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==r)return a}else if(o===!1&&typeof r<"u"&&n!==r)return null}return!1}function du(t,e,n,r){const i=e._rootScopes,s=Zp(e._fallback,n,r),o=[...t,...i],a=new Set;a.add(r);let l=rh(a,o,n,s||n,r);return l===null||typeof s<"u"&&s!==n&&(l=rh(a,o,s,l,r),l===null)?!1:cu(Array.from(a),[""],i,s,()=>Ib(e,n,r))}function rh(t,e,n,r,i){for(;n;)n=Ab(t,e,n,r,i);return n}function Ib(t,e,n){const r=t._getTarget();e in r||(r[e]={});const i=r[e];return me(i)&&A(n)?n:i||{}}function Ub(t,e,n,r){let i;for(const s of e)if(i=Jp(Db(s,t),n),typeof i<"u")return uu(t,i)?du(n,r,t,i):i}function Jp(t,e){for(const n of e){if(!n)continue;const r=n[t];if(typeof r<"u")return r}}function ih(t){let e=t._keys;return e||(e=t._keys=Hb(t._scopes)),e}function Hb(t){const e=new Set;for(const n of t)for(const r of Object.keys(n).filter(i=>!i.startsWith("_")))e.add(r);return Array.from(e)}function hu(){return typeof window<"u"&&typeof document<"u"}function fu(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function $o(t,e,n){let r;return typeof t=="string"?(r=parseInt(t,10),t.indexOf("%")!==-1&&(r=r/100*e.parentNode[n])):r=t,r}const aa=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function Wb(t,e){return aa(t).getPropertyValue(e)}const Bb=["top","right","bottom","left"];function Hn(t,e,n){const r={};n=n?"-"+n:"";for(let i=0;i<4;i++){const s=Bb[i];r[s]=parseFloat(t[e+"-"+s+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}const Vb=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function Yb(t,e){const n=t.touches,r=n&&n.length?n[0]:t,{offsetX:i,offsetY:s}=r;let o=!1,a,l;if(Vb(i,s,t.target))a=i,l=s;else{const u=e.getBoundingClientRect();a=r.clientX-u.left,l=r.clientY-u.top,o=!0}return{x:a,y:l,box:o}}function Ln(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:r}=e,i=aa(n),s=i.boxSizing==="border-box",o=Hn(i,"padding"),a=Hn(i,"border","width"),{x:l,y:u,box:d}=Yb(t,n),h=o.left+(d&&a.left),f=o.top+(d&&a.top);let{width:m,height:v}=e;return s&&(m-=o.width+a.width,v-=o.height+a.height),{x:Math.round((l-h)/m*n.width/r),y:Math.round((u-f)/v*n.height/r)}}function Xb(t,e,n){let r,i;if(e===void 0||n===void 0){const s=t&&fu(t);if(!s)e=t.clientWidth,n=t.clientHeight;else{const o=s.getBoundingClientRect(),a=aa(s),l=Hn(a,"border","width"),u=Hn(a,"padding");e=o.width-u.width-l.width,n=o.height-u.height-l.height,r=$o(a.maxWidth,s,"clientWidth"),i=$o(a.maxHeight,s,"clientHeight")}}return{width:e,height:n,maxWidth:r||Ro,maxHeight:i||Ro}}const js=t=>Math.round(t*10)/10;function Qb(t,e,n,r){const i=aa(t),s=Hn(i,"margin"),o=$o(i.maxWidth,t,"clientWidth")||Ro,a=$o(i.maxHeight,t,"clientHeight")||Ro,l=Xb(t,e,n);let{width:u,height:d}=l;if(i.boxSizing==="content-box"){const f=Hn(i,"border","width"),m=Hn(i,"padding");u-=m.width+f.width,d-=m.height+f.height}return u=Math.max(0,u-s.width),d=Math.max(0,r?u/r:d-s.height),u=js(Math.min(u,o,l.maxWidth)),d=js(Math.min(d,a,l.maxHeight)),u&&!d&&(d=js(u/2)),(e!==void 0||n!==void 0)&&r&&l.height&&d>l.height&&(d=l.height,u=js(Math.floor(d*r))),{width:u,height:d}}function sh(t,e,n){const r=e||1,i=Math.floor(t.height*r),s=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const o=t.canvas;return o.style&&(n||!o.style.height&&!o.style.width)&&(o.style.height=`${t.height}px`,o.style.width=`${t.width}px`),t.currentDevicePixelRatio!==r||o.height!==i||o.width!==s?(t.currentDevicePixelRatio=r,o.height=i,o.width=s,t.ctx.setTransform(r,0,0,r,0,0),!0):!1}const Kb=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};hu()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function oh(t,e){const n=Wb(t,e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}const Gb=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,r){return n-r},leftForLtr(n,r){return n-r}}},qb=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function kr(t,e,n){return t?Gb(e,n):qb()}function eg(t,e){let n,r;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,r=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function tg(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Cs(t,e,n){return t.options.clip?t[n]:e[n]}function Zb(t,e){const{xScale:n,yScale:r}=t;return n&&r?{left:Cs(n,e,"left"),right:Cs(n,e,"right"),top:Cs(r,e,"top"),bottom:Cs(r,e,"bottom")}:e}function Jb(t,e){const n=e._clip;if(n.disabled)return!1;const r=Zb(e,t.chartArea);return{left:n.left===!1?0:r.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:r.right+(n.right===!0?0:n.right),top:n.top===!1?0:r.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:r.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class ew{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,r,i){const s=n.listeners[i],o=n.duration;s.forEach(a=>a({chart:e,initial:n.initial,numSteps:o,currentStep:Math.min(r-n.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=Wp.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((r,i)=>{if(!r.running||!r.items.length)return;const s=r.items;let o=s.length-1,a=!1,l;for(;o>=0;--o)l=s[o],l._active?(l._total>r.duration&&(r.duration=l._total),l.tick(e),a=!0):(s[o]=s[s.length-1],s.pop());a&&(i.draw(),this._notify(i,r,e,"progress")),s.length||(r.running=!1,this._notify(i,r,e,"complete"),r.initial=!1),n+=s.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let r=n.get(e);return r||(r={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,r)),r}listen(e,n,r){this._getAnims(e).listeners[n].push(r)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((r,i)=>Math.max(r,i._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const r=n.items;let i=r.length-1;for(;i>=0;--i)r[i].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Tt=new ew;const ah="transparent",tw={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const r=Zd(t||ah),i=r.valid&&Zd(e||ah);return i&&i.valid?i.mix(r,n).hexString():e},number(t,e,n){return t+(e-t)*n}};class nw{constructor(e,n,r,i){const s=n[r];i=Ns([e.to,i,s,e.from]);const o=Ns([e.from,s,i]);this._active=!0,this._fn=e.fn||tw[e.type||typeof o],this._easing=wi[e.easing]||wi.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=r,this._from=o,this._to=i,this._promises=void 0}active(){return this._active}update(e,n,r){if(this._active){this._notify(!1);const i=this._target[this._prop],s=r-this._start,o=this._duration-s;this._start=r,this._duration=Math.floor(Math.max(o,e.duration)),this._total+=s,this._loop=!!e.loop,this._to=Ns([e.to,n,i,e.from]),this._from=Ns([e.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,r=this._duration,i=this._prop,s=this._from,o=this._loop,a=this._to;let l;if(this._active=s!==a&&(o||n<r),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=s;return}l=n/r%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(s,a,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,r)=>{e.push({res:n,rej:r})})}_notify(e){const n=e?"res":"rej",r=this._promises||[];for(let i=0;i<r.length;i++)r[i][n]()}}class ng{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!A(e))return;const n=Object.keys(ce.animation),r=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const s=e[i];if(!A(s))return;const o={};for(const a of n)o[a]=s[a];(me(s.properties)&&s.properties||[i]).forEach(a=>{(a===i||!r.has(a))&&r.set(a,o)})})}_animateOptions(e,n){const r=n.options,i=iw(e,r);if(!i)return[];const s=this._createAnimations(i,r);return r.$shared&&rw(e.options.$animations,r).then(()=>{e.options=r},()=>{}),s}_createAnimations(e,n){const r=this._properties,i=[],s=e.$animations||(e.$animations={}),o=Object.keys(n),a=Date.now();let l;for(l=o.length-1;l>=0;--l){const u=o[l];if(u.charAt(0)==="$")continue;if(u==="options"){i.push(...this._animateOptions(e,n));continue}const d=n[u];let h=s[u];const f=r.get(u);if(h)if(f&&h.active()){h.update(f,d,a);continue}else h.cancel();if(!f||!f.duration){e[u]=d;continue}s[u]=h=new nw(f,e,u,d),i.push(h)}return i}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const r=this._createAnimations(e,n);if(r.length)return Tt.add(this._chart,r),!0}}function rw(t,e){const n=[],r=Object.keys(e);for(let i=0;i<r.length;i++){const s=t[r[i]];s&&s.active()&&n.push(s.wait())}return Promise.all(n)}function iw(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function lh(t,e){const n=t&&t.options||{},r=n.reverse,i=n.min===void 0?e:0,s=n.max===void 0?e:0;return{start:r?s:i,end:r?i:s}}function sw(t,e,n){if(n===!1)return!1;const r=lh(t,n),i=lh(e,n);return{top:i.end,right:r.end,bottom:i.start,left:r.start}}function ow(t){let e,n,r,i;return A(t)?(e=t.top,n=t.right,r=t.bottom,i=t.left):e=n=r=i=t,{top:e,right:n,bottom:r,left:i,disabled:t===!1}}function rg(t,e){const n=[],r=t._getSortedDatasetMetas(e);let i,s;for(i=0,s=r.length;i<s;++i)n.push(r[i].index);return n}function ch(t,e,n,r={}){const i=t.keys,s=r.mode==="single";let o,a,l,u;if(e===null)return;let d=!1;for(o=0,a=i.length;o<a;++o){if(l=+i[o],l===n){if(d=!0,r.all)continue;break}u=t.values[l],lt(u)&&(s||e===0||yn(e)===yn(u))&&(e+=u)}return!d&&!r.all?0:e}function aw(t,e){const{iScale:n,vScale:r}=e,i=n.axis==="x"?"x":"y",s=r.axis==="x"?"x":"y",o=Object.keys(t),a=new Array(o.length);let l,u,d;for(l=0,u=o.length;l<u;++l)d=o[l],a[l]={[i]:d,[s]:t[d]};return a}function Wa(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function lw(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function cw(t){const{min:e,max:n,minDefined:r,maxDefined:i}=t.getUserBounds();return{min:r?e:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function uw(t,e,n){const r=t[e]||(t[e]={});return r[n]||(r[n]={})}function uh(t,e,n,r){for(const i of e.getMatchingVisibleMetas(r).reverse()){const s=t[i.index];if(n&&s>0||!n&&s<0)return i.index}return null}function dh(t,e){const{chart:n,_cachedMeta:r}=t,i=n._stacks||(n._stacks={}),{iScale:s,vScale:o,index:a}=r,l=s.axis,u=o.axis,d=lw(s,o,r),h=e.length;let f;for(let m=0;m<h;++m){const v=e[m],{[l]:g,[u]:b}=v,p=v._stacks||(v._stacks={});f=p[u]=uw(i,d,g),f[a]=b,f._top=uh(f,o,!0,r.type),f._bottom=uh(f,o,!1,r.type);const x=f._visualValues||(f._visualValues={});x[a]=b}}function Ba(t,e){const n=t.scales;return Object.keys(n).filter(r=>n[r].axis===e).shift()}function dw(t,e){return Ar(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function hw(t,e,n){return Ar(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function qr(t,e){const n=t.controller.index,r=t.vScale&&t.vScale.axis;if(r){e=e||t._parsed;for(const i of e){const s=i._stacks;if(!s||s[r]===void 0||s[r][n]===void 0)return;delete s[r][n],s[r]._visualValues!==void 0&&s[r]._visualValues[n]!==void 0&&delete s[r]._visualValues[n]}}}const Va=t=>t==="reset"||t==="none",hh=(t,e)=>e?t:Object.assign({},t),fw=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:rg(n,!0),values:null};class Nr{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Wa(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&qr(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,r=this.getDataset(),i=(h,f,m,v)=>h==="x"?f:h==="r"?v:m,s=n.xAxisID=I(r.xAxisID,Ba(e,"x")),o=n.yAxisID=I(r.yAxisID,Ba(e,"y")),a=n.rAxisID=I(r.rAxisID,Ba(e,"r")),l=n.indexAxis,u=n.iAxisID=i(l,s,o,a),d=n.vAxisID=i(l,o,s,a);n.xScale=this.getScaleForId(s),n.yScale=this.getScaleForId(o),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&Kd(this._data,this),e._stacked&&qr(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),r=this._data;if(A(n)){const i=this._cachedMeta;this._data=aw(n,i)}else if(r!==n){if(r){Kd(r,this);const i=this._cachedMeta;qr(i),i._parsed=[]}n&&Object.isExtensible(n)&&fb(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,r=this.getDataset();let i=!1;this._dataCheck();const s=n._stacked;n._stacked=Wa(n.vScale,n),n.stack!==r.stack&&(i=!0,qr(n),n.stack=r.stack),this._resyncElements(e),(i||s!==n._stacked)&&(dh(this,n._parsed),n._stacked=Wa(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),r=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(r,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:r,_data:i}=this,{iScale:s,_stacked:o}=r,a=s.axis;let l=e===0&&n===i.length?!0:r._sorted,u=e>0&&r._parsed[e-1],d,h,f;if(this._parsing===!1)r._parsed=i,r._sorted=!0,f=i;else{me(i[e])?f=this.parseArrayData(r,i,e,n):A(i[e])?f=this.parseObjectData(r,i,e,n):f=this.parsePrimitiveData(r,i,e,n);const m=()=>h[a]===null||u&&h[a]<u[a];for(d=0;d<n;++d)r._parsed[d+e]=h=f[d],l&&(m()&&(l=!1),u=h);r._sorted=l}o&&dh(this,f)}parsePrimitiveData(e,n,r,i){const{iScale:s,vScale:o}=e,a=s.axis,l=o.axis,u=s.getLabels(),d=s===o,h=new Array(i);let f,m,v;for(f=0,m=i;f<m;++f)v=f+r,h[f]={[a]:d||s.parse(u[v],v),[l]:o.parse(n[v],v)};return h}parseArrayData(e,n,r,i){const{xScale:s,yScale:o}=e,a=new Array(i);let l,u,d,h;for(l=0,u=i;l<u;++l)d=l+r,h=n[d],a[l]={x:s.parse(h[0],d),y:o.parse(h[1],d)};return a}parseObjectData(e,n,r,i){const{xScale:s,yScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=new Array(i);let d,h,f,m;for(d=0,h=i;d<h;++d)f=d+r,m=n[f],u[d]={x:s.parse(Kn(m,a),f),y:o.parse(Kn(m,l),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,r){const i=this.chart,s=this._cachedMeta,o=n[e.axis],a={keys:rg(i,!0),values:n._stacks[e.axis]._visualValues};return ch(a,o,s.index,{mode:r})}updateRangeFromParsed(e,n,r,i){const s=r[n.axis];let o=s===null?NaN:s;const a=i&&r._stacks[n.axis];i&&a&&(i.values=a,o=ch(i,s,this._cachedMeta.index)),e.min=Math.min(e.min,o),e.max=Math.max(e.max,o)}getMinMax(e,n){const r=this._cachedMeta,i=r._parsed,s=r._sorted&&e===r.iScale,o=i.length,a=this._getOtherScale(e),l=fw(n,r,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=cw(a);let f,m;function v(){m=i[f];const g=m[a.axis];return!lt(m[e.axis])||d>g||h<g}for(f=0;f<o&&!(!v()&&(this.updateRangeFromParsed(u,e,m,l),s));++f);if(s){for(f=o-1;f>=0;--f)if(!v()){this.updateRangeFromParsed(u,e,m,l);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,r=[];let i,s,o;for(i=0,s=n.length;i<s;++i)o=n[i][e.axis],lt(o)&&r.push(o);return r}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,r=n.iScale,i=n.vScale,s=this.getParsed(e);return{label:r?""+r.getLabelForValue(s[r.axis]):"",value:i?""+i.getLabelForValue(s[i.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=ow(I(this.options.clip,sw(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,r=this._cachedMeta,i=r.data||[],s=n.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||i.length-a,u=this.options.drawActiveElementsOnTop;let d;for(r.dataset&&r.dataset.draw(e,s,a,l),d=a;d<a+l;++d){const h=i[d];h.hidden||(h.active&&u?o.push(h):h.draw(e,s))}for(d=0;d<o.length;++d)o[d].draw(e,s)}getStyle(e,n){const r=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(r):this.resolveDataElementOptions(e||0,r)}getContext(e,n,r){const i=this.getDataset();let s;if(e>=0&&e<this._cachedMeta.data.length){const o=this._cachedMeta.data[e];s=o.$context||(o.$context=hw(this.getContext(),e,o)),s.parsed=this.getParsed(e),s.raw=i.data[e],s.index=s.dataIndex=e}else s=this.$context||(this.$context=dw(this.chart.getContext(),this.index)),s.dataset=i,s.index=s.datasetIndex=this.index;return s.active=!!n,s.mode=r,s}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",r){const i=n==="active",s=this._cachedDataOpts,o=e+"-"+n,a=s[o],l=this.enableOptionSharing&&Vi(r);if(a)return hh(a,l);const u=this.chart.config,d=u.datasetElementScopeKeys(this._type,e),h=i?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),d),m=Object.keys(ce.elements[e]),v=()=>this.getContext(r,i,n),g=u.resolveNamedOptions(f,m,v,h);return g.$shared&&(g.$shared=l,s[o]=Object.freeze(hh(g,l))),g}_resolveAnimations(e,n,r){const i=this.chart,s=this._cachedDataOpts,o=`animation-${n}`,a=s[o];if(a)return a;let l;if(i.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);l=d.createResolver(f,this.getContext(e,r,n))}const u=new ng(i,l&&l.animations);return l&&l._cacheable&&(s[o]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||Va(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const r=this.resolveDataElementOptions(e,n),i=this._sharedOptions,s=this.getSharedOptions(r),o=this.includeOptions(n,s)||s!==i;return this.updateSharedOptions(s,n,r),{sharedOptions:s,includeOptions:o}}updateElement(e,n,r,i){Va(i)?Object.assign(e,r):this._resolveAnimations(n,i).update(e,r)}updateSharedOptions(e,n,r){e&&!Va(n)&&this._resolveAnimations(void 0,n).update(e,r)}_setStyle(e,n,r,i){e.active=i;const s=this.getStyle(n,i);this._resolveAnimations(n,r,i).update(e,{options:!i&&this.getSharedOptions(s)||s})}removeHoverStyle(e,n,r){this._setStyle(e,r,"active",!1)}setHoverStyle(e,n,r){this._setStyle(e,r,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,r=this._cachedMeta.data;for(const[a,l,u]of this._syncList)this[a](l,u);this._syncList=[];const i=r.length,s=n.length,o=Math.min(s,i);o&&this.parse(0,o),s>i?this._insertElements(i,s-i,e):s<i&&this._removeElements(s,i-s)}_insertElements(e,n,r=!0){const i=this._cachedMeta,s=i.data,o=e+n;let a;const l=u=>{for(u.length+=n,a=u.length-1;a>=o;a--)u[a]=u[a-n]};for(l(s),a=e;a<o;++a)s[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(e,n),r&&this.updateElements(s,e,n,"reset")}updateElements(e,n,r,i){}_removeElements(e,n){const r=this._cachedMeta;if(this._parsing){const i=r._parsed.splice(e,n);r._stacked&&qr(r,i)}r.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,r,i]=e;this[n](r,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const r=arguments.length-2;r&&this._sync(["_insertElements",e,r])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}L(Nr,"defaults",{}),L(Nr,"datasetElementType",null),L(Nr,"dataElementType",null);function mw(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let r=[];for(let i=0,s=n.length;i<s;i++)r=r.concat(n[i].controller.getAllParsedValues(t));t._cache.$bar=Hp(r.sort((i,s)=>i-s))}return t._cache.$bar}function pw(t){const e=t.iScale,n=mw(e,t.type);let r=e._length,i,s,o,a;const l=()=>{o===32767||o===-32768||(Vi(a)&&(r=Math.min(r,Math.abs(o-a)||r)),a=o)};for(i=0,s=n.length;i<s;++i)o=e.getPixelForValue(n[i]),l();for(a=void 0,i=0,s=e.ticks.length;i<s;++i)o=e.getPixelForTick(i),l();return r}function gw(t,e,n,r){const i=n.barThickness;let s,o;return X(i)?(s=e.min*n.categoryPercentage,o=n.barPercentage):(s=i*r,o=1),{chunk:s/r,ratio:o,start:e.pixels[t]-s/2}}function xw(t,e,n,r){const i=e.pixels,s=i[t];let o=t>0?i[t-1]:null,a=t<i.length-1?i[t+1]:null;const l=n.categoryPercentage;o===null&&(o=s-(a===null?e.end-e.start:a-s)),a===null&&(a=s+s-o);const u=s-(s-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/r,ratio:n.barPercentage,start:u}}function vw(t,e,n,r){const i=n.parse(t[0],r),s=n.parse(t[1],r),o=Math.min(i,s),a=Math.max(i,s);let l=o,u=a;Math.abs(o)>Math.abs(a)&&(l=a,u=o),e[n.axis]=u,e._custom={barStart:l,barEnd:u,start:i,end:s,min:o,max:a}}function ig(t,e,n,r){return me(t)?vw(t,e,n,r):e[n.axis]=n.parse(t,r),e}function fh(t,e,n,r){const i=t.iScale,s=t.vScale,o=i.getLabels(),a=i===s,l=[];let u,d,h,f;for(u=n,d=n+r;u<d;++u)f=e[u],h={},h[i.axis]=a||i.parse(o[u],u),l.push(ig(f,h,s,u));return l}function Ya(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function yw(t,e,n){return t!==0?yn(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function bw(t){let e,n,r,i,s;return t.horizontal?(e=t.base>t.x,n="left",r="right"):(e=t.base<t.y,n="bottom",r="top"),e?(i="end",s="start"):(i="start",s="end"),{start:n,end:r,reverse:e,top:i,bottom:s}}function ww(t,e,n,r){let i=e.borderSkipped;const s={};if(!i){t.borderSkipped=s;return}if(i===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:o,end:a,reverse:l,top:u,bottom:d}=bw(t);i==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===r?i=u:(n._bottom||0)===r?i=d:(s[mh(d,o,a,l)]=!0,i=u)),s[mh(i,o,a,l)]=!0,t.borderSkipped=s}function mh(t,e,n,r){return r?(t=Sw(t,e,n),t=ph(t,n,e)):t=ph(t,e,n),t}function Sw(t,e,n){return t===e?n:t===n?e:t}function ph(t,e,n){return t==="start"?e:t==="end"?n:t}function _w(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Ks extends Nr{parsePrimitiveData(e,n,r,i){return fh(e,n,r,i)}parseArrayData(e,n,r,i){return fh(e,n,r,i)}parseObjectData(e,n,r,i){const{iScale:s,vScale:o}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,u=s.axis==="x"?a:l,d=o.axis==="x"?a:l,h=[];let f,m,v,g;for(f=r,m=r+i;f<m;++f)g=n[f],v={},v[s.axis]=s.parse(Kn(g,u),f),h.push(ig(Kn(g,d),v,o,f));return h}updateRangeFromParsed(e,n,r,i){super.updateRangeFromParsed(e,n,r,i);const s=r._custom;s&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,s.min),e.max=Math.max(e.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:r,vScale:i}=n,s=this.getParsed(e),o=s._custom,a=Ya(o)?"["+o.start+", "+o.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+r.getLabelForValue(s[r.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,r,i){const s=i==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),u=a.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,i);for(let m=n;m<n+r;m++){const v=this.getParsed(m),g=s||X(v[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(m),b=this._calculateBarIndexPixels(m,d),p=(v._stacks||{})[a.axis],x={horizontal:u,base:g.base,enableBorderRadius:!p||Ya(v._custom)||o===p._top||o===p._bottom,x:u?g.head:b.center,y:u?b.center:g.head,height:u?b.size:Math.abs(g.size),width:u?Math.abs(g.size):b.size};f&&(x.options=h||this.resolveDataElementOptions(m,e[m].active?"active":i));const y=x.options||e[m].options;ww(x,y,p,o),_w(x,y,d.ratio),this.updateElement(e[m],m,x,i)}}_getStacks(e,n){const{iScale:r}=this._cachedMeta,i=r.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),s=r.options.stacked,o=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[r.axis],u=d=>{const h=d._parsed.find(m=>m[r.axis]===l),f=h&&h[d.vScale.axis];if(X(f)||isNaN(f))return!0};for(const d of i)if(!(n!==void 0&&u(d))&&((s===!1||o.indexOf(d.stack)===-1||s===void 0&&d.stack===void 0)&&o.push(d.stack),d.index===e))break;return o.length||o.push(void 0),o}_getStackCount(e){return this._getStacks(void 0,e).length}_getStackIndex(e,n,r){const i=this._getStacks(e,r),s=n!==void 0?i.indexOf(n):-1;return s===-1?i.length-1:s}_getRuler(){const e=this.options,n=this._cachedMeta,r=n.iScale,i=[];let s,o;for(s=0,o=n.data.length;s<o;++s)i.push(r.getPixelForValue(this.getParsed(s)[r.axis],s));const a=e.barThickness;return{min:a||pw(n),pixels:i,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:e.grouped,ratio:a?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:r,index:i},options:{base:s,minBarLength:o}}=this,a=s||0,l=this.getParsed(e),u=l._custom,d=Ya(u);let h=l[n.axis],f=0,m=r?this.applyStack(n,l,r):h,v,g;m!==h&&(f=m-h,m=h),d&&(h=u.barStart,m=u.barEnd-u.barStart,h!==0&&yn(h)!==yn(u.barEnd)&&(f=0),f+=h);const b=!X(s)&&!d?s:f;let p=n.getPixelForValue(b);if(this.chart.getDataVisibility(e)?v=n.getPixelForValue(f+m):v=p,g=v-p,Math.abs(g)<o){g=yw(g,n,a)*o,h===a&&(p-=g/2);const x=n.getPixelForDecimal(0),y=n.getPixelForDecimal(1),w=Math.min(x,y),S=Math.max(x,y);p=Math.max(Math.min(p,S),w),v=p+g,r&&!d&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(v)-n.getValueForPixel(p))}if(p===n.getPixelForValue(a)){const x=yn(g)*n.getLineWidthForValue(a)/2;p+=x,g-=x}return{size:g,base:p,head:v,center:v+g/2}}_calculateBarIndexPixels(e,n){const r=n.scale,i=this.options,s=i.skipNull,o=I(i.maxBarThickness,1/0);let a,l;if(n.grouped){const u=s?this._getStackCount(e):n.stackCount,d=i.barThickness==="flex"?xw(e,n,i,u):gw(e,n,i,u),h=this._getStackIndex(this.index,this._cachedMeta.stack,s?e:void 0);a=d.start+d.chunk*h+d.chunk/2,l=Math.min(o,d.chunk*d.ratio)}else a=r.getPixelForValue(this.getParsed(e)[r.axis],e),l=Math.min(o,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,r=e.data,i=r.length;let s=0;for(;s<i;++s)this.getParsed(s)[n.axis]!==null&&!r[s].hidden&&r[s].draw(this._ctx)}}L(Ks,"id","bar"),L(Ks,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),L(Ks,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function kw(t,e,n){let r=1,i=1,s=0,o=0;if(e<de){const a=t,l=a+e,u=Math.cos(a),d=Math.sin(a),h=Math.cos(l),f=Math.sin(l),m=(y,w,S)=>Do(y,a,l,!0)?1:Math.max(w,w*n,S,S*n),v=(y,w,S)=>Do(y,a,l,!0)?-1:Math.min(w,w*n,S,S*n),g=m(0,u,h),b=m(ge,d,f),p=v(he,u,h),x=v(he+ge,d,f);r=(g-p)/2,i=(b-x)/2,s=-(g+p)/2,o=-(b+x)/2}return{ratioX:r,ratioY:i,offsetX:s,offsetY:o}}class ai extends Nr{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const r=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=r;else{let s=l=>+r[l];if(A(r[e])){const{key:l="value"}=this._parsing;s=u=>+Kn(r[u],l)}let o,a;for(o=e,a=e+n;o<a;++o)i._parsed[o]=s(o)}}_getRotation(){return At(this.options.rotation-90)}_getCircumference(){return At(this.options.circumference)}_getRotationExtents(){let e=de,n=-de;for(let r=0;r<this.chart.data.datasets.length;++r)if(this.chart.isDatasetVisible(r)&&this.chart.getDatasetMeta(r).type===this._type){const i=this.chart.getDatasetMeta(r).controller,s=i._getRotation(),o=i._getCircumference();e=Math.min(e,s),n=Math.max(n,s+o)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:r}=n,i=this._cachedMeta,s=i.data,o=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,a=Math.max((Math.min(r.width,r.height)-o)/2,0),l=Math.min(q1(this.options.cutout,a),1),u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:m,offsetX:v,offsetY:g}=kw(h,d,l),b=(r.width-o)/f,p=(r.height-o)/m,x=Math.max(Math.min(b,p)/2,0),y=zp(this.options.radius,x),w=Math.max(y*l,0),S=(y-w)/this._getVisibleDatasetWeightTotal();this.offsetX=v*y,this.offsetY=g*y,i.total=this.calculateTotal(),this.outerRadius=y-S*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-S*u,0),this.updateElements(s,0,s.length,e)}_circumference(e,n){const r=this.options,i=this._cachedMeta,s=this._getCircumference();return n&&r.animation.animateRotate||!this.chart.getDataVisibility(e)||i._parsed[e]===null||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*s/de)}updateElements(e,n,r,i){const s=i==="reset",o=this.chart,a=o.chartArea,u=o.options.animation,d=(a.left+a.right)/2,h=(a.top+a.bottom)/2,f=s&&u.animateScale,m=f?0:this.innerRadius,v=f?0:this.outerRadius,{sharedOptions:g,includeOptions:b}=this._getSharedOptions(n,i);let p=this._getRotation(),x;for(x=0;x<n;++x)p+=this._circumference(x,s);for(x=n;x<n+r;++x){const y=this._circumference(x,s),w=e[x],S={x:d+this.offsetX,y:h+this.offsetY,startAngle:p,endAngle:p+y,circumference:y,outerRadius:v,innerRadius:m};b&&(S.options=g||this.resolveDataElementOptions(x,w.active?"active":i)),p+=y,this.updateElement(w,x,S,i)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let r=0,i;for(i=0;i<n.length;i++){const s=e._parsed[i];s!==null&&!isNaN(s)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(r+=Math.abs(s))}return r}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?de*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,r=this.chart,i=r.data.labels||[],s=su(n._parsed[e],r.options.locale);return{label:i[e]||"",value:s}}getMaxBorderWidth(e){let n=0;const r=this.chart;let i,s,o,a,l;if(!e){for(i=0,s=r.data.datasets.length;i<s;++i)if(r.isDatasetVisible(i)){o=r.getDatasetMeta(i),e=o.data,a=o.controller;break}}if(!e)return 0;for(i=0,s=e.length;i<s;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let r=0,i=e.length;r<i;++r){const s=this.resolveDataElementOptions(r);n=Math.max(n,s.offset||0,s.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let r=0;r<e;++r)this.chart.isDatasetVisible(r)&&(n+=this._getRingWeight(r));return n}_getRingWeight(e){return Math.max(I(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}L(ai,"id","doughnut"),L(ai,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),L(ai,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),L(ai,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:r,color:i}}=e.legend.options;return n.labels.map((s,o)=>{const l=e.getDatasetMeta(0).controller.getStyle(o);return{text:s,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:r,hidden:!e.getDataVisibility(o),index:o}})}return[]}},onClick(e,n,r){r.chart.toggleDataVisibility(n.index),r.chart.update()}}}});function En(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class mu{constructor(e){L(this,"options");this.options=e||{}}static override(e){Object.assign(mu.prototype,e)}init(){}formats(){return En()}parse(){return En()}format(){return En()}add(){return En()}diff(){return En()}startOf(){return En()}endOf(){return En()}}var Nw={_date:mu};function jw(t,e,n,r){const{controller:i,data:s,_sorted:o}=t,a=i._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&e!=="r"&&o&&s.length){const u=a._reversePixels?db:ql;if(r){if(i._sharedOptions){const d=s[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=u(s,e,n-h),m=u(s,e,n+h);return{lo:f.lo,hi:m.hi}}}}else{const d=u(s,e,n);if(l){const{vScale:h}=i._cachedMeta,{_parsed:f}=t,m=f.slice(0,d.lo+1).reverse().findIndex(g=>!X(g[h.axis]));d.lo-=Math.max(0,m);const v=f.slice(d.hi).findIndex(g=>!X(g[h.axis]));d.hi+=Math.max(0,v)}return d}}return{lo:0,hi:s.length-1}}function la(t,e,n,r,i){const s=t.getSortedVisibleDatasetMetas(),o=n[e];for(let a=0,l=s.length;a<l;++a){const{index:u,data:d}=s[a],{lo:h,hi:f}=jw(s[a],e,o,i);for(let m=h;m<=f;++m){const v=d[m];v.skip||r(v,u,m)}}}function Cw(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(r,i){const s=e?Math.abs(r.x-i.x):0,o=n?Math.abs(r.y-i.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(o,2))}}function Xa(t,e,n,r,i){const s=[];return!i&&!t.isPointInArea(e)||la(t,n,e,function(a,l,u){!i&&!Qp(a,t.chartArea,0)||a.inRange(e.x,e.y,r)&&s.push({element:a,datasetIndex:l,index:u})},!0),s}function Mw(t,e,n,r){let i=[];function s(o,a,l){const{startAngle:u,endAngle:d}=o.getProps(["startAngle","endAngle"],r),{angle:h}=Ip(o,{x:e.x,y:e.y});Do(h,u,d)&&i.push({element:o,datasetIndex:a,index:l})}return la(t,n,e,s),i}function Pw(t,e,n,r,i,s){let o=[];const a=Cw(n);let l=Number.POSITIVE_INFINITY;function u(d,h,f){const m=d.inRange(e.x,e.y,i);if(r&&!m)return;const v=d.getCenterPoint(i);if(!(!!s||t.isPointInArea(v))&&!m)return;const b=a(e,v);b<l?(o=[{element:d,datasetIndex:h,index:f}],l=b):b===l&&o.push({element:d,datasetIndex:h,index:f})}return la(t,n,e,u),o}function Qa(t,e,n,r,i,s){return!s&&!t.isPointInArea(e)?[]:n==="r"&&!r?Mw(t,e,n,i):Pw(t,e,n,r,i,s)}function gh(t,e,n,r,i){const s=[],o=n==="x"?"inXRange":"inYRange";let a=!1;return la(t,n,e,(l,u,d)=>{l[o]&&l[o](e[n],i)&&(s.push({element:l,datasetIndex:u,index:d}),a=a||l.inRange(e.x,e.y,i))}),r&&!a?[]:s}var Ew={modes:{index(t,e,n,r){const i=Ln(e,t),s=n.axis||"x",o=n.includeInvisible||!1,a=n.intersect?Xa(t,i,s,r,o):Qa(t,i,s,!1,r,o),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const d=a[0].index,h=u.data[d];h&&!h.skip&&l.push({element:h,datasetIndex:u.index,index:d})}),l):[]},dataset(t,e,n,r){const i=Ln(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;let a=n.intersect?Xa(t,i,s,r,o):Qa(t,i,s,!1,r,o);if(a.length>0){const l=a[0].datasetIndex,u=t.getDatasetMeta(l).data;a=[];for(let d=0;d<u.length;++d)a.push({element:u[d],datasetIndex:l,index:d})}return a},point(t,e,n,r){const i=Ln(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;return Xa(t,i,s,r,o)},nearest(t,e,n,r){const i=Ln(e,t),s=n.axis||"xy",o=n.includeInvisible||!1;return Qa(t,i,s,n.intersect,r,o)},x(t,e,n,r){const i=Ln(e,t);return gh(t,i,"x",n.intersect,r)},y(t,e,n,r){const i=Ln(e,t);return gh(t,i,"y",n.intersect,r)}}};const sg=["left","top","right","bottom"];function Zr(t,e){return t.filter(n=>n.pos===e)}function xh(t,e){return t.filter(n=>sg.indexOf(n.pos)===-1&&n.box.axis===e)}function Jr(t,e){return t.sort((n,r)=>{const i=e?r:n,s=e?n:r;return i.weight===s.weight?i.index-s.index:i.weight-s.weight})}function Tw(t){const e=[];let n,r,i,s,o,a;for(n=0,r=(t||[]).length;n<r;++n)i=t[n],{position:s,options:{stack:o,stackWeight:a=1}}=i,e.push({index:n,box:i,pos:s,horizontal:i.isHorizontal(),weight:i.weight,stack:o&&s+o,stackWeight:a});return e}function Rw(t){const e={};for(const n of t){const{stack:r,pos:i,stackWeight:s}=n;if(!r||!sg.includes(i))continue;const o=e[r]||(e[r]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=s}return e}function Ow(t,e){const n=Rw(t),{vBoxMaxWidth:r,hBoxMaxHeight:i}=e;let s,o,a;for(s=0,o=t.length;s<o;++s){a=t[s];const{fullSize:l}=a.box,u=n[a.stack],d=u&&a.stackWeight/u.weight;a.horizontal?(a.width=d?d*r:l&&e.availableWidth,a.height=i):(a.width=r,a.height=d?d*i:l&&e.availableHeight)}return n}function Dw(t){const e=Tw(t),n=Jr(e.filter(u=>u.box.fullSize),!0),r=Jr(Zr(e,"left"),!0),i=Jr(Zr(e,"right")),s=Jr(Zr(e,"top"),!0),o=Jr(Zr(e,"bottom")),a=xh(e,"x"),l=xh(e,"y");return{fullSize:n,leftAndTop:r.concat(s),rightAndBottom:i.concat(l).concat(o).concat(a),chartArea:Zr(e,"chartArea"),vertical:r.concat(i).concat(l),horizontal:s.concat(o).concat(a)}}function vh(t,e,n,r){return Math.max(t[n],e[n])+Math.max(t[r],e[r])}function og(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function Lw(t,e,n,r){const{pos:i,box:s}=n,o=t.maxPadding;if(!A(i)){n.size&&(t[i]-=n.size);const h=r[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?s.height:s.width),n.size=h.size/h.count,t[i]+=n.size}s.getPadding&&og(o,s.getPadding());const a=Math.max(0,e.outerWidth-vh(o,t,"left","right")),l=Math.max(0,e.outerHeight-vh(o,t,"top","bottom")),u=a!==t.w,d=l!==t.h;return t.w=a,t.h=l,n.horizontal?{same:u,other:d}:{same:d,other:u}}function $w(t){const e=t.maxPadding;function n(r){const i=Math.max(e[r]-t[r],0);return t[r]+=i,i}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function zw(t,e){const n=e.maxPadding;function r(i){const s={left:0,top:0,right:0,bottom:0};return i.forEach(o=>{s[o]=Math.max(e[o],n[o])}),s}return r(t?["left","right"]:["top","bottom"])}function li(t,e,n,r){const i=[];let s,o,a,l,u,d;for(s=0,o=t.length,u=0;s<o;++s){a=t[s],l=a.box,l.update(a.width||e.w,a.height||e.h,zw(a.horizontal,e));const{same:h,other:f}=Lw(e,n,a,r);u|=h&&i.length,d=d||f,l.fullSize||i.push(a)}return u&&li(i,e,n,r)||d}function Ms(t,e,n,r,i){t.top=n,t.left=e,t.right=e+r,t.bottom=n+i,t.width=r,t.height=i}function yh(t,e,n,r){const i=n.padding;let{x:s,y:o}=e;for(const a of t){const l=a.box,u=r[a.stack]||{placed:0,weight:1},d=a.stackWeight/u.weight||1;if(a.horizontal){const h=e.w*d,f=u.size||l.height;Vi(u.start)&&(o=u.start),l.fullSize?Ms(l,i.left,o,n.outerWidth-i.right-i.left,f):Ms(l,e.left+u.placed,o,h,f),u.start=o,u.placed+=h,o=l.bottom}else{const h=e.h*d,f=u.size||l.width;Vi(u.start)&&(s=u.start),l.fullSize?Ms(l,s,i.top,f,n.outerHeight-i.bottom-i.top):Ms(l,s,e.top+u.placed,f,h),u.start=s,u.placed+=h,s=l.right}}e.x=s,e.y=o}var rt={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,r){if(!t)return;const i=ct(t.options.layout.padding),s=Math.max(e-i.width,0),o=Math.max(n-i.height,0),a=Dw(t.boxes),l=a.vertical,u=a.horizontal;W(t.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const d=l.reduce((g,b)=>b.box.options&&b.box.options.display===!1?g:g+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:i,availableWidth:s,availableHeight:o,vBoxMaxWidth:s/2/d,hBoxMaxHeight:o/2}),f=Object.assign({},i);og(f,ct(r));const m=Object.assign({maxPadding:f,w:s,h:o,x:i.left,y:i.top},i),v=Ow(l.concat(u),h);li(a.fullSize,m,h,v),li(l,m,h,v),li(u,m,h,v)&&li(l,m,h,v),$w(m),yh(a.leftAndTop,m,h,v),m.x+=m.w,m.y+=m.h,yh(a.rightAndBottom,m,h,v),t.chartArea={left:m.left,top:m.top,right:m.left+m.w,bottom:m.top+m.h,height:m.h,width:m.w},W(a.chartArea,g=>{const b=g.box;Object.assign(b,t.chartArea),b.update(m.w,m.h,{left:0,top:0,right:0,bottom:0})})}};class ag{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,r){}removeEventListener(e,n,r){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,r,i){return n=Math.max(0,n||e.width),r=r||e.height,{width:n,height:Math.max(0,i?Math.floor(n/i):r)}}isAttached(e){return!0}updateConfig(e){}}class Fw extends ag{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Gs="$chartjs",Aw={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},bh=t=>t===null||t==="";function Iw(t,e){const n=t.style,r=t.getAttribute("height"),i=t.getAttribute("width");if(t[Gs]={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",bh(i)){const s=oh(t,"width");s!==void 0&&(t.width=s)}if(bh(r))if(t.style.height==="")t.height=t.width/(e||2);else{const s=oh(t,"height");s!==void 0&&(t.height=s)}return t}const lg=Kb?{passive:!0}:!1;function Uw(t,e,n){t&&t.addEventListener(e,n,lg)}function Hw(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,lg)}function Ww(t,e){const n=Aw[t.type]||t.type,{x:r,y:i}=Ln(t,e);return{type:n,chart:e,native:t,x:r!==void 0?r:null,y:i!==void 0?i:null}}function zo(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function Bw(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let o=!1;for(const a of s)o=o||zo(a.addedNodes,r),o=o&&!zo(a.removedNodes,r);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function Vw(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let o=!1;for(const a of s)o=o||zo(a.removedNodes,r),o=o&&!zo(a.addedNodes,r);o&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const Xi=new Map;let wh=0;function cg(){const t=window.devicePixelRatio;t!==wh&&(wh=t,Xi.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function Yw(t,e){Xi.size||window.addEventListener("resize",cg),Xi.set(t,e)}function Xw(t){Xi.delete(t),Xi.size||window.removeEventListener("resize",cg)}function Qw(t,e,n){const r=t.canvas,i=r&&fu(r);if(!i)return;const s=Bp((a,l)=>{const u=i.clientWidth;n(a,l),u<i.clientWidth&&n()},window),o=new ResizeObserver(a=>{const l=a[0],u=l.contentRect.width,d=l.contentRect.height;u===0&&d===0||s(u,d)});return o.observe(i),Yw(t,s),o}function Ka(t,e,n){n&&n.disconnect(),e==="resize"&&Xw(t)}function Kw(t,e,n){const r=t.canvas,i=Bp(s=>{t.ctx!==null&&n(Ww(s,t))},t);return Uw(r,e,i),i}class Gw extends ag{acquireContext(e,n){const r=e&&e.getContext&&e.getContext("2d");return r&&r.canvas===e?(Iw(e,n),r):null}releaseContext(e){const n=e.canvas;if(!n[Gs])return!1;const r=n[Gs].initial;["height","width"].forEach(s=>{const o=r[s];X(o)?n.removeAttribute(s):n.setAttribute(s,o)});const i=r.style||{};return Object.keys(i).forEach(s=>{n.style[s]=i[s]}),n.width=n.width,delete n[Gs],!0}addEventListener(e,n,r){this.removeEventListener(e,n);const i=e.$proxies||(e.$proxies={}),o={attach:Bw,detach:Vw,resize:Qw}[n]||Kw;i[n]=o(e,n,r)}removeEventListener(e,n){const r=e.$proxies||(e.$proxies={}),i=r[n];if(!i)return;({attach:Ka,detach:Ka,resize:Ka}[n]||Hw)(e,n,i),r[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,r,i){return Qb(e,n,r,i)}isAttached(e){const n=e&&fu(e);return!!(n&&n.isConnected)}}function qw(t){return!hu()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?Fw:Gw}class Vt{constructor(){L(this,"x");L(this,"y");L(this,"active",!1);L(this,"options");L(this,"$animations")}tooltipPosition(e){const{x:n,y:r}=this.getProps(["x","y"],e);return{x:n,y:r}}hasValue(){return Oo(this.x)&&Oo(this.y)}getProps(e,n){const r=this.$animations;if(!n||!r)return this;const i={};return e.forEach(s=>{i[s]=r[s]&&r[s].active()?r[s]._to:this[s]}),i}}L(Vt,"defaults",{}),L(Vt,"defaultRoutes");function Zw(t,e){const n=t.options.ticks,r=Jw(t),i=Math.min(n.maxTicksLimit||r,r),s=n.major.enabled?t2(e):[],o=s.length,a=s[0],l=s[o-1],u=[];if(o>i)return n2(e,u,s,o/i),u;const d=e2(s,e,i);if(o>0){let h,f;const m=o>1?Math.round((l-a)/(o-1)):null;for(Ps(e,u,d,X(m)?0:a-m,a),h=0,f=o-1;h<f;h++)Ps(e,u,d,s[h],s[h+1]);return Ps(e,u,d,l,X(m)?e.length:l+m),u}return Ps(e,u,d),u}function Jw(t){const e=t.options.offset,n=t._tickSize(),r=t._length/n+(e?0:1),i=t._maxLength/n;return Math.floor(Math.min(r,i))}function e2(t,e,n){const r=r2(t),i=e.length/n;if(!r)return Math.max(i,1);const s=ib(r);for(let o=0,a=s.length-1;o<a;o++){const l=s[o];if(l>i)return l}return Math.max(i,1)}function t2(t){const e=[];let n,r;for(n=0,r=t.length;n<r;n++)t[n].major&&e.push(n);return e}function n2(t,e,n,r){let i=0,s=n[0],o;for(r=Math.ceil(r),o=0;o<t.length;o++)o===s&&(e.push(t[o]),i++,s=n[i*r])}function Ps(t,e,n,r,i){const s=I(r,0),o=Math.min(I(i,t.length),t.length);let a=0,l,u,d;for(n=Math.ceil(n),i&&(l=i-r,n=l/Math.floor(l/n)),d=s;d<0;)a++,d=Math.round(s+a*n);for(u=Math.max(s,0);u<o;u++)u===d&&(e.push(t[u]),a++,d=Math.round(s+a*n))}function r2(t){const e=t.length;let n,r;if(e<2)return!1;for(r=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==r)return!1;return r}const i2=t=>t==="left"?"right":t==="right"?"left":t,Sh=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,_h=(t,e)=>Math.min(e||t,t);function kh(t,e){const n=[],r=t.length/e,i=t.length;let s=0;for(;s<i;s+=r)n.push(t[Math.floor(s)]);return n}function s2(t,e,n){const r=t.ticks.length,i=Math.min(e,r-1),s=t._startPixel,o=t._endPixel,a=1e-6;let l=t.getPixelForTick(i),u;if(!(n&&(r===1?u=Math.max(l-s,o-l):e===0?u=(t.getPixelForTick(1)-l)/2:u=(l-t.getPixelForTick(i-1))/2,l+=i<e?u:-u,l<s-a||l>o+a)))return l}function o2(t,e){W(t,n=>{const r=n.gc,i=r.length/2;let s;if(i>e){for(s=0;s<i;++s)delete n.data[r[s]];r.splice(0,i)}})}function ei(t){return t.drawTicks?t.tickLength:0}function Nh(t,e){if(!t.display)return 0;const n=Ce(t.font,e),r=ct(t.padding);return(me(t.text)?t.text.length:1)*n.lineHeight+r.height}function a2(t,e){return Ar(t,{scale:e,type:"scale"})}function l2(t,e,n){return Ar(t,{tick:n,index:e,type:"tick"})}function c2(t,e,n){let r=iu(t);return(n&&e!=="right"||!n&&e==="right")&&(r=i2(r)),r}function u2(t,e,n,r){const{top:i,left:s,bottom:o,right:a,chart:l}=t,{chartArea:u,scales:d}=l;let h=0,f,m,v;const g=o-i,b=a-s;if(t.isHorizontal()){if(m=Ne(r,s,a),A(n)){const p=Object.keys(n)[0],x=n[p];v=d[p].getPixelForValue(x)+g-e}else n==="center"?v=(u.bottom+u.top)/2+g-e:v=Sh(t,n,e);f=a-s}else{if(A(n)){const p=Object.keys(n)[0],x=n[p];m=d[p].getPixelForValue(x)-b+e}else n==="center"?m=(u.left+u.right)/2-b+e:m=Sh(t,n,e);v=Ne(r,o,i),h=n==="left"?-ge:ge}return{titleX:m,titleY:v,maxWidth:f,rotation:h}}class Ir extends Vt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:r,_suggestedMax:i}=this;return e=wt(e,Number.POSITIVE_INFINITY),n=wt(n,Number.NEGATIVE_INFINITY),r=wt(r,Number.POSITIVE_INFINITY),i=wt(i,Number.NEGATIVE_INFINITY),{min:wt(e,r),max:wt(n,i),minDefined:lt(e),maxDefined:lt(n)}}getMinMax(e){let{min:n,max:r,minDefined:i,maxDefined:s}=this.getUserBounds(),o;if(i&&s)return{min:n,max:r};const a=this.getMatchingVisibleMetas();for(let l=0,u=a.length;l<u;++l)o=a[l].controller.getMinMax(this,e),i||(n=Math.min(n,o.min)),s||(r=Math.max(r,o.max));return n=s&&n>r?r:n,r=i&&n>r?n:r,{min:wt(n,wt(r,n)),max:wt(r,wt(n,r))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){J(this.options.beforeUpdate,[this])}update(e,n,r){const{beginAtZero:i,grace:s,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=r=Object.assign({left:0,right:0,top:0,bottom:0},r),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+r.left+r.right:this.height+r.top+r.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Ob(this,s,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?kh(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=Zw(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,r;this.isHorizontal()?(n=this.left,r=this.right):(n=this.top,r=this.bottom,e=!e),this._startPixel=n,this._endPixel=r,this._reversePixels=e,this._length=r-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){J(this.options.afterUpdate,[this])}beforeSetDimensions(){J(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){J(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),J(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){J(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let r,i,s;for(r=0,i=e.length;r<i;r++)s=e[r],s.label=J(n.callback,[s.value,r,e],this)}afterTickToLabelConversion(){J(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){J(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,r=_h(this.ticks.length,e.ticks.maxTicksLimit),i=n.minRotation||0,s=n.maxRotation;let o=i,a,l,u;if(!this._isVisible()||!n.display||i>=s||r<=1||!this.isHorizontal()){this.labelRotation=i;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,m=He(this.chart.width-h,0,this.maxWidth);a=e.offset?this.maxWidth/r:m/(r-1),h+6>a&&(a=m/(r-(e.offset?.5:1)),l=this.maxHeight-ei(e.grid)-n.padding-Nh(e.title,this.chart.options.font),u=Math.sqrt(h*h+f*f),o=lb(Math.min(Math.asin(He((d.highest.height+6)/a,-1,1)),Math.asin(He(l/u,-1,1))-Math.asin(He(f/u,-1,1)))),o=Math.max(i,Math.min(s,o))),this.labelRotation=o}afterCalculateLabelRotation(){J(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){J(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:r,title:i,grid:s}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){const l=Nh(i,n.options.font);if(a?(e.width=this.maxWidth,e.height=ei(s)+l):(e.height=this.maxHeight,e.width=ei(s)+l),r.display&&this.ticks.length){const{first:u,last:d,widest:h,highest:f}=this._getLabelSizes(),m=r.padding*2,v=At(this.labelRotation),g=Math.cos(v),b=Math.sin(v);if(a){const p=r.mirror?0:b*h.width+g*f.height;e.height=Math.min(this.maxHeight,e.height+p+m)}else{const p=r.mirror?0:g*h.width+b*f.height;e.width=Math.min(this.maxWidth,e.width+p+m)}this._calculatePadding(u,d,b,g)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,r,i){const{ticks:{align:s,padding:o},position:a}=this.options,l=this.labelRotation!==0,u=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,m=0;l?u?(f=i*e.width,m=r*n.height):(f=r*e.height,m=i*n.width):s==="start"?m=n.width:s==="end"?f=e.width:s!=="inner"&&(f=e.width/2,m=n.width/2),this.paddingLeft=Math.max((f-d+o)*this.width/(this.width-d),0),this.paddingRight=Math.max((m-h+o)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;s==="start"?(d=0,h=e.height):s==="end"&&(d=n.height,h=0),this.paddingTop=d+o,this.paddingBottom=h+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){J(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,r;for(n=0,r=e.length;n<r;n++)X(e[n].label)&&(e.splice(n,1),r--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let r=this.ticks;n<r.length&&(r=kh(r,n)),this._labelSizes=e=this._computeLabelSizes(r,r.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,r){const{ctx:i,_longestTextCache:s}=this,o=[],a=[],l=Math.floor(n/_h(n,r));let u=0,d=0,h,f,m,v,g,b,p,x,y,w,S;for(h=0;h<n;h+=l){if(v=e[h].label,g=this._resolveTickFontOptions(h),i.font=b=g.string,p=s[b]=s[b]||{data:{},gc:[]},x=g.lineHeight,y=w=0,!X(v)&&!me(v))y=eh(i,p.data,p.gc,y,v),w=x;else if(me(v))for(f=0,m=v.length;f<m;++f)S=v[f],!X(S)&&!me(S)&&(y=eh(i,p.data,p.gc,y,S),w+=x);o.push(y),a.push(w),u=Math.max(y,u),d=Math.max(w,d)}o2(s,n);const N=o.indexOf(u),_=a.indexOf(d),j=E=>({width:o[E]||0,height:a[E]||0});return{first:j(0),last:j(n-1),widest:j(N),highest:j(_),widths:o,heights:a}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return ub(this._alignToPixels?Pn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const r=n[e];return r.$context||(r.$context=l2(this.getContext(),e,r))}return this.$context||(this.$context=a2(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=At(this.labelRotation),r=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),s=this._getLabelSizes(),o=e.autoSkipPadding||0,a=s?s.widest.width+o:0,l=s?s.highest.height+o:0;return this.isHorizontal()?l*r>a*i?a/r:l/i:l*i<a*r?l/r:a/i}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,r=this.chart,i=this.options,{grid:s,position:o,border:a}=i,l=s.offset,u=this.isHorizontal(),h=this.ticks.length+(l?1:0),f=ei(s),m=[],v=a.setContext(this.getContext()),g=v.display?v.width:0,b=g/2,p=function(Y){return Pn(r,Y,g)};let x,y,w,S,N,_,j,E,M,T,D,V;if(o==="top")x=p(this.bottom),_=this.bottom-f,E=x-b,T=p(e.top)+b,V=e.bottom;else if(o==="bottom")x=p(this.top),T=e.top,V=p(e.bottom)-b,_=x+b,E=this.top+f;else if(o==="left")x=p(this.right),N=this.right-f,j=x-b,M=p(e.left)+b,D=e.right;else if(o==="right")x=p(this.left),M=e.left,D=p(e.right)-b,N=x+b,j=this.left+f;else if(n==="x"){if(o==="center")x=p((e.top+e.bottom)/2+.5);else if(A(o)){const Y=Object.keys(o)[0],G=o[Y];x=p(this.chart.scales[Y].getPixelForValue(G))}T=e.top,V=e.bottom,_=x+b,E=_+f}else if(n==="y"){if(o==="center")x=p((e.left+e.right)/2);else if(A(o)){const Y=Object.keys(o)[0],G=o[Y];x=p(this.chart.scales[Y].getPixelForValue(G))}N=x-b,j=N-f,M=e.left,D=e.right}const Se=I(i.ticks.maxTicksLimit,h),U=Math.max(1,Math.ceil(h/Se));for(y=0;y<h;y+=U){const Y=this.getContext(y),G=s.setContext(Y),P=a.setContext(Y),O=G.lineWidth,$=G.color,Q=P.dash||[],q=P.dashOffset,yt=G.tickWidth,Ee=G.tickColor,Pt=G.tickBorderDash||[],Te=G.tickBorderDashOffset;w=s2(this,y,l),w!==void 0&&(S=Pn(r,w,O),u?N=j=M=D=S:_=E=T=V=S,m.push({tx1:N,ty1:_,tx2:j,ty2:E,x1:M,y1:T,x2:D,y2:V,width:O,color:$,borderDash:Q,borderDashOffset:q,tickWidth:yt,tickColor:Ee,tickBorderDash:Pt,tickBorderDashOffset:Te}))}return this._ticksLength=h,this._borderValue=x,m}_computeLabelItems(e){const n=this.axis,r=this.options,{position:i,ticks:s}=r,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:u,padding:d,mirror:h}=s,f=ei(r.grid),m=f+d,v=h?-d:m,g=-At(this.labelRotation),b=[];let p,x,y,w,S,N,_,j,E,M,T,D,V="middle";if(i==="top")N=this.bottom-v,_=this._getXAxisLabelAlignment();else if(i==="bottom")N=this.top+v,_=this._getXAxisLabelAlignment();else if(i==="left"){const U=this._getYAxisLabelAlignment(f);_=U.textAlign,S=U.x}else if(i==="right"){const U=this._getYAxisLabelAlignment(f);_=U.textAlign,S=U.x}else if(n==="x"){if(i==="center")N=(e.top+e.bottom)/2+m;else if(A(i)){const U=Object.keys(i)[0],Y=i[U];N=this.chart.scales[U].getPixelForValue(Y)+m}_=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")S=(e.left+e.right)/2-m;else if(A(i)){const U=Object.keys(i)[0],Y=i[U];S=this.chart.scales[U].getPixelForValue(Y)}_=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?V="top":l==="end"&&(V="bottom"));const Se=this._getLabelSizes();for(p=0,x=a.length;p<x;++p){y=a[p],w=y.label;const U=s.setContext(this.getContext(p));j=this.getPixelForTick(p)+s.labelOffset,E=this._resolveTickFontOptions(p),M=E.lineHeight,T=me(w)?w.length:1;const Y=T/2,G=U.color,P=U.textStrokeColor,O=U.textStrokeWidth;let $=_;o?(S=j,_==="inner"&&(p===x-1?$=this.options.reverse?"left":"right":p===0?$=this.options.reverse?"right":"left":$="center"),i==="top"?u==="near"||g!==0?D=-T*M+M/2:u==="center"?D=-Se.highest.height/2-Y*M+M:D=-Se.highest.height+M/2:u==="near"||g!==0?D=M/2:u==="center"?D=Se.highest.height/2-Y*M:D=Se.highest.height-T*M,h&&(D*=-1),g!==0&&!U.showLabelBackdrop&&(S+=M/2*Math.sin(g))):(N=j,D=(1-T)*M/2);let Q;if(U.showLabelBackdrop){const q=ct(U.backdropPadding),yt=Se.heights[p],Ee=Se.widths[p];let Pt=D-q.top,Te=0-q.left;switch(V){case"middle":Pt-=yt/2;break;case"bottom":Pt-=yt;break}switch(_){case"center":Te-=Ee/2;break;case"right":Te-=Ee;break;case"inner":p===x-1?Te-=Ee:p>0&&(Te-=Ee/2);break}Q={left:Te,top:Pt,width:Ee+q.width,height:yt+q.height,color:U.backdropColor}}b.push({label:w,font:E,textOffset:D,options:{rotation:g,color:G,strokeColor:P,strokeWidth:O,textAlign:$,textBaseline:V,translation:[S,N],backdrop:Q}})}return b}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-At(this.labelRotation))return e==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:r,mirror:i,padding:s}}=this.options,o=this._getLabelSizes(),a=e+s,l=o.widest.width;let u,d;return n==="left"?i?(d=this.right+s,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d+=l)):(d=this.right-a,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d=this.left)):n==="right"?i?(d=this.left+s,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d-=l)):(d=this.left+a,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d=this.right)):u="right",{textAlign:u,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:r,top:i,width:s,height:o}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(r,i,s,o),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(s=>s.value===e);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(e){const n=this.options.grid,r=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let s,o;const a=(l,u,d)=>{!d.width||!d.color||(r.save(),r.lineWidth=d.width,r.strokeStyle=d.color,r.setLineDash(d.borderDash||[]),r.lineDashOffset=d.borderDashOffset,r.beginPath(),r.moveTo(l.x,l.y),r.lineTo(u.x,u.y),r.stroke(),r.restore())};if(n.display)for(s=0,o=i.length;s<o;++s){const l=i[s];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:r,grid:i}}=this,s=r.setContext(this.getContext()),o=r.display?s.width:0;if(!o)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let u,d,h,f;this.isHorizontal()?(u=Pn(e,this.left,o)-o/2,d=Pn(e,this.right,a)+a/2,h=f=l):(h=Pn(e,this.top,o)-o/2,f=Pn(e,this.bottom,a)+a/2,u=d=l),n.save(),n.lineWidth=s.width,n.strokeStyle=s.color,n.beginPath(),n.moveTo(u,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const r=this.ctx,i=this._computeLabelArea();i&&ou(r,i);const s=this.getLabelItems(e);for(const o of s){const a=o.options,l=o.font,u=o.label,d=o.textOffset;Yi(r,u,0,d,l,a)}i&&au(r)}drawTitle(){const{ctx:e,options:{position:n,title:r,reverse:i}}=this;if(!r.display)return;const s=Ce(r.font),o=ct(r.padding),a=r.align;let l=s.lineHeight/2;n==="bottom"||n==="center"||A(n)?(l+=o.bottom,me(r.text)&&(l+=s.lineHeight*(r.text.length-1))):l+=o.top;const{titleX:u,titleY:d,maxWidth:h,rotation:f}=u2(this,l,n,a);Yi(e,r.text,0,0,s,{color:r.color,maxWidth:h,rotation:f,textAlign:c2(a,n,i),textBaseline:"middle",translation:[u,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,r=I(e.grid&&e.grid.z,-1),i=I(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Ir.prototype.draw?[{z:n,draw:s=>{this.draw(s)}}]:[{z:r,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",i=[];let s,o;for(s=0,o=n.length;s<o;++s){const a=n[s];a[r]===this.id&&(!e||a.type===e)&&i.push(a)}return i}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Ce(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Es{constructor(e,n,r){this.type=e,this.scope=n,this.override=r,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let r;f2(n)&&(r=this.register(n));const i=this.items,s=e.id,o=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+e);return s in i||(i[s]=e,d2(e,o,r),this.override&&ce.override(e.id,e.overrides)),o}get(e){return this.items[e]}unregister(e){const n=this.items,r=e.id,i=this.scope;r in n&&delete n[r],i&&r in ce[i]&&(delete ce[i][r],this.override&&delete Gn[r])}}function d2(t,e,n){const r=Bi(Object.create(null),[n?ce.get(n):{},ce.get(e),t.defaults]);ce.set(e,r),t.defaultRoutes&&h2(e,t.defaultRoutes),t.descriptors&&ce.describe(e,t.descriptors)}function h2(t,e){Object.keys(e).forEach(n=>{const r=n.split("."),i=r.pop(),s=[t].concat(r).join("."),o=e[n].split("."),a=o.pop(),l=o.join(".");ce.route(s,i,l,a)})}function f2(t){return"id"in t&&"defaults"in t}class m2{constructor(){this.controllers=new Es(Nr,"datasets",!0),this.elements=new Es(Vt,"elements"),this.plugins=new Es(Object,"plugins"),this.scales=new Es(Ir,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,r){[...n].forEach(i=>{const s=r||this._getRegistryForType(i);r||s.isForType(i)||s===this.plugins&&i.id?this._exec(e,s,i):W(i,o=>{const a=r||this._getRegistryForType(o);this._exec(e,a,o)})})}_exec(e,n,r){const i=nu(e);J(r["before"+i],[],r),n[e](r),J(r["after"+i],[],r)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const r=this._typedRegistries[n];if(r.isForType(e))return r}return this.plugins}_get(e,n,r){const i=n.get(e);if(i===void 0)throw new Error('"'+e+'" is not a registered '+r+".");return i}}var kt=new m2;class p2{constructor(){this._init=[]}notify(e,n,r,i){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const s=i?this._descriptors(e).filter(i):this._descriptors(e),o=this._notify(s,e,n,r);return n==="afterDestroy"&&(this._notify(s,e,"stop"),this._notify(this._init,e,"uninstall")),o}_notify(e,n,r,i){i=i||{};for(const s of e){const o=s.plugin,a=o[r],l=[n,i,s.options];if(J(a,l,o)===!1&&i.cancelable)return!1}return!0}invalidate(){X(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const r=e&&e.config,i=I(r.options&&r.options.plugins,{}),s=g2(r);return i===!1&&!n?[]:v2(e,s,i,n)}_notifyStateChanges(e){const n=this._oldCache||[],r=this._cache,i=(s,o)=>s.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,r),e,"stop"),this._notify(i(r,n),e,"start")}}function g2(t){const e={},n=[],r=Object.keys(kt.plugins.items);for(let s=0;s<r.length;s++)n.push(kt.getPlugin(r[s]));const i=t.plugins||[];for(let s=0;s<i.length;s++){const o=i[s];n.indexOf(o)===-1&&(n.push(o),e[o.id]=!0)}return{plugins:n,localIds:e}}function x2(t,e){return!e&&t===!1?null:t===!0?{}:t}function v2(t,{plugins:e,localIds:n},r,i){const s=[],o=t.getContext();for(const a of e){const l=a.id,u=x2(r[l],i);u!==null&&s.push({plugin:a,options:y2(t.config,{plugin:a,local:n[l]},u,o)})}return s}function y2(t,{plugin:e,local:n},r,i){const s=t.pluginScopeKeys(e),o=t.getOptionScopes(r,s);return n&&e.defaults&&o.push(e.defaults),t.createResolver(o,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Jl(t,e){const n=ce.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function b2(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function w2(t,e){return t===e?"_index_":"_value_"}function jh(t){if(t==="x"||t==="y"||t==="r")return t}function S2(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function ec(t,...e){if(jh(t))return t;for(const n of e){const r=n.axis||S2(n.position)||t.length>1&&jh(t[0].toLowerCase());if(r)return r}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Ch(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function _2(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(r=>r.xAxisID===t||r.yAxisID===t);if(n.length)return Ch(t,"x",n[0])||Ch(t,"y",n[0])}return{}}function k2(t,e){const n=Gn[t.type]||{scales:{}},r=e.scales||{},i=Jl(t.type,e),s=Object.create(null);return Object.keys(r).forEach(o=>{const a=r[o];if(!A(a))return console.error(`Invalid scale configuration for scale: ${o}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const l=ec(o,a,_2(o,t),ce.scales[a.type]),u=w2(l,i),d=n.scales||{};s[o]=bi(Object.create(null),[{axis:l},a,d[l],d[u]])}),t.data.datasets.forEach(o=>{const a=o.type||t.type,l=o.indexAxis||Jl(a,e),d=(Gn[a]||{}).scales||{};Object.keys(d).forEach(h=>{const f=b2(h,l),m=o[f+"AxisID"]||f;s[m]=s[m]||Object.create(null),bi(s[m],[{axis:f},r[m],d[h]])})}),Object.keys(s).forEach(o=>{const a=s[o];bi(a,[ce.scales[a.type],ce.scale])}),s}function ug(t){const e=t.options||(t.options={});e.plugins=I(e.plugins,{}),e.scales=k2(t,e)}function dg(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function N2(t){return t=t||{},t.data=dg(t.data),ug(t),t}const Mh=new Map,hg=new Set;function Ts(t,e){let n=Mh.get(t);return n||(n=e(),Mh.set(t,n),hg.add(n)),n}const ti=(t,e,n)=>{const r=Kn(e,n);r!==void 0&&t.add(r)};class j2{constructor(e){this._config=N2(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=dg(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),ug(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Ts(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return Ts(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return Ts(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,r=this.type;return Ts(`${r}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const r=this._scopeCache;let i=r.get(e);return(!i||n)&&(i=new Map,r.set(e,i)),i}getOptionScopes(e,n,r){const{options:i,type:s}=this,o=this._cachedScopes(e,r),a=o.get(n);if(a)return a;const l=new Set;n.forEach(d=>{e&&(l.add(e),d.forEach(h=>ti(l,e,h))),d.forEach(h=>ti(l,i,h)),d.forEach(h=>ti(l,Gn[s]||{},h)),d.forEach(h=>ti(l,ce,h)),d.forEach(h=>ti(l,Zl,h))});const u=Array.from(l);return u.length===0&&u.push(Object.create(null)),hg.has(n)&&o.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,Gn[n]||{},ce.datasets[n]||{},{type:n},ce,Zl]}resolveNamedOptions(e,n,r,i=[""]){const s={$shared:!0},{resolver:o,subPrefixes:a}=Ph(this._resolverCache,e,i);let l=o;if(M2(o,n)){s.$shared=!1,r=Sn(r)?r():r;const u=this.createResolver(e,r,a);l=Dr(o,r,u)}for(const u of n)s[u]=l[u];return s}createResolver(e,n,r=[""],i){const{resolver:s}=Ph(this._resolverCache,e,r);return A(n)?Dr(s,n,void 0,i):s}}function Ph(t,e,n){let r=t.get(e);r||(r=new Map,t.set(e,r));const i=n.join();let s=r.get(i);return s||(s={resolver:cu(e,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},r.set(i,s)),s}const C2=t=>A(t)&&Object.getOwnPropertyNames(t).some(e=>Sn(t[e]));function M2(t,e){const{isScriptable:n,isIndexable:r}=Gp(t);for(const i of e){const s=n(i),o=r(i),a=(o||s)&&t[i];if(s&&(Sn(a)||C2(a))||o&&me(a))return!0}return!1}var P2="4.4.9";const E2=["top","bottom","left","right","chartArea"];function Eh(t,e){return t==="top"||t==="bottom"||E2.indexOf(t)===-1&&e==="x"}function Th(t,e){return function(n,r){return n[t]===r[t]?n[e]-r[e]:n[t]-r[t]}}function Rh(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),J(n&&n.onComplete,[t],e)}function T2(t){const e=t.chart,n=e.options.animation;J(n&&n.onProgress,[t],e)}function fg(t){return hu()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const qs={},Oh=t=>{const e=fg(t);return Object.values(qs).filter(n=>n.canvas===e).pop()};function R2(t,e,n){const r=Object.keys(t);for(const i of r){const s=+i;if(s>=e){const o=t[i];delete t[i],(n>0||s>e)&&(t[s+n]=o)}}}function O2(t,e,n,r){return!n||t.type==="mouseout"?null:r?e:t}var Kt;let ns=(Kt=class{static register(...e){kt.add(...e),Dh()}static unregister(...e){kt.remove(...e),Dh()}constructor(e,n){const r=this.config=new j2(n),i=fg(e),s=Oh(i);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const o=r.createResolver(r.chartOptionScopes(),this.getContext());this.platform=new(r.platform||qw(i)),this.platform.updateConfig(r);const a=this.platform.acquireContext(i,o.aspectRatio),l=a&&a.canvas,u=l&&l.height,d=l&&l.width;if(this.id=G1(),this.ctx=a,this.canvas=l,this.width=d,this.height=u,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new p2,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=mb(h=>this.update(h),o.resizeDelay||0),this._dataChanges=[],qs[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Tt.listen(this,"complete",Rh),Tt.listen(this,"progress",T2),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:r,height:i,_aspectRatio:s}=this;return X(e)?n&&s?s:i?r/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return kt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():sh(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return th(this.canvas,this.ctx),this}stop(){return Tt.stop(this),this}resize(e,n){Tt.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const r=this.options,i=this.canvas,s=r.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(i,e,n,s),a=r.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,sh(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),J(r.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};W(n,(r,i)=>{r.id=i})}buildOrUpdateScales(){const e=this.options,n=e.scales,r=this.scales,i=Object.keys(r).reduce((o,a)=>(o[a]=!1,o),{});let s=[];n&&(s=s.concat(Object.keys(n).map(o=>{const a=n[o],l=ec(o,a),u=l==="r",d=l==="x";return{options:a,dposition:u?"chartArea":d?"bottom":"left",dtype:u?"radialLinear":d?"category":"linear"}}))),W(s,o=>{const a=o.options,l=a.id,u=ec(l,a),d=I(a.type,o.dtype);(a.position===void 0||Eh(a.position,u)!==Eh(o.dposition))&&(a.position=o.dposition),i[l]=!0;let h=null;if(l in r&&r[l].type===d)h=r[l];else{const f=kt.getScale(d);h=new f({id:l,type:d,ctx:this.ctx,chart:this}),r[h.id]=h}h.init(a,e)}),W(i,(o,a)=>{o||delete r[a]}),W(r,o=>{rt.configure(this,o,o.options),rt.addBox(this,o)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,r=e.length;if(e.sort((i,s)=>i.index-s.index),r>n){for(let i=n;i<r;++i)this._destroyDatasetMeta(i);e.splice(n,r-n)}this._sortedMetasets=e.slice(0).sort(Th("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((r,i)=>{n.filter(s=>s===r._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let r,i;for(this._removeUnreferencedMetasets(),r=0,i=n.length;r<i;r++){const s=n[r];let o=this.getDatasetMeta(r);const a=s.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(r),o=this.getDatasetMeta(r)),o.type=a,o.indexAxis=s.indexAxis||Jl(a,this.options),o.order=s.order||0,o.index=r,o.label=""+s.label,o.visible=this.isDatasetVisible(r),o.controller)o.controller.updateIndex(r),o.controller.linkScales();else{const l=kt.getController(a),{datasetElementType:u,dataElementType:d}=ce.datasets[a];Object.assign(l,{dataElementType:kt.getElement(d),datasetElementType:u&&kt.getElement(u)}),o.controller=new l(this,r),e.push(o.controller)}}return this._updateMetasets(),e}_resetElements(){W(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const r=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!r.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let u=0,d=this.data.datasets.length;u<d;u++){const{controller:h}=this.getDatasetMeta(u),f=!i&&s.indexOf(h)===-1;h.buildOrUpdateElements(f),o=Math.max(+h.getMaxOverflow(),o)}o=this._minPadding=r.layout.autoPadding?o:0,this._updateLayout(o),i||W(s,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Th("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){W(this.scales,e=>{rt.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),r=new Set(e.events);(!Vd(n,r)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:r,start:i,count:s}of n){const o=r==="_removeElements"?-s:s;R2(e,i,o)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,r=s=>new Set(e.filter(o=>o[0]===s).map((o,a)=>a+","+o.splice(1).join(","))),i=r(0);for(let s=1;s<n;s++)if(!Vd(i,r(s)))return;return Array.from(i).map(s=>s.split(",")).map(s=>({method:s[1],start:+s[2],count:+s[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;rt.update(this,this.width,this.height,e);const n=this.chartArea,r=n.width<=0||n.height<=0;this._layers=[],W(this.boxes,i=>{r&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,s)=>{i._idx=s}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,r=this.data.datasets.length;n<r;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,r=this.data.datasets.length;n<r;++n)this._updateDataset(n,Sn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const r=this.getDatasetMeta(e),i={meta:r,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(r.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Tt.has(this)?this.attached&&!Tt.running(this)&&Tt.start(this):(this.draw(),Rh({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:r,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(r,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,r=[];let i,s;for(i=0,s=n.length;i<s;++i){const o=n[i];(!e||o.visible)&&r.push(o)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,r={meta:e,index:e.index,cancelable:!0},i=Jb(this,e);this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(i&&ou(n,i),e.controller.draw(),i&&au(n),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(e){return Qp(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,r,i){const s=Ew.modes[n];return typeof s=="function"?s(this,e,r,i):[]}getDatasetMeta(e){const n=this.data.datasets[e],r=this._metasets;let i=r.filter(s=>s&&s._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},r.push(i)),i}getContext(){return this.$context||(this.$context=Ar(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const r=this.getDatasetMeta(e);return typeof r.hidden=="boolean"?!r.hidden:!n.hidden}setDatasetVisibility(e,n){const r=this.getDatasetMeta(e);r.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,r){const i=r?"show":"hide",s=this.getDatasetMeta(e),o=s.controller._resolveAnimations(void 0,i);Vi(n)?(s.data[n].hidden=!r,this.update()):(this.setDatasetVisibility(e,r),o.update(s,{visible:r}),this.update(a=>a.datasetIndex===e?i:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Tt.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),th(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete qs[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,r=(s,o)=>{n.addEventListener(this,s,o),e[s]=o},i=(s,o,a)=>{s.offsetX=o,s.offsetY=a,this._eventHandler(s)};W(this.options.events,s=>r(s,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,r=(l,u)=>{n.addEventListener(this,l,u),e[l]=u},i=(l,u)=>{e[l]&&(n.removeEventListener(this,l,u),delete e[l])},s=(l,u)=>{this.canvas&&this.resize(l,u)};let o;const a=()=>{i("attach",a),this.attached=!0,this.resize(),r("resize",s),r("detach",o)};o=()=>{this.attached=!1,i("resize",s),this._stop(),this._resize(0,0),r("attach",a)},n.isAttached(this.canvas)?a():o()}unbindEvents(){W(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},W(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,r){const i=r?"set":"remove";let s,o,a,l;for(n==="dataset"&&(s=this.getDatasetMeta(e[0].datasetIndex),s.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=e.length;a<l;++a){o=e[a];const u=o&&this.getDatasetMeta(o.datasetIndex).controller;u&&u[i+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],r=e.map(({datasetIndex:s,index:o})=>{const a=this.getDatasetMeta(s);if(!a)throw new Error("No dataset found at index "+s);return{datasetIndex:s,element:a.data[o],index:o}});!Eo(r,n)&&(this._active=r,this._lastEvent=null,this._updateHoverStyles(r,n))}notifyPlugins(e,n,r){return this._plugins.notify(this,e,n,r)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,r){const i=this.options.hover,s=(l,u)=>l.filter(d=>!u.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),o=s(n,e),a=r?e:s(e,n);o.length&&this.updateHoverStyle(o,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(e,n){const r={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},i=o=>(o.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",r,i)===!1)return;const s=this._handleEvent(e,n,r.inChartArea);return r.cancelable=!1,this.notifyPlugins("afterEvent",r,i),(s||r.changed)&&this.render(),this}_handleEvent(e,n,r){const{_active:i=[],options:s}=this,o=n,a=this._getActiveElements(e,i,r,o),l=nb(e),u=O2(e,this._lastEvent,r,l);r&&(this._lastEvent=null,J(s.onHover,[e,a,this],this),l&&J(s.onClick,[e,a,this],this));const d=!Eo(a,i);return(d||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=u,d}_getActiveElements(e,n,r,i){if(e.type==="mouseout")return[];if(!r)return n;const s=this.options.hover;return this.getElementsAtEventForMode(e,s.mode,s,i)}},L(Kt,"defaults",ce),L(Kt,"instances",qs),L(Kt,"overrides",Gn),L(Kt,"registry",kt),L(Kt,"version",P2),L(Kt,"getChart",Oh),Kt);function Dh(){return W(ns.instances,t=>t._plugins.invalidate())}function D2(t,e,n){const{startAngle:r,pixelMargin:i,x:s,y:o,outerRadius:a,innerRadius:l}=e;let u=i/a;t.beginPath(),t.arc(s,o,a,r-u,n+u),l>i?(u=i/l,t.arc(s,o,l,n+u,r-u,!0)):t.arc(s,o,i,n+ge,r-ge),t.closePath(),t.clip()}function L2(t){return lu(t,["outerStart","outerEnd","innerStart","innerEnd"])}function $2(t,e,n,r){const i=L2(t.options.borderRadius),s=(n-e)/2,o=Math.min(s,r*e/2),a=l=>{const u=(n-Math.min(s,l))*r/2;return He(l,0,Math.min(s,u))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:He(i.innerStart,0,o),innerEnd:He(i.innerEnd,0,o)}}function rr(t,e,n,r){return{x:n+t*Math.cos(e),y:r+t*Math.sin(e)}}function Fo(t,e,n,r,i,s){const{x:o,y:a,startAngle:l,pixelMargin:u,innerRadius:d}=e,h=Math.max(e.outerRadius+r+n-u,0),f=d>0?d+r+n+u:0;let m=0;const v=i-l;if(r){const U=d>0?d-r:0,Y=h>0?h-r:0,G=(U+Y)/2,P=G!==0?v*G/(G+r):v;m=(v-P)/2}const g=Math.max(.001,v*h-n/he)/h,b=(v-g)/2,p=l+b+m,x=i-b-m,{outerStart:y,outerEnd:w,innerStart:S,innerEnd:N}=$2(e,f,h,x-p),_=h-y,j=h-w,E=p+y/_,M=x-w/j,T=f+S,D=f+N,V=p+S/T,Se=x-N/D;if(t.beginPath(),s){const U=(E+M)/2;if(t.arc(o,a,h,E,U),t.arc(o,a,h,U,M),w>0){const O=rr(j,M,o,a);t.arc(O.x,O.y,w,M,x+ge)}const Y=rr(D,x,o,a);if(t.lineTo(Y.x,Y.y),N>0){const O=rr(D,Se,o,a);t.arc(O.x,O.y,N,x+ge,Se+Math.PI)}const G=(x-N/f+(p+S/f))/2;if(t.arc(o,a,f,x-N/f,G,!0),t.arc(o,a,f,G,p+S/f,!0),S>0){const O=rr(T,V,o,a);t.arc(O.x,O.y,S,V+Math.PI,p-ge)}const P=rr(_,p,o,a);if(t.lineTo(P.x,P.y),y>0){const O=rr(_,E,o,a);t.arc(O.x,O.y,y,p-ge,E)}}else{t.moveTo(o,a);const U=Math.cos(E)*h+o,Y=Math.sin(E)*h+a;t.lineTo(U,Y);const G=Math.cos(M)*h+o,P=Math.sin(M)*h+a;t.lineTo(G,P)}t.closePath()}function z2(t,e,n,r,i){const{fullCircles:s,startAngle:o,circumference:a}=e;let l=e.endAngle;if(s){Fo(t,e,n,r,l,i);for(let u=0;u<s;++u)t.fill();isNaN(a)||(l=o+(a%de||de))}return Fo(t,e,n,r,l,i),t.fill(),l}function F2(t,e,n,r,i){const{fullCircles:s,startAngle:o,circumference:a,options:l}=e,{borderWidth:u,borderJoinStyle:d,borderDash:h,borderDashOffset:f}=l,m=l.borderAlign==="inner";if(!u)return;t.setLineDash(h||[]),t.lineDashOffset=f,m?(t.lineWidth=u*2,t.lineJoin=d||"round"):(t.lineWidth=u,t.lineJoin=d||"bevel");let v=e.endAngle;if(s){Fo(t,e,n,r,v,i);for(let g=0;g<s;++g)t.stroke();isNaN(a)||(v=o+(a%de||de))}m&&D2(t,e,v),s||(Fo(t,e,n,r,v,i),t.stroke())}class ci extends Vt{constructor(n){super();L(this,"circumference");L(this,"endAngle");L(this,"fullCircles");L(this,"innerRadius");L(this,"outerRadius");L(this,"pixelMargin");L(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,r,i){const s=this.getProps(["x","y"],i),{angle:o,distance:a}=Ip(s,{x:n,y:r}),{startAngle:l,endAngle:u,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),m=(this.options.spacing+this.options.borderWidth)/2,v=I(f,u-l),g=Do(o,l,u)&&l!==u,b=v>=de||g,p=An(a,d+m,h+m);return b&&p}getCenterPoint(n){const{x:r,y:i,startAngle:s,endAngle:o,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:d}=this.options,h=(s+o)/2,f=(a+l+d+u)/2;return{x:r+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:r,circumference:i}=this,s=(r.offset||0)/4,o=(r.spacing||0)/2,a=r.circular;if(this.pixelMargin=r.borderAlign==="inner"?.33:0,this.fullCircles=i>de?Math.floor(i/de):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*s,Math.sin(l)*s);const u=1-Math.sin(Math.min(he,i||0)),d=s*u;n.fillStyle=r.backgroundColor,n.strokeStyle=r.borderColor,z2(n,this,d,o,a),F2(n,this,d,o,a),n.restore()}}L(ci,"id","arc"),L(ci,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),L(ci,"defaultRoutes",{backgroundColor:"backgroundColor"}),L(ci,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function mg(t,e){const{x:n,y:r,base:i,width:s,height:o}=t.getProps(["x","y","base","width","height"],e);let a,l,u,d,h;return t.horizontal?(h=o/2,a=Math.min(n,i),l=Math.max(n,i),u=r-h,d=r+h):(h=s/2,a=n-h,l=n+h,u=Math.min(r,i),d=Math.max(r,i)),{left:a,top:u,right:l,bottom:d}}function on(t,e,n,r){return t?0:He(e,n,r)}function A2(t,e,n){const r=t.options.borderWidth,i=t.borderSkipped,s=Kp(r);return{t:on(i.top,s.top,0,n),r:on(i.right,s.right,0,e),b:on(i.bottom,s.bottom,0,n),l:on(i.left,s.left,0,e)}}function I2(t,e,n){const{enableBorderRadius:r}=t.getProps(["enableBorderRadius"]),i=t.options.borderRadius,s=_r(i),o=Math.min(e,n),a=t.borderSkipped,l=r||A(i);return{topLeft:on(!l||a.top||a.left,s.topLeft,0,o),topRight:on(!l||a.top||a.right,s.topRight,0,o),bottomLeft:on(!l||a.bottom||a.left,s.bottomLeft,0,o),bottomRight:on(!l||a.bottom||a.right,s.bottomRight,0,o)}}function U2(t){const e=mg(t),n=e.right-e.left,r=e.bottom-e.top,i=A2(t,n/2,r/2),s=I2(t,n/2,r/2);return{outer:{x:e.left,y:e.top,w:n,h:r,radius:s},inner:{x:e.left+i.l,y:e.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,s.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(i.b,i.r))}}}}function Ga(t,e,n,r){const i=e===null,s=n===null,a=t&&!(i&&s)&&mg(t,r);return a&&(i||An(e,a.left,a.right))&&(s||An(n,a.top,a.bottom))}function H2(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function W2(t,e){t.rect(e.x,e.y,e.w,e.h)}function qa(t,e,n={}){const r=t.x!==n.x?-e:0,i=t.y!==n.y?-e:0,s=(t.x+t.w!==n.x+n.w?e:0)-r,o=(t.y+t.h!==n.y+n.h?e:0)-i;return{x:t.x+r,y:t.y+i,w:t.w+s,h:t.h+o,radius:t.radius}}class Zs extends Vt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:r,backgroundColor:i}}=this,{inner:s,outer:o}=U2(this),a=H2(o.radius)?Lo:W2;e.save(),(o.w!==s.w||o.h!==s.h)&&(e.beginPath(),a(e,qa(o,n,s)),e.clip(),a(e,qa(s,-n,o)),e.fillStyle=r,e.fill("evenodd")),e.beginPath(),a(e,qa(s,n)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,n,r){return Ga(this,e,n,r)}inXRange(e,n){return Ga(this,e,null,n)}inYRange(e,n){return Ga(this,null,e,n)}getCenterPoint(e){const{x:n,y:r,base:i,horizontal:s}=this.getProps(["x","y","base","horizontal"],e);return{x:s?(n+i)/2:n,y:s?r:(r+i)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}L(Zs,"id","bar"),L(Zs,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),L(Zs,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const Lh=(t,e)=>{let{boxHeight:n=e,boxWidth:r=e}=t;return t.usePointStyle&&(n=Math.min(n,e),r=t.pointStyleWidth||Math.min(r,e)),{boxWidth:r,boxHeight:n,itemHeight:Math.max(e,n)}},B2=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class $h extends Vt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,r){this.maxWidth=e,this.maxHeight=n,this._margins=r,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=J(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(r=>e.filter(r,this.chart.data))),e.sort&&(n=n.sort((r,i)=>e.sort(r,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const r=e.labels,i=Ce(r.font),s=i.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Lh(r,s);let u,d;n.font=i.string,this.isHorizontal()?(u=this.maxWidth,d=this._fitRows(o,s,a,l)+10):(d=this.maxHeight,u=this._fitCols(o,i,a,l)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,r,i){const{ctx:s,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.lineWidths=[0],d=i+a;let h=e;s.textAlign="left",s.textBaseline="middle";let f=-1,m=-d;return this.legendItems.forEach((v,g)=>{const b=r+n/2+s.measureText(v.text).width;(g===0||u[u.length-1]+b+2*a>o)&&(h+=d,u[u.length-(g>0?0:1)]=0,m+=d,f++),l[g]={left:0,top:m,row:f,width:b,height:i},u[u.length-1]+=b+a}),h}_fitCols(e,n,r,i){const{ctx:s,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],u=this.columnSizes=[],d=o-e;let h=a,f=0,m=0,v=0,g=0;return this.legendItems.forEach((b,p)=>{const{itemWidth:x,itemHeight:y}=V2(r,n,s,b,i);p>0&&m+y+2*a>d&&(h+=f+a,u.push({width:f,height:m}),v+=f+a,g++,f=m=0),l[p]={left:v,top:m,col:g,width:x,height:y},f=Math.max(f,x),m+=y+a}),h+=f,u.push({width:f,height:m}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:r,labels:{padding:i},rtl:s}}=this,o=kr(s,this.left,this.width);if(this.isHorizontal()){let a=0,l=Ne(r,this.left+i,this.right-this.lineWidths[a]);for(const u of n)a!==u.row&&(a=u.row,l=Ne(r,this.left+i,this.right-this.lineWidths[a])),u.top+=this.top+e+i,u.left=o.leftForLtr(o.x(l),u.width),l+=u.width+i}else{let a=0,l=Ne(r,this.top+e+i,this.bottom-this.columnSizes[a].height);for(const u of n)u.col!==a&&(a=u.col,l=Ne(r,this.top+e+i,this.bottom-this.columnSizes[a].height)),u.top=l,u.left+=this.left+i,u.left=o.leftForLtr(o.x(u.left),u.width),l+=u.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;ou(e,this),this._draw(),au(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:r,ctx:i}=this,{align:s,labels:o}=e,a=ce.color,l=kr(e.rtl,this.left,this.width),u=Ce(o.font),{padding:d}=o,h=u.size,f=h/2;let m;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=u.string;const{boxWidth:v,boxHeight:g,itemHeight:b}=Lh(o,h),p=function(N,_,j){if(isNaN(v)||v<=0||isNaN(g)||g<0)return;i.save();const E=I(j.lineWidth,1);if(i.fillStyle=I(j.fillStyle,a),i.lineCap=I(j.lineCap,"butt"),i.lineDashOffset=I(j.lineDashOffset,0),i.lineJoin=I(j.lineJoin,"miter"),i.lineWidth=E,i.strokeStyle=I(j.strokeStyle,a),i.setLineDash(I(j.lineDash,[])),o.usePointStyle){const M={radius:g*Math.SQRT2/2,pointStyle:j.pointStyle,rotation:j.rotation,borderWidth:E},T=l.xPlus(N,v/2),D=_+f;Xp(i,M,T,D,o.pointStyleWidth&&v)}else{const M=_+Math.max((h-g)/2,0),T=l.leftForLtr(N,v),D=_r(j.borderRadius);i.beginPath(),Object.values(D).some(V=>V!==0)?Lo(i,{x:T,y:M,w:v,h:g,radius:D}):i.rect(T,M,v,g),i.fill(),E!==0&&i.stroke()}i.restore()},x=function(N,_,j){Yi(i,j.text,N,_+b/2,u,{strikethrough:j.hidden,textAlign:l.textAlign(j.textAlign)})},y=this.isHorizontal(),w=this._computeTitleHeight();y?m={x:Ne(s,this.left+d,this.right-r[0]),y:this.top+d+w,line:0}:m={x:this.left+d,y:Ne(s,this.top+w+d,this.bottom-n[0].height),line:0},eg(this.ctx,e.textDirection);const S=b+d;this.legendItems.forEach((N,_)=>{i.strokeStyle=N.fontColor,i.fillStyle=N.fontColor;const j=i.measureText(N.text).width,E=l.textAlign(N.textAlign||(N.textAlign=o.textAlign)),M=v+f+j;let T=m.x,D=m.y;l.setWidth(this.width),y?_>0&&T+M+d>this.right&&(D=m.y+=S,m.line++,T=m.x=Ne(s,this.left+d,this.right-r[m.line])):_>0&&D+S>this.bottom&&(T=m.x=T+n[m.line].width+d,m.line++,D=m.y=Ne(s,this.top+w+d,this.bottom-n[m.line].height));const V=l.x(T);if(p(V,D,N),T=pb(E,T+v+f,y?T+M:this.right,e.rtl),x(l.x(T),D,N),y)m.x+=M+d;else if(typeof N.text!="string"){const Se=u.lineHeight;m.y+=pg(N,Se)+d}else m.y+=S}),tg(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,r=Ce(n.font),i=ct(n.padding);if(!n.display)return;const s=kr(e.rtl,this.left,this.width),o=this.ctx,a=n.position,l=r.size/2,u=i.top+l;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+u,h=Ne(e.align,h,this.right-f);else{const v=this.columnSizes.reduce((g,b)=>Math.max(g,b.height),0);d=u+Ne(e.align,this.top,this.bottom-v-e.labels.padding-this._computeTitleHeight())}const m=Ne(a,h,h+f);o.textAlign=s.textAlign(iu(a)),o.textBaseline="middle",o.strokeStyle=n.color,o.fillStyle=n.color,o.font=r.string,Yi(o,n.text,m,d,r)}_computeTitleHeight(){const e=this.options.title,n=Ce(e.font),r=ct(e.padding);return e.display?n.lineHeight+r.height:0}_getLegendItemAt(e,n){let r,i,s;if(An(e,this.left,this.right)&&An(n,this.top,this.bottom)){for(s=this.legendHitBoxes,r=0;r<s.length;++r)if(i=s[r],An(e,i.left,i.left+i.width)&&An(n,i.top,i.top+i.height))return this.legendItems[r]}return null}handleEvent(e){const n=this.options;if(!Q2(e.type,n))return;const r=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const i=this._hoveredItem,s=B2(i,r);i&&!s&&J(n.onLeave,[e,i,this],this),this._hoveredItem=r,r&&!s&&J(n.onHover,[e,r,this],this)}else r&&J(n.onClick,[e,r,this],this)}}function V2(t,e,n,r,i){const s=Y2(r,t,e,n),o=X2(i,r,e.lineHeight);return{itemWidth:s,itemHeight:o}}function Y2(t,e,n,r){let i=t.text;return i&&typeof i!="string"&&(i=i.reduce((s,o)=>s.length>o.length?s:o)),e+n.size/2+r.measureText(i).width}function X2(t,e,n){let r=t;return typeof e.text!="string"&&(r=pg(e,n)),r}function pg(t,e){const n=t.text?t.text.length:0;return e*n}function Q2(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var gg={id:"legend",_element:$h,start(t,e,n){const r=t.legend=new $h({ctx:t.ctx,options:n,chart:t});rt.configure(t,r,n),rt.addBox(t,r)},stop(t){rt.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const r=t.legend;rt.configure(t,r,n),r.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const r=e.datasetIndex,i=n.chart;i.isDatasetVisible(r)?(i.hide(r),e.hidden=!0):(i.show(r),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:r,textAlign:i,color:s,useBorderRadius:o,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const u=l.controller.getStyle(n?0:void 0),d=ct(u.borderWidth);return{text:e[l.index].label,fillStyle:u.backgroundColor,fontColor:s,hidden:!l.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:u.borderColor,pointStyle:r||u.pointStyle,rotation:u.rotation,textAlign:i||u.textAlign,borderRadius:o&&(a||u.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class xg extends Vt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const r=this.options;if(this.left=0,this.top=0,!r.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const i=me(r.text)?r.text.length:1;this._padding=ct(r.padding);const s=i*Ce(r.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:r,bottom:i,right:s,options:o}=this,a=o.align;let l=0,u,d,h;return this.isHorizontal()?(d=Ne(a,r,s),h=n+e,u=s-r):(o.position==="left"?(d=r+e,h=Ne(a,i,n),l=he*-.5):(d=s-e,h=Ne(a,n,i),l=he*.5),u=i-n),{titleX:d,titleY:h,maxWidth:u,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const r=Ce(n.font),s=r.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:u}=this._drawArgs(s);Yi(e,n.text,0,0,r,{color:n.color,maxWidth:l,rotation:u,textAlign:iu(n.align),textBaseline:"middle",translation:[o,a]})}}function K2(t,e){const n=new xg({ctx:t.ctx,options:e,chart:t});rt.configure(t,n,e),rt.addBox(t,n),t.titleBlock=n}var G2={id:"title",_element:xg,start(t,e,n){K2(t,n)},stop(t){const e=t.titleBlock;rt.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const r=t.titleBlock;rt.configure(t,r,n),r.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ui={average(t){if(!t.length)return!1;let e,n,r=new Set,i=0,s=0;for(e=0,n=t.length;e<n;++e){const a=t[e].element;if(a&&a.hasValue()){const l=a.tooltipPosition();r.add(l.x),i+=l.y,++s}}return s===0||r.size===0?!1:{x:[...r].reduce((a,l)=>a+l)/r.size,y:i/s}},nearest(t,e){if(!t.length)return!1;let n=e.x,r=e.y,i=Number.POSITIVE_INFINITY,s,o,a;for(s=0,o=t.length;s<o;++s){const l=t[s].element;if(l&&l.hasValue()){const u=l.getCenterPoint(),d=cb(e,u);d<i&&(i=d,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,r=l.y}return{x:n,y:r}}};function St(t,e){return e&&(me(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Rt(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function q2(t,e){const{element:n,datasetIndex:r,index:i}=e,s=t.getDatasetMeta(r).controller,{label:o,value:a}=s.getLabelAndValue(i);return{chart:t,label:o,parsed:s.getParsed(i),raw:t.data.datasets[r].data[i],formattedValue:a,dataset:s.getDataset(),dataIndex:i,datasetIndex:r,element:n}}function zh(t,e){const n=t.chart.ctx,{body:r,footer:i,title:s}=t,{boxWidth:o,boxHeight:a}=e,l=Ce(e.bodyFont),u=Ce(e.titleFont),d=Ce(e.footerFont),h=s.length,f=i.length,m=r.length,v=ct(e.padding);let g=v.height,b=0,p=r.reduce((w,S)=>w+S.before.length+S.lines.length+S.after.length,0);if(p+=t.beforeBody.length+t.afterBody.length,h&&(g+=h*u.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),p){const w=e.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=m*w+(p-m)*l.lineHeight+(p-1)*e.bodySpacing}f&&(g+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let x=0;const y=function(w){b=Math.max(b,n.measureText(w).width+x)};return n.save(),n.font=u.string,W(t.title,y),n.font=l.string,W(t.beforeBody.concat(t.afterBody),y),x=e.displayColors?o+2+e.boxPadding:0,W(r,w=>{W(w.before,y),W(w.lines,y),W(w.after,y)}),x=0,n.font=d.string,W(t.footer,y),n.restore(),b+=v.width,{width:b,height:g}}function Z2(t,e){const{y:n,height:r}=e;return n<r/2?"top":n>t.height-r/2?"bottom":"center"}function J2(t,e,n,r){const{x:i,width:s}=r,o=n.caretSize+n.caretPadding;if(t==="left"&&i+s+o>e.width||t==="right"&&i-s-o<0)return!0}function eS(t,e,n,r){const{x:i,width:s}=n,{width:o,chartArea:{left:a,right:l}}=t;let u="center";return r==="center"?u=i<=(a+l)/2?"left":"right":i<=s/2?u="left":i>=o-s/2&&(u="right"),J2(u,t,e,n)&&(u="center"),u}function Fh(t,e,n){const r=n.yAlign||e.yAlign||Z2(t,n);return{xAlign:n.xAlign||e.xAlign||eS(t,e,n,r),yAlign:r}}function tS(t,e){let{x:n,width:r}=t;return e==="right"?n-=r:e==="center"&&(n-=r/2),n}function nS(t,e,n){let{y:r,height:i}=t;return e==="top"?r+=n:e==="bottom"?r-=i+n:r-=i/2,r}function Ah(t,e,n,r){const{caretSize:i,caretPadding:s,cornerRadius:o}=t,{xAlign:a,yAlign:l}=n,u=i+s,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:m}=_r(o);let v=tS(e,a);const g=nS(e,l,u);return l==="center"?a==="left"?v+=u:a==="right"&&(v-=u):a==="left"?v-=Math.max(d,f)+i:a==="right"&&(v+=Math.max(h,m)+i),{x:He(v,0,r.width-e.width),y:He(g,0,r.height-e.height)}}function Rs(t,e,n){const r=ct(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-r.right:t.x+r.left}function Ih(t){return St([],Rt(t))}function rS(t,e,n){return Ar(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function Uh(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const vg={beforeTitle:Et,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,r=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(r>0&&e.dataIndex<r)return n[e.dataIndex]}return""},afterTitle:Et,beforeBody:Et,beforeLabel:Et,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return X(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Et,afterBody:Et,beforeFooter:Et,footer:Et,afterFooter:Et};function ze(t,e,n,r){const i=t[e].call(n,r);return typeof i>"u"?vg[e].call(n,r):i}class tc extends Vt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,r=this.options.setContext(this.getContext()),i=r.enabled&&n.options.animation&&r.animations,s=new ng(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=rS(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:r}=n,i=ze(r,"beforeTitle",this,e),s=ze(r,"title",this,e),o=ze(r,"afterTitle",this,e);let a=[];return a=St(a,Rt(i)),a=St(a,Rt(s)),a=St(a,Rt(o)),a}getBeforeBody(e,n){return Ih(ze(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:r}=n,i=[];return W(e,s=>{const o={before:[],lines:[],after:[]},a=Uh(r,s);St(o.before,Rt(ze(a,"beforeLabel",this,s))),St(o.lines,ze(a,"label",this,s)),St(o.after,Rt(ze(a,"afterLabel",this,s))),i.push(o)}),i}getAfterBody(e,n){return Ih(ze(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:r}=n,i=ze(r,"beforeFooter",this,e),s=ze(r,"footer",this,e),o=ze(r,"afterFooter",this,e);let a=[];return a=St(a,Rt(i)),a=St(a,Rt(s)),a=St(a,Rt(o)),a}_createItems(e){const n=this._active,r=this.chart.data,i=[],s=[],o=[];let a=[],l,u;for(l=0,u=n.length;l<u;++l)a.push(q2(this.chart,n[l]));return e.filter&&(a=a.filter((d,h,f)=>e.filter(d,h,f,r))),e.itemSort&&(a=a.sort((d,h)=>e.itemSort(d,h,r))),W(a,d=>{const h=Uh(e.callbacks,d);i.push(ze(h,"labelColor",this,d)),s.push(ze(h,"labelPointStyle",this,d)),o.push(ze(h,"labelTextColor",this,d))}),this.labelColors=i,this.labelPointStyles=s,this.labelTextColors=o,this.dataPoints=a,a}update(e,n){const r=this.options.setContext(this.getContext()),i=this._active;let s,o=[];if(!i.length)this.opacity!==0&&(s={opacity:0});else{const a=ui[r.position].call(this,i,this._eventPosition);o=this._createItems(r),this.title=this.getTitle(o,r),this.beforeBody=this.getBeforeBody(o,r),this.body=this.getBody(o,r),this.afterBody=this.getAfterBody(o,r),this.footer=this.getFooter(o,r);const l=this._size=zh(this,r),u=Object.assign({},a,l),d=Fh(this.chart,r,u),h=Ah(r,u,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,s={opacity:1,x:h.x,y:h.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,s&&this._resolveAnimations().update(this,s),e&&r.external&&r.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,r,i){const s=this.getCaretPosition(e,r,i);n.lineTo(s.x1,s.y1),n.lineTo(s.x2,s.y2),n.lineTo(s.x3,s.y3)}getCaretPosition(e,n,r){const{xAlign:i,yAlign:s}=this,{caretSize:o,cornerRadius:a}=r,{topLeft:l,topRight:u,bottomLeft:d,bottomRight:h}=_r(a),{x:f,y:m}=e,{width:v,height:g}=n;let b,p,x,y,w,S;return s==="center"?(w=m+g/2,i==="left"?(b=f,p=b-o,y=w+o,S=w-o):(b=f+v,p=b+o,y=w-o,S=w+o),x=b):(i==="left"?p=f+Math.max(l,d)+o:i==="right"?p=f+v-Math.max(u,h)-o:p=this.caretX,s==="top"?(y=m,w=y-o,b=p-o,x=p+o):(y=m+g,w=y+o,b=p+o,x=p-o),S=y),{x1:b,x2:p,x3:x,y1:y,y2:w,y3:S}}drawTitle(e,n,r){const i=this.title,s=i.length;let o,a,l;if(s){const u=kr(r.rtl,this.x,this.width);for(e.x=Rs(this,r.titleAlign,r),n.textAlign=u.textAlign(r.titleAlign),n.textBaseline="middle",o=Ce(r.titleFont),a=r.titleSpacing,n.fillStyle=r.titleColor,n.font=o.string,l=0;l<s;++l)n.fillText(i[l],u.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+a,l+1===s&&(e.y+=r.titleMarginBottom-a)}}_drawColorBox(e,n,r,i,s){const o=this.labelColors[r],a=this.labelPointStyles[r],{boxHeight:l,boxWidth:u}=s,d=Ce(s.bodyFont),h=Rs(this,"left",s),f=i.x(h),m=l<d.lineHeight?(d.lineHeight-l)/2:0,v=n.y+m;if(s.usePointStyle){const g={radius:Math.min(u,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},b=i.leftForLtr(f,u)+u/2,p=v+l/2;e.strokeStyle=s.multiKeyBackground,e.fillStyle=s.multiKeyBackground,nh(e,g,b,p),e.strokeStyle=o.borderColor,e.fillStyle=o.backgroundColor,nh(e,g,b,p)}else{e.lineWidth=A(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,e.strokeStyle=o.borderColor,e.setLineDash(o.borderDash||[]),e.lineDashOffset=o.borderDashOffset||0;const g=i.leftForLtr(f,u),b=i.leftForLtr(i.xPlus(f,1),u-2),p=_r(o.borderRadius);Object.values(p).some(x=>x!==0)?(e.beginPath(),e.fillStyle=s.multiKeyBackground,Lo(e,{x:g,y:v,w:u,h:l,radius:p}),e.fill(),e.stroke(),e.fillStyle=o.backgroundColor,e.beginPath(),Lo(e,{x:b,y:v+1,w:u-2,h:l-2,radius:p}),e.fill()):(e.fillStyle=s.multiKeyBackground,e.fillRect(g,v,u,l),e.strokeRect(g,v,u,l),e.fillStyle=o.backgroundColor,e.fillRect(b,v+1,u-2,l-2))}e.fillStyle=this.labelTextColors[r]}drawBody(e,n,r){const{body:i}=this,{bodySpacing:s,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:u,boxPadding:d}=r,h=Ce(r.bodyFont);let f=h.lineHeight,m=0;const v=kr(r.rtl,this.x,this.width),g=function(j){n.fillText(j,v.x(e.x+m),e.y+f/2),e.y+=f+s},b=v.textAlign(o);let p,x,y,w,S,N,_;for(n.textAlign=o,n.textBaseline="middle",n.font=h.string,e.x=Rs(this,b,r),n.fillStyle=r.bodyColor,W(this.beforeBody,g),m=a&&b!=="right"?o==="center"?u/2+d:u+2+d:0,w=0,N=i.length;w<N;++w){for(p=i[w],x=this.labelTextColors[w],n.fillStyle=x,W(p.before,g),y=p.lines,a&&y.length&&(this._drawColorBox(n,e,w,v,r),f=Math.max(h.lineHeight,l)),S=0,_=y.length;S<_;++S)g(y[S]),f=h.lineHeight;W(p.after,g)}m=0,f=h.lineHeight,W(this.afterBody,g),e.y-=s}drawFooter(e,n,r){const i=this.footer,s=i.length;let o,a;if(s){const l=kr(r.rtl,this.x,this.width);for(e.x=Rs(this,r.footerAlign,r),e.y+=r.footerMarginTop,n.textAlign=l.textAlign(r.footerAlign),n.textBaseline="middle",o=Ce(r.footerFont),n.fillStyle=r.footerColor,n.font=o.string,a=0;a<s;++a)n.fillText(i[a],l.x(e.x),e.y+o.lineHeight/2),e.y+=o.lineHeight+r.footerSpacing}}drawBackground(e,n,r,i){const{xAlign:s,yAlign:o}=this,{x:a,y:l}=e,{width:u,height:d}=r,{topLeft:h,topRight:f,bottomLeft:m,bottomRight:v}=_r(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+h,l),o==="top"&&this.drawCaret(e,n,r,i),n.lineTo(a+u-f,l),n.quadraticCurveTo(a+u,l,a+u,l+f),o==="center"&&s==="right"&&this.drawCaret(e,n,r,i),n.lineTo(a+u,l+d-v),n.quadraticCurveTo(a+u,l+d,a+u-v,l+d),o==="bottom"&&this.drawCaret(e,n,r,i),n.lineTo(a+m,l+d),n.quadraticCurveTo(a,l+d,a,l+d-m),o==="center"&&s==="left"&&this.drawCaret(e,n,r,i),n.lineTo(a,l+h),n.quadraticCurveTo(a,l,a+h,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,r=this.$animations,i=r&&r.x,s=r&&r.y;if(i||s){const o=ui[e.position].call(this,this._active,this._eventPosition);if(!o)return;const a=this._size=zh(this,e),l=Object.assign({},o,this._size),u=Fh(n,e,l),d=Ah(e,l,u,n);(i._to!==d.x||s._to!==d.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let r=this.opacity;if(!r)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},s={x:this.x,y:this.y};r=Math.abs(r)<.001?0:r;const o=ct(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(e.save(),e.globalAlpha=r,this.drawBackground(s,e,i,n),eg(e,n.textDirection),s.y+=o.top,this.drawTitle(s,e,n),this.drawBody(s,e,n),this.drawFooter(s,e,n),tg(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const r=this._active,i=e.map(({datasetIndex:a,index:l})=>{const u=this.chart.getDatasetMeta(a);if(!u)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:u.data[l],index:l}}),s=!Eo(r,i),o=this._positionChanged(i,n);(s||o)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,r=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,s=this._active||[],o=this._getActiveElements(e,s,n,r),a=this._positionChanged(o,e),l=n||!Eo(o,s)||a;return l&&(this._active=o,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,r,i){const s=this.options;if(e.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const o=this.chart.getElementsAtEventForMode(e,s.mode,s,r);return s.reverse&&o.reverse(),o}_positionChanged(e,n){const{caretX:r,caretY:i,options:s}=this,o=ui[s.position].call(this,e,n);return o!==!1&&(r!==o.x||i!==o.y)}}L(tc,"positioners",ui);var yg={id:"tooltip",_element:tc,positioners:ui,afterInit(t,e,n){n&&(t.tooltip=new tc({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:vg},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const iS=(t,e,n,r)=>(typeof e=="string"?(n=t.push(e)-1,r.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function sS(t,e,n,r){const i=t.indexOf(e);if(i===-1)return iS(t,e,n,r);const s=t.lastIndexOf(e);return i!==s?n:i}const oS=(t,e)=>t===null?null:He(Math.round(t),0,e);function Hh(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class nc extends Ir{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const r=this.getLabels();for(const{index:i,label:s}of n)r[i]===s&&r.splice(i,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(X(e))return null;const r=this.getLabels();return n=isFinite(n)&&r[n]===e?n:sS(r,e,I(n,e),this._addedLabels),oS(n,r.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:r,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(r=0),n||(i=this.getLabels().length-1)),this.min=r,this.max=i}buildTicks(){const e=this.min,n=this.max,r=this.options.offset,i=[];let s=this.getLabels();s=e===0&&n===s.length-1?s:s.slice(e,n+1),this._valueRange=Math.max(s.length-(r?0:1),1),this._startValue=this.min-(r?.5:0);for(let o=e;o<=n;o++)i.push({value:o});return i}getLabelForValue(e){return Hh.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}L(nc,"id","category"),L(nc,"defaults",{ticks:{callback:Hh}});function aS(t,e){const n=[],{bounds:i,step:s,min:o,max:a,precision:l,count:u,maxTicks:d,maxDigits:h,includeBounds:f}=t,m=s||1,v=d-1,{min:g,max:b}=e,p=!X(o),x=!X(a),y=!X(u),w=(b-g)/(h+1);let S=Xd((b-g)/v/m)*m,N,_,j,E;if(S<1e-14&&!p&&!x)return[{value:g},{value:b}];E=Math.ceil(b/S)-Math.floor(g/S),E>v&&(S=Xd(E*S/v/m)*m),X(l)||(N=Math.pow(10,l),S=Math.ceil(S*N)/N),i==="ticks"?(_=Math.floor(g/S)*S,j=Math.ceil(b/S)*S):(_=g,j=b),p&&x&&s&&ob((a-o)/s,S/1e3)?(E=Math.round(Math.min((a-o)/S,d)),S=(a-o)/E,_=o,j=a):y?(_=p?o:_,j=x?a:j,E=u-1,S=(j-_)/E):(E=(j-_)/S,Qs(E,Math.round(E),S/1e3)?E=Math.round(E):E=Math.ceil(E));const M=Math.max(Qd(S),Qd(_));N=Math.pow(10,X(l)?M:l),_=Math.round(_*N)/N,j=Math.round(j*N)/N;let T=0;for(p&&(f&&_!==o?(n.push({value:o}),_<o&&T++,Qs(Math.round((_+T*S)*N)/N,o,Wh(o,w,t))&&T++):_<o&&T++);T<E;++T){const D=Math.round((_+T*S)*N)/N;if(x&&D>a)break;n.push({value:D})}return x&&f&&j!==a?n.length&&Qs(n[n.length-1].value,a,Wh(a,w,t))?n[n.length-1].value=a:n.push({value:a}):(!x||j===a)&&n.push({value:j}),n}function Wh(t,e,{horizontal:n,minRotation:r}){const i=At(r),s=(n?Math.sin(i):Math.cos(i))||.001,o=.75*e*(""+t).length;return Math.min(e/s,o)}class lS extends Ir{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return X(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:r}=this.getUserBounds();let{min:i,max:s}=this;const o=l=>i=n?i:l,a=l=>s=r?s:l;if(e){const l=yn(i),u=yn(s);l<0&&u<0?a(0):l>0&&u>0&&o(0)}if(i===s){let l=s===0?1:Math.abs(s*.05);a(s+l),e||o(i-l)}this.min=i,this.max=s}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:r}=e,i;return r?(i=Math.ceil(this.max/r)-Math.floor(this.min/r)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${r} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let r=this.getTickLimit();r=Math.max(2,r);const i={maxTicks:r,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},s=this._range||this,o=aS(i,s);return e.bounds==="ticks"&&ab(o,this,"value"),e.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const e=this.ticks;let n=this.min,r=this.max;if(super.configure(),this.options.offset&&e.length){const i=(r-n)/Math.max(e.length-1,1)/2;n-=i,r+=i}this._startValue=n,this._endValue=r,this._valueRange=r-n}getLabelForValue(e){return su(e,this.chart.options.locale,this.options.ticks.format)}}class rc extends lS{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=lt(e)?e:0,this.max=lt(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,r=At(this.options.ticks.minRotation),i=(e?Math.sin(r):Math.cos(r))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,s.lineHeight/i))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}L(rc,"id","linear"),L(rc,"defaults",{ticks:{callback:Yp.formatters.numeric}});const ca={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Ae=Object.keys(ca);function Bh(t,e){return t-e}function Vh(t,e){if(X(e))return null;const n=t._adapter,{parser:r,round:i,isoWeekday:s}=t._parseOpts;let o=e;return typeof r=="function"&&(o=r(o)),lt(o)||(o=typeof r=="string"?n.parse(o,r):n.parse(o)),o===null?null:(i&&(o=i==="week"&&(Oo(s)||s===!0)?n.startOf(o,"isoWeek",s):n.startOf(o,i)),+o)}function Yh(t,e,n,r){const i=Ae.length;for(let s=Ae.indexOf(t);s<i-1;++s){const o=ca[Ae[s]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((n-e)/(a*o.size))<=r)return Ae[s]}return Ae[i-1]}function cS(t,e,n,r,i){for(let s=Ae.length-1;s>=Ae.indexOf(n);s--){const o=Ae[s];if(ca[o].common&&t._adapter.diff(i,r,o)>=e-1)return o}return Ae[n?Ae.indexOf(n):0]}function uS(t){for(let e=Ae.indexOf(t)+1,n=Ae.length;e<n;++e)if(ca[Ae[e]].common)return Ae[e]}function Xh(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:r,hi:i}=ru(n,e),s=n[r]>=e?n[r]:n[i];t[s]=!0}}function dS(t,e,n,r){const i=t._adapter,s=+i.startOf(e[0].value,r),o=e[e.length-1].value;let a,l;for(a=s;a<=o;a=+i.add(a,1,r))l=n[a],l>=0&&(e[l].major=!0);return e}function Qh(t,e,n){const r=[],i={},s=e.length;let o,a;for(o=0;o<s;++o)a=e[o],i[a]=o,r.push({value:a,major:!1});return s===0||!n?r:dS(t,r,i,n)}class Ao extends Ir{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const r=e.time||(e.time={}),i=this._adapter=new Nw._date(e.adapters.date);i.init(n),bi(r.displayFormats,i.formats()),this._parseOpts={parser:r.parser,round:r.round,isoWeekday:r.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:Vh(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,r=e.time.unit||"day";let{min:i,max:s,minDefined:o,maxDefined:a}=this.getUserBounds();function l(u){!o&&!isNaN(u.min)&&(i=Math.min(i,u.min)),!a&&!isNaN(u.max)&&(s=Math.max(s,u.max))}(!o||!a)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=lt(i)&&!isNaN(i)?i:+n.startOf(Date.now(),r),s=lt(s)&&!isNaN(s)?s:+n.endOf(Date.now(),r)+1,this.min=Math.min(i,s-1),this.max=Math.max(i+1,s)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],r=e[e.length-1]),{min:n,max:r}}buildTicks(){const e=this.options,n=e.time,r=e.ticks,i=r.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const s=this.min,o=this.max,a=hb(i,s,o);return this._unit=n.unit||(r.autoSkip?Yh(n.minUnit,this.min,this.max,this._getLabelCapacity(s)):cS(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!r.major.enabled||this._unit==="year"?void 0:uS(this._unit),this.initOffsets(i),e.reverse&&a.reverse(),Qh(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,r=0,i,s;this.options.offset&&e.length&&(i=this.getDecimalForValue(e[0]),e.length===1?n=1-i:n=(this.getDecimalForValue(e[1])-i)/2,s=this.getDecimalForValue(e[e.length-1]),e.length===1?r=s:r=(s-this.getDecimalForValue(e[e.length-2]))/2);const o=e.length<3?.5:.25;n=He(n,0,o),r=He(r,0,o),this._offsets={start:n,end:r,factor:1/(n+1+r)}}_generate(){const e=this._adapter,n=this.min,r=this.max,i=this.options,s=i.time,o=s.unit||Yh(s.minUnit,n,r,this._getLabelCapacity(n)),a=I(i.ticks.stepSize,1),l=o==="week"?s.isoWeekday:!1,u=Oo(l)||l===!0,d={};let h=n,f,m;if(u&&(h=+e.startOf(h,"isoWeek",l)),h=+e.startOf(h,u?"day":o),e.diff(r,n,o)>1e5*a)throw new Error(n+" and "+r+" are too far apart with stepSize of "+a+" "+o);const v=i.ticks.source==="data"&&this.getDataTimestamps();for(f=h,m=0;f<r;f=+e.add(f,a,o),m++)Xh(d,f,v);return(f===r||i.bounds==="ticks"||m===1)&&Xh(d,f,v),Object.keys(d).sort(Bh).map(g=>+g)}getLabelForValue(e){const n=this._adapter,r=this.options.time;return r.tooltipFormat?n.format(e,r.tooltipFormat):n.format(e,r.displayFormats.datetime)}format(e,n){const i=this.options.time.displayFormats,s=this._unit,o=n||i[s];return this._adapter.format(e,o)}_tickFormatFunction(e,n,r,i){const s=this.options,o=s.ticks.callback;if(o)return J(o,[e,n,r],this);const a=s.time.displayFormats,l=this._unit,u=this._majorUnit,d=l&&a[l],h=u&&a[u],f=r[n],m=u&&h&&f&&f.major;return this._adapter.format(e,i||(m?h:d))}generateTickLabels(e){let n,r,i;for(n=0,r=e.length;n<r;++n)i=e[n],i.label=this._tickFormatFunction(i.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,r=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+r)*n.factor)}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+r*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,r=this.ctx.measureText(e).width,i=At(this.isHorizontal()?n.maxRotation:n.minRotation),s=Math.cos(i),o=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:r*s+a*o,h:r*o+a*s}}_getLabelCapacity(e){const n=this.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,s=this._tickFormatFunction(e,0,Qh(this,[e],this._majorUnit),i),o=this._getLabelSize(s),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let e=this._cache.data||[],n,r;if(e.length)return e;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,r=i.length;n<r;++n)e=e.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,r;if(e.length)return e;const i=this.getLabels();for(n=0,r=i.length;n<r;++n)e.push(Vh(this,i[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return Hp(e.sort(Bh))}}L(Ao,"id","time"),L(Ao,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Os(t,e,n){let r=0,i=t.length-1,s,o,a,l;n?(e>=t[r].pos&&e<=t[i].pos&&({lo:r,hi:i}=ql(t,"pos",e)),{pos:s,time:a}=t[r],{pos:o,time:l}=t[i]):(e>=t[r].time&&e<=t[i].time&&({lo:r,hi:i}=ql(t,"time",e)),{time:s,pos:a}=t[r],{time:o,pos:l}=t[i]);const u=o-s;return u?a+(l-a)*(e-s)/u:a}class Kh extends Ao{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=Os(n,this.min),this._tableRange=Os(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:r}=this,i=[],s=[];let o,a,l,u,d;for(o=0,a=e.length;o<a;++o)u=e[o],u>=n&&u<=r&&i.push(u);if(i.length<2)return[{time:n,pos:0},{time:r,pos:1}];for(o=0,a=i.length;o<a;++o)d=i[o+1],l=i[o-1],u=i[o],Math.round((d+l)/2)!==u&&s.push({time:u,pos:o/(a-1)});return s}_generate(){const e=this.min,n=this.max;let r=super.getDataTimestamps();return(!r.includes(e)||!r.length)&&r.splice(0,0,e),(!r.includes(n)||r.length===1)&&r.push(n),r.sort((i,s)=>i-s)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),r=this.getLabelTimestamps();return n.length&&r.length?e=this.normalize(n.concat(r)):e=n.length?n:r,e=this._cache.all=e,e}getDecimalForValue(e){return(Os(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return Os(this._table,r*this._tableRange+this._minPos,!0)}}L(Kh,"id","timeseries"),L(Kh,"defaults",Ao.defaults);const bg="label";function Gh(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function hS(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function wg(t,e){t.labels=e}function Sg(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:bg;const r=[];t.datasets=e.map(i=>{const s=t.datasets.find(o=>o[n]===i[n]);return!s||!i.data||r.includes(s)?{...i}:(r.push(s),Object.assign(s,i),s)})}function fS(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:bg;const n={labels:[],datasets:[]};return wg(n,t.labels),Sg(n,t.datasets,e),n}function mS(t,e){const{height:n=150,width:r=300,redraw:i=!1,datasetIdKey:s,type:o,data:a,options:l,plugins:u=[],fallbackContent:d,updateMode:h,...f}=t,m=k.useRef(null),v=k.useRef(null),g=()=>{m.current&&(v.current=new ns(m.current,{type:o,data:fS(a,s),options:l&&{...l},plugins:u}),Gh(e,v.current))},b=()=>{Gh(e,null),v.current&&(v.current.destroy(),v.current=null)};return k.useEffect(()=>{!i&&v.current&&l&&hS(v.current,l)},[i,l]),k.useEffect(()=>{!i&&v.current&&wg(v.current.config.data,a.labels)},[i,a.labels]),k.useEffect(()=>{!i&&v.current&&a.datasets&&Sg(v.current.config.data,a.datasets,s)},[i,a.datasets]),k.useEffect(()=>{v.current&&(i?(b(),setTimeout(g)):v.current.update(h))},[i,l,a.labels,a.datasets,h]),k.useEffect(()=>{v.current&&(b(),setTimeout(g))},[o]),k.useEffect(()=>(g(),()=>b()),[]),pt.createElement("canvas",{ref:m,role:"img",height:n,width:r,...f},d)}const pS=k.forwardRef(mS);function _g(t,e){return ns.register(e),k.forwardRef((n,r)=>pt.createElement(pS,{...n,ref:r,type:t}))}const gS=_g("bar",Ks),xS=_g("doughnut",ai);ns.register(ci,yg,gg);function vS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n}=Ye(),{theme:r}=Pe(),i=k.useRef(null),s=t.currentUnits,o=e,a=s+o,l=a>0?o/a*100:0;k.useEffect(()=>{const h=i.current;if(h){const f=h.ctx,m=f.createRadialGradient(200,200,50,200,200,150);m.addColorStop(0,"#667eea"),m.addColorStop(.3,"#764ba2"),m.addColorStop(.6,"#667eea"),m.addColorStop(1,"#f093fb");const v=f.createRadialGradient(200,200,50,200,200,150);v.addColorStop(0,"#ff9a9e"),v.addColorStop(.3,"#fecfef"),v.addColorStop(.6,"#fecfef"),v.addColorStop(1,"#ffc3a0"),h.data.datasets[0].backgroundColor=[m,v],h.update()}},[s,o]);const u={labels:[`Remaining ${n()}`,`Used ${n()}`],datasets:[{data:[s,o],backgroundColor:["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)"],borderColor:["rgba(255, 255, 255, 0.9)","rgba(255, 255, 255, 0.9)"],borderWidth:4,cutout:"78%",borderRadius:12,borderJoinStyle:"round",hoverBorderWidth:6,hoverBorderColor:["rgba(255, 255, 255, 1)","rgba(255, 255, 255, 1)"],shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.1)"}]},d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(h){const f=h.label||"",m=h.parsed,v=a>0?(m/a*100).toFixed(1):0;return`${f}: ${m.toFixed(2)} (${v}%)`}}}},animation:{animateRotate:!0,animateScale:!0,duration:2e3,easing:"easeInOutCubic",delay:h=>h.dataIndex*200},interaction:{intersect:!1,mode:"nearest"},elements:{arc:{borderWidth:4,hoverBorderWidth:6,borderSkipped:!1,borderAlign:"inner"}},layout:{padding:{top:20,bottom:20,left:20,right:20}}};return c.jsxs("div",{className:"relative",children:[c.jsxs("div",{className:"relative h-96 p-8",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 rounded-3xl opacity-60"}),c.jsx("div",{className:"absolute inset-1 bg-gradient-to-tr from-white/90 via-white/70 to-white/50 rounded-2xl backdrop-blur-lg border border-white/20 shadow-2xl"}),c.jsx("div",{className:"absolute inset-3 bg-gradient-to-bl from-white/40 to-transparent rounded-xl"}),c.jsx("div",{className:"absolute top-4 left-4 w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-xl animate-pulse"}),c.jsx("div",{className:"absolute bottom-4 right-4 w-20 h-20 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-15 blur-2xl animate-pulse",style:{animationDelay:"1s"}}),c.jsxs("div",{className:"relative h-full flex items-center justify-center",children:[c.jsx("div",{className:"w-full h-full max-w-sm max-h-sm",children:c.jsx(xS,{ref:i,data:u,options:d})}),c.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 w-40 h-40 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"}),c.jsxs("div",{className:"relative w-40 h-40 bg-white/40 backdrop-blur-lg rounded-full border border-white/30 shadow-2xl flex items-center justify-center",children:[c.jsx("div",{className:"absolute inset-2 bg-gradient-to-br from-white/60 via-white/40 to-white/20 rounded-full"}),c.jsxs("div",{className:"relative text-center z-10",children:[c.jsx("div",{className:"mb-2 flex justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"}),c.jsx(Ue,{className:"relative h-8 w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce",style:{animationDuration:"2s"}})]})}),c.jsxs("div",{className:"relative mb-1",children:[c.jsx("div",{className:"text-3xl font-black bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent drop-shadow-lg",children:s.toFixed(1)}),c.jsxs("div",{className:"text-xs font-semibold text-gray-600 mt-1 tracking-wide",children:[n()," Left"]})]}),c.jsx("div",{className:"mt-1",children:c.jsxs("div",{className:`text-sm font-bold tracking-tight ${l>70?"text-red-500 drop-shadow-lg":l>40?"text-amber-500 drop-shadow-lg":"text-emerald-500 drop-shadow-lg"}`,children:[l.toFixed(1),"% Used"]})})]}),c.jsx("div",{className:"absolute top-3 right-3 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-pulse"}),c.jsx("div",{className:"absolute bottom-3 left-3 w-1.5 h-1.5 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-50 animate-pulse",style:{animationDelay:"1s"}})]})]})})]})]}),c.jsxs("div",{className:"mt-8 grid grid-cols-2 gap-6",children:[c.jsxs("div",{className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 p-6 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/40 to-transparent"}),c.jsxs("div",{className:"relative flex items-center space-x-4",children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"}),c.jsx("div",{className:"relative p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl shadow-lg",children:c.jsx(Zc,{className:"h-6 w-6 text-white"})})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-black bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:s.toFixed(2)}),c.jsxs("div",{className:"text-sm font-semibold text-gray-600 tracking-wide",children:[n()," Available"]})]})]})]}),c.jsxs("div",{className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-50 via-pink-50 to-red-50 p-6 border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/40 to-transparent"}),c.jsxs("div",{className:"relative flex items-center space-x-4",children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"}),c.jsx("div",{className:"relative p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg",children:c.jsx(_1,{className:"h-6 w-6 text-white"})})]}),c.jsxs("div",{children:[c.jsx("div",{className:"text-2xl font-black bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:o.toFixed(2)}),c.jsxs("div",{className:"text-sm font-semibold text-gray-600 tracking-wide",children:[n()," Consumed"]})]})]})]})]}),c.jsxs("div",{className:"mt-8 relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 p-6 border border-white/30 shadow-xl backdrop-blur-sm",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/60 to-white/20"}),c.jsxs("div",{className:"relative flex justify-between items-center",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:"text-sm font-semibold text-gray-500 tracking-wider uppercase",children:"Total Cost"}),c.jsxs("div",{className:"text-3xl font-black bg-gradient-to-r from-slate-700 via-gray-800 to-zinc-700 bg-clip-text text-transparent",children:[t.currencySymbol||"R",(o*t.unitCost).toFixed(2)]})]}),c.jsxs("div",{className:"text-right space-y-2",children:[c.jsx("div",{className:"text-sm font-semibold text-gray-500 tracking-wider uppercase",children:"Rate"}),c.jsxs("div",{className:"text-xl font-bold bg-gradient-to-r from-slate-600 to-gray-700 bg-clip-text text-transparent",children:[t.currencySymbol||"R",t.unitCost.toFixed(2),"/",n()]})]})]}),c.jsx("div",{className:"absolute top-2 right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 blur-sm"}),c.jsx("div",{className:"absolute bottom-2 left-2 w-6 h-6 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-15 blur-sm"})]}),c.jsxs("div",{className:"mt-8",children:[c.jsxs("div",{className:"flex justify-between items-center mb-4",children:[c.jsx("span",{className:"text-sm font-bold text-gray-700 tracking-wide uppercase",children:"Usage Progress"}),c.jsxs("span",{className:`text-lg font-black px-3 py-1 rounded-full ${l>70?"bg-gradient-to-r from-red-100 to-red-200 text-red-700":l>40?"bg-gradient-to-r from-amber-100 to-yellow-200 text-amber-700":"bg-gradient-to-r from-emerald-100 to-green-200 text-emerald-700"}`,children:[l.toFixed(1),"%"]})]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"w-full bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100 rounded-full h-4 shadow-inner border border-white/50 backdrop-blur-sm",children:c.jsx("div",{className:`h-4 rounded-full transition-all duration-1500 ease-out shadow-lg ${l>70?"bg-gradient-to-r from-red-400 via-red-500 to-red-600":l>40?"bg-gradient-to-r from-amber-400 via-yellow-500 to-orange-500":"bg-gradient-to-r from-emerald-400 via-green-500 to-teal-500"}`,style:{width:`${Math.min(l,100)}%`}})}),c.jsx("div",{className:`absolute top-0 h-4 rounded-full opacity-60 blur-sm transition-all duration-1500 ${l>70?"bg-gradient-to-r from-red-300 to-red-500":l>40?"bg-gradient-to-r from-amber-300 to-orange-500":"bg-gradient-to-r from-emerald-300 to-green-500"}`,style:{width:`${Math.min(l,100)}%`}}),c.jsx("div",{className:`absolute top-0 h-4 rounded-full opacity-30 blur-md transition-all duration-1500 ${l>70?"bg-gradient-to-r from-red-200 to-red-400":l>40?"bg-gradient-to-r from-amber-200 to-orange-400":"bg-gradient-to-r from-emerald-200 to-green-400"}`,style:{width:`${Math.min(l,100)}%`}}),l>0&&c.jsx("div",{className:"absolute top-0 h-4 rounded-full bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse",style:{width:`${Math.min(l,100)}%`}})]})]})]})}function yS(){const t=ia(),{state:e,getDisplayUnitName:n}=Ye(),{theme:r}=Pe(),i=e.currentUnits,s=e.thresholdLimit;return c.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx("div",{className:"p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg",children:c.jsx(Hi,{className:"h-6 w-6 text-white animate-pulse"})})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsxs("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:["⚠️ Low ",n()," Warning!"]}),c.jsxs("div",{className:"text-sm text-amber-700 space-y-2",children:[c.jsxs("p",{className:"font-medium",children:["You have ",c.jsxs("strong",{className:"text-amber-900",children:[i.toFixed(2)," ",n()]})," remaining, which is below your threshold of ",c.jsxs("strong",{className:"text-amber-900",children:[s.toFixed(2)," ",n()]}),"."]}),c.jsxs("p",{children:["💡 ",c.jsx("strong",{children:"Time to top up!"})," Consider purchasing more ",n()," to avoid running out of power."]})]}),c.jsxs("div",{className:"mt-4 flex flex-wrap gap-3",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",children:"🛒 Top Up Now"}),c.jsx("button",{onClick:()=>t("/settings"),className:"bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200",children:"⚙️ Adjust Threshold"})]})]})]})})}function qh(){const t=ia(),{state:e,isThresholdExceeded:n,getDisplayUnitName:r}=Ye(),{theme:i}=Pe();return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${i.text}`,children:"Dashboard"}),c.jsx("p",{className:`mt-2 ${i.textSecondary}`,children:"Monitor your electricity usage and current meter readings"})]}),n&&c.jsx(yS,{}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${i.card} rounded-2xl shadow-lg p-6 border ${i.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${i.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Ue,{className:"h-5 w-5 text-white"})}),"Usage Overview"]}),c.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-4",children:c.jsx(vS,{})})]}),c.jsxs("div",{className:`${i.card} rounded-2xl shadow-lg p-6 border ${i.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${i.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(xn,{className:"h-5 w-5 text-white"})}),"Recent Activity"]}),c.jsxs("div",{className:"space-y-3",children:[e.purchases.slice(0,3).map(s=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500 shadow-sm",children:c.jsx(st,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsxs("p",{className:`text-sm font-semibold ${i.text}`,children:["Purchase: ",e.currencySymbol||"R",s.currency.toFixed(2)]}),c.jsx("p",{className:`text-xs ${i.textSecondary} opacity-70`,children:new Date(s.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:"text-sm font-semibold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:["+",s.units.toFixed(2)," ",r()]})]})},s.id)),e.usageHistory.slice(0,2).map(s=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-rose-400 to-pink-500 shadow-sm",children:c.jsx(xn,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-sm font-semibold ${i.text}`,children:"Usage recorded"}),c.jsx("p",{className:`text-xs ${i.textSecondary} opacity-70`,children:new Date(s.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:"text-sm font-semibold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:["-",s.usage.toFixed(2)," ",r()]})]})},s.id)),e.purchases.length===0&&e.usageHistory.length===0&&c.jsxs("div",{className:"text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-green-200 w-fit mx-auto mb-4",children:c.jsx(Ue,{className:"h-12 w-12 text-emerald-600"})}),c.jsx("p",{className:`text-sm ${i.textSecondary} opacity-80 font-medium`,children:"No recent activity"}),c.jsx("p",{className:`text-xs ${i.textSecondary} opacity-60 mt-1`,children:"Start by making a purchase or recording usage"})]})]})]})]}),c.jsxs("div",{className:`${i.card} rounded-2xl shadow-lg p-8 border ${i.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsxs("h2",{className:`text-2xl font-bold ${i.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-slate-500 to-gray-600 shadow-lg",children:c.jsx(Ue,{className:"h-6 w-6 text-white"})}),"Quick Actions"]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[c.jsxs("button",{onClick:()=>t("/purchases"),className:"p-6 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-2xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[c.jsx(st,{className:"h-8 w-8 mx-auto mb-3"}),c.jsx("span",{className:"block text-lg font-semibold",children:"Add Purchase"}),c.jsx("span",{className:"block text-sm opacity-80 mt-1",children:"Top up your units"})]}),c.jsxs("button",{onClick:()=>t("/usage"),className:"p-6 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[c.jsx(xn,{className:"h-8 w-8 mx-auto mb-3"}),c.jsx("span",{className:"block text-lg font-semibold",children:"Record Usage"}),c.jsx("span",{className:"block text-sm opacity-80 mt-1",children:"Track consumption"})]}),c.jsxs("button",{onClick:()=>t("/history"),className:"p-6 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-2xl hover:from-violet-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[c.jsx(Ue,{className:"h-8 w-8 mx-auto mb-3"}),c.jsx("span",{className:"block text-lg font-semibold",children:"View History"}),c.jsx("span",{className:"block text-sm opacity-80 mt-1",children:"See all records"})]})]})]})]})}function bS(){var g;const[t,e]=k.useState(""),[n,r]=k.useState(!1),{state:i,addPurchase:s,getDisplayUnitName:o}=Ye(),{theme:a}=Pe(),l=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],u=b=>Math.round((b+Number.EPSILON)*100)/100,d=b=>{const p=u(b);return p%1===0?p.toString():p.toFixed(2)},h=parseFloat(t)||0,f=i.unitCost||0,m=f>0?u(h/f):0,v=async b=>{b.preventDefault(),r(!0);try{const p=u(parseFloat(t)),x=m;if(isNaN(p)||p<=0){alert("Please enter a valid positive amount");return}if(f<=0){alert("Please set a valid unit cost in Settings before making a purchase");return}if(x<=0){alert("The calculated units must be greater than 0");return}s(p,x),e(""),alert(`Purchase added successfully! Added ${d(x)} ${o()} for ${i.currencySymbol||"R"}${d(p)}`)}catch(p){console.error("Error adding purchase:",p),alert("Error adding purchase. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:v,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"currency",className:`block text-sm font-semibold ${a.text} mb-3`,children:["💰 Amount (",((g=l.find(b=>b.code===(i.currency||"ZAR")))==null?void 0:g.name)||"South African Rand",")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500",children:c.jsx(st,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currency",value:t,onChange:b=>e(b.target.value),step:"0.01",min:"0",placeholder:"Enter amount to spend",className:`w-full pl-12 pr-4 py-4 border-2 border-emerald-100 rounded-xl focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 bg-gradient-to-br from-emerald-50 to-green-50 ${a.text} placeholder-emerald-400 font-medium shadow-sm transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${a.textSecondary} opacity-80 font-medium`,children:"💡 Enter the amount you want to spend"})]}),c.jsxs("div",{children:[c.jsxs("label",{className:`block text-sm font-semibold ${a.text} mb-3`,children:["⚡ Units Preview (",o(),")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500",children:c.jsx(Ue,{className:"h-4 w-4 text-white"})})}),c.jsx("div",{className:`w-full pl-12 pr-4 py-4 border-2 border-blue-100 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 ${a.text} font-bold text-lg shadow-sm flex items-center min-h-[56px]`,children:h>0&&f>0?c.jsxs("span",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:[d(m)," ",o()]}):c.jsx("span",{className:"text-blue-400 font-medium",children:f<=0?"Set unit cost in Settings first":"Enter amount above to see units"})})]}),c.jsx("p",{className:`mt-2 text-xs ${a.textSecondary} opacity-80 font-medium`,children:"⚡ Live preview of units you'll receive"})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Tp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${a.text} text-lg`,children:"Calculation Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${a.text} font-bold`,children:[i.currencySymbol||"R",d(f)," per ",o()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Amount:"}),c.jsxs("span",{className:"font-bold text-lg bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[i.currencySymbol||"R",d(h)]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"Units:"}),c.jsxs("span",{className:"font-bold text-lg bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:[d(m)," ",o()]})]}),c.jsx("div",{className:"border-t border-violet-200 my-3"}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg border border-amber-100",children:[c.jsx("span",{className:`${a.textSecondary} font-medium`,children:"New Total Units:"}),c.jsxs("span",{className:"font-bold text-xl bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent",children:[d(u(i.currentUnits+m))," ",o()]})]})]})]}),c.jsx("button",{type:"submit",disabled:n||h<=0||m<=0||f<=0,className:"w-full bg-gradient-to-r from-emerald-500 via-green-500 to-teal-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-emerald-600 hover:via-green-600 hover:to-teal-700 transition-all duration-300 focus:ring-4 focus:ring-emerald-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Adding Purchase..."]}):c.jsxs(c.Fragment,{children:[c.jsx(st,{className:"h-5 w-5"}),"Add Purchase"]})})}),c.jsxs("div",{className:"text-center p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-100",children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-2",children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${a.text}`,children:"Live Calculator"})]}),c.jsx("p",{className:`text-xs ${a.textSecondary} opacity-80 leading-relaxed`,children:"Enter the amount you want to spend and see the units you'll receive in real-time. The calculation is based on your current unit cost setting."})]})]})}function wS(){const{state:t,getDisplayUnitName:e}=Ye(),{theme:n}=Pe(),r=t.purchases.reduce((s,o)=>s+o.currency,0),i=t.purchases.reduce((s,o)=>s+o.units,0);return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${n.text}`,children:"Purchases"}),c.jsx("p",{className:`mt-2 ${n.textSecondary}`,children:"Add new purchases and view your purchase history"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg",children:c.jsx(st,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Spent"}),c.jsxs("p",{className:"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[t.currencySymbol||"R",r.toFixed(2)]}),c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})]})]})}),c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg",children:c.jsx(Ue,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsxs("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:["Total ",e()," Purchased"]}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:i.toFixed(2)}),c.jsx("p",{className:"text-xs text-blue-500 font-medium",children:e()})]})]})}),c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Purchases"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:t.purchases.length}),c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Transactions"})]})]})})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(st,{className:"h-5 w-5 text-white"})}),"Add New Purchase"]}),c.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-4",children:c.jsx(bS,{})})]}),c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(Sr,{className:"h-5 w-5 text-white"})}),"Recent Purchases"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.purchases.slice(0,10).map(s=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${n.text} text-lg`,children:[t.currencySymbol||"R",s.currency.toFixed(2)]}),c.jsxs("p",{className:`text-sm ${n.textSecondary} opacity-80`,children:[s.units.toFixed(2)," ",e()," @ ",t.currencySymbol||"R",s.unitCost.toFixed(2),"/",e()]}),c.jsx("p",{className:`text-xs ${n.textSecondary} mt-1 opacity-70`,children:s.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200",children:["+",s.units.toFixed(2)," ",e()]}),c.jsxs("p",{className:`text-xs ${n.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",s.currency.toFixed(2)]})]})]})},s.id)),t.purchases.length===0&&c.jsxs("div",{className:"text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-green-200 w-fit mx-auto mb-4",children:c.jsx(st,{className:"h-12 w-12 text-emerald-600"})}),c.jsx("p",{className:`text-sm ${n.textSecondary} opacity-80 font-medium`,children:"No purchases yet"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-60 mt-1`,children:"Add your first purchase above to get started"})]})]})]})]}),t.purchases.length>0&&c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg border ${n.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsxs("div",{className:"px-8 py-6 border-b border-gray-200 bg-white/60 backdrop-blur-sm rounded-t-2xl",children:[c.jsxs("h2",{className:`text-2xl font-bold ${n.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-slate-500 to-gray-600 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),"All Purchases"]}),c.jsx("p",{className:`mt-2 ${n.textSecondary} opacity-80`,children:"Complete history of all your purchase transactions"})]}),c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsx("thead",{className:n.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsxs("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:["Amount (",t.currencySymbol||"R",")"]}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:e()}),c.jsxs("th",{className:`px-6 py-3 text-left text-xs font-medium ${n.textSecondary} uppercase tracking-wider`,children:["Cost per ",e()]})]})}),c.jsx("tbody",{className:`${n.card} divide-y divide-gray-200`,children:t.purchases.map(s=>c.jsxs("tr",{children:[c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${n.text}`,children:s.timestamp}),c.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm font-medium ${n.text}`,children:[t.currencySymbol||"R",s.currency.toFixed(2)]}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${n.text}`,children:s.units.toFixed(2)}),c.jsxs("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${n.text}`,children:[t.currencySymbol||"R",s.unitCost.toFixed(2)]})]},s.id))})]})})]})]})}function SS(){const[t,e]=k.useState(""),[n,r]=k.useState(!1),{state:i,updateUsage:s,usageSinceLastRecording:o,getDisplayUnitName:a}=Ye(),{theme:l}=Pe(),u=parseFloat(t)||0,d=i.currentUnits-u,h=d*i.unitCost,f=async m=>{m.preventDefault(),r(!0);try{const v=parseFloat(t);if(isNaN(v)||v<0){alert("Please enter a valid meter reading (0 or greater)");return}if(v>i.currentUnits){alert("Current reading cannot be higher than your available units");return}s(v),e(""),alert(`Usage recorded successfully! Used ${d.toFixed(2)} ${a()} costing ${i.currencySymbol||"R"}${h.toFixed(2)}`)}catch(v){console.error("Error recording usage:",v),alert("Error recording usage. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:f,className:"space-y-6",children:[c.jsxs("div",{className:"p-5 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm",children:[c.jsxs("h3",{className:`font-semibold ${l.text} mb-4 flex items-center gap-2`,children:[c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500",children:c.jsx(Ue,{className:"h-4 w-4 text-white"})}),"Current Status"]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-2 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Available Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-2 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.previousUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-2 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Usage Since Last:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[o.toFixed(2)," ",a()]})]})]})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currentReading",className:`block text-sm font-semibold ${l.text} mb-3`,children:"Current Meter Reading"}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500",children:c.jsx(Ue,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currentReading",value:t,onChange:m=>e(m.target.value),step:"0.01",min:"0",max:i.currentUnits,placeholder:"Enter current meter reading",className:`w-full pl-12 pr-4 py-4 border-2 border-emerald-100 rounded-xl focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 bg-gradient-to-br from-emerald-50 to-green-50 ${l.text} placeholder-emerald-400 font-medium shadow-sm transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${l.textSecondary} opacity-80 font-medium`,children:"📊 Enter the current reading from your electricity meter"})]}),u>0&&c.jsxs("div",{className:"p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Tp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${l.text} text-lg`,children:"Usage Calculation"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"New Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[u.toFixed(2)," ",a()]})]}),c.jsx("div",{className:"border-t border-violet-200 my-3"}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-rose-50 to-pink-50 rounded-lg border border-rose-100",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Units Used:"}),c.jsxs("span",{className:`font-bold text-lg ${d>=0?"bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent":"text-red-600"}`,children:[d.toFixed(2)," ",a()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg border border-amber-100",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`font-bold text-lg ${d>=0?"bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent":"text-red-600"}`,children:[i.currencySymbol||"R",h.toFixed(2)]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100",children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Remaining Units:"}),c.jsxs("span",{className:"font-bold text-lg bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[u.toFixed(2)," ",a()]})]})]}),d<0&&c.jsx("div",{className:"mt-4 p-4 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-xl",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-1 rounded-lg bg-red-500 mr-2",children:c.jsx("span",{className:"text-white text-xs",children:"⚠️"})}),c.jsx("span",{className:"text-red-700 text-sm font-medium",children:"Warning: New reading cannot be higher than available units"})]})})]}),c.jsx("button",{type:"submit",disabled:n||u<=0||u>i.currentUnits,className:"w-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-600 hover:via-indigo-600 hover:to-purple-700 transition-all duration-300 focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Recording Usage..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Ue,{className:"h-5 w-5"}),"Record Usage"]})})}),c.jsxs("div",{className:"text-center p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-100",children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-2",children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${l.text}`,children:"How it works"})]}),c.jsx("p",{className:`text-xs ${l.textSecondary} opacity-80 leading-relaxed`,children:"Record your current meter reading to track electricity usage. The system will calculate how many units you've used since the last recording."})]})]})}ns.register(nc,rc,Zs,G2,yg,gg);function _S(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n}=Ye(),{theme:r}=Pe(),i=t.usageHistory.reduce((u,d)=>u+d.usage,0),s=t.usageHistory.length>0?i/t.usageHistory.length:0,o=t.usageHistory.slice(-7).reverse(),a={labels:o.length>0?o.map((u,d)=>new Date(u.timestamp).toLocaleDateString("en-US",{month:"short",day:"numeric"})):["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:`Daily Usage (${n()})`,data:o.length>0?o.map(u=>u.usage):[12.5,15.2,8.7,22.1,18.9,14.3,16.8],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(251, 146, 60, 0.8)","rgba(14, 165, 233, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgba(99, 102, 241, 1)","rgba(139, 92, 246, 1)","rgba(236, 72, 153, 1)","rgba(34, 197, 94, 1)","rgba(251, 146, 60, 1)","rgba(14, 165, 233, 1)","rgba(168, 85, 247, 1)"],borderWidth:2,borderRadius:8,borderSkipped:!1}]},l={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Daily Usage Trend",font:{size:16,weight:"bold"},color:r.text==="text-gray-900"?"#1f2937":"#f9fafb",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(u){return`Usage: ${u.parsed.y.toFixed(2)} ${n()}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(156, 163, 175, 0.2)",drawBorder:!1},ticks:{color:r.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12},callback:function(u){return u+" "+n()}}},x:{grid:{display:!1},ticks:{color:r.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12}}}},animation:{duration:1500,easing:"easeInOutQuart"}};return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${r.text}`,children:"Usage Tracking"}),c.jsx("p",{className:`mt-2 ${r.textSecondary}`,children:"Record your current meter readings and track electricity usage"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-6 border ${r.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg",children:c.jsx(Ue,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${r.textSecondary} opacity-80`,children:"Current Reading"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:t.currentUnits.toFixed(2)}),c.jsx("p",{className:"text-xs text-blue-500 font-medium",children:n()})]})]})}),c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-6 border ${r.border} bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-rose-400 to-pink-500 shadow-lg",children:c.jsx(xn,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${r.textSecondary} opacity-80`,children:"Usage Since Last"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:e.toFixed(2)}),c.jsx("p",{className:"text-xs text-rose-500 font-medium",children:n()})]})]})}),c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-6 border ${r.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${r.textSecondary} opacity-80`,children:"Total Usage"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:i.toFixed(2)}),c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:n()})]})]})}),c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-6 border ${r.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg",children:c.jsx(Fd,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${r.textSecondary} opacity-80`,children:"Average Usage"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:s.toFixed(2)}),c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:n()})]})]})})]}),c.jsxs("div",{className:`${r.card} rounded-2xl shadow-lg p-8 border ${r.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsx("div",{className:"flex items-center justify-between mb-6",children:c.jsxs("div",{children:[c.jsxs("h2",{className:`text-2xl font-bold ${r.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg",children:c.jsx(Zc,{className:"h-6 w-6 text-white"})}),"Usage Analytics"]}),c.jsx("p",{className:`mt-2 ${r.textSecondary} opacity-80`,children:"Visual representation of your daily electricity consumption"})]})}),c.jsxs("div",{className:"h-80 relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 rounded-xl backdrop-blur-sm"}),c.jsx("div",{className:"relative h-full p-4",children:c.jsx(gS,{data:a,options:l})})]})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${r.card} rounded-2xl shadow-lg p-6 border ${r.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${r.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Ue,{className:"h-5 w-5 text-white"})}),"Record New Reading"]}),c.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-4",children:c.jsx(SS,{})})]}),c.jsxs("div",{className:`${r.card} rounded-2xl shadow-lg p-6 border ${r.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${r.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(Sr,{className:"h-5 w-5 text-white"})}),"Recent Readings"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.usageHistory.slice(0,10).map(u=>c.jsx("div",{className:"p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm hover:shadow-md transition-all duration-200",children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${r.text} text-lg`,children:[u.currentUnits.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-sm ${r.textSecondary} opacity-80`,children:["Previous: ",u.previousUnits.toFixed(2)," ",n()]}),c.jsx("p",{className:`text-xs ${r.textSecondary} mt-1 opacity-70`,children:u.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${u.usage>0?"bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200":"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200"}`,children:["-",u.usage.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${r.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",(u.usage*t.unitCost).toFixed(2)]})]})]})},u.id)),t.usageHistory.length===0&&c.jsxs("div",{className:"text-center py-12 bg-white/40 backdrop-blur-sm rounded-xl border border-white/40",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-gray-100 to-slate-200 w-fit mx-auto mb-4",children:c.jsx(xn,{className:"h-12 w-12 text-gray-400"})}),c.jsx("p",{className:`text-sm ${r.textSecondary} opacity-80 font-medium`,children:"No usage records yet"}),c.jsx("p",{className:`text-xs ${r.textSecondary} opacity-60 mt-1`,children:"Record your first reading above to get started"})]})]})]})]}),c.jsxs("div",{className:`${r.card} rounded-2xl shadow-lg p-8 border ${r.border} bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${r.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md",children:c.jsx(Fd,{className:"h-5 w-5 text-white"})}),"How Usage is Calculated"]}),c.jsx("div",{className:"p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/40 shadow-sm",children:c.jsxs("div",{className:"space-y-4 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100",children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${r.text} font-bold text-lg`,children:[t.previousUnits.toFixed(2)," ",n()]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-100",children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Current Reading:"}),c.jsxs("span",{className:`${r.text} font-bold text-lg`,children:[t.currentUnits.toFixed(2)," ",n()]})]}),c.jsx("div",{className:"border-t border-gradient-to-r from-gray-200 to-slate-200 my-4"}),c.jsxs("div",{className:"flex justify-between items-center p-4 bg-gradient-to-r from-rose-50 to-pink-50 rounded-lg border border-rose-100",children:[c.jsx("span",{className:`${r.text} font-semibold`,children:"Usage Since Last Recording:"}),c.jsxs("div",{className:"text-right",children:[c.jsxs("div",{className:`${r.text} text-sm opacity-70 mb-1`,children:[t.previousUnits.toFixed(2)," - ",t.currentUnits.toFixed(2)]}),c.jsxs("span",{className:`${r.text} font-bold text-xl bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent`,children:[e.toFixed(2)," ",n()]})]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg border border-violet-100",children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`${r.text} font-bold text-lg bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent`,children:[t.currencySymbol||"R",(e*t.unitCost).toFixed(2)]})]})]})})]})]})}function kS({history:t}){const{state:e,getDisplayUnitName:n}=Ye(),{theme:r}=Pe();return t.length===0?null:c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsx("thead",{className:r.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Type"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Details"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:n()}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Amount"})]})}),c.jsx("tbody",{className:`${r.card} divide-y divide-gray-200`,children:t.map(i=>c.jsxs("tr",{className:"hover:bg-gray-50",children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"flex items-center",children:i.type==="purchase"?c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:c.jsx(st,{className:"h-4 w-4 text-green-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[c.jsx(g1,{className:"mr-1 h-3 w-3"}),"Purchase"]})})]}):c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:c.jsx(xn,{className:"h-4 w-4 text-red-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[c.jsx(p1,{className:"mr-1 h-3 w-3"}),"Usage"]})})]})})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:i.timestamp}),c.jsx("td",{className:`px-6 py-4 text-sm ${r.text}`,children:i.type==="purchase"?c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Electricity Purchase"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["@ ",e.currencySymbol||"R",i.unitCost.toFixed(2)," per ",n()]})]}):c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Usage Recording"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["From ",i.previousUnits.toFixed(2)," to ",i.currentUnits.toFixed(2)," ",n()]})]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:i.type==="purchase"?c.jsxs("span",{className:"text-green-600 font-medium",children:["+",i.units.toFixed(2)]}):c.jsxs("span",{className:"text-red-600 font-medium",children:["-",i.usage.toFixed(2)]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:i.type==="purchase"?c.jsxs("span",{className:"text-green-600 font-medium",children:["+",e.currencySymbol||"R",i.currency.toFixed(2)]}):c.jsxs("span",{className:"text-red-600 font-medium",children:["-",e.currencySymbol||"R",(i.usage*e.unitCost).toFixed(2)]})})]},`${i.type}-${i.id}`))})]})})}function NS(){const t=ia(),[e,n]=k.useState("all"),[r,i]=k.useState(""),{state:s,getDisplayUnitName:o}=Ye(),{theme:a}=Pe(),l=[...s.purchases.map(v=>({...v,type:"purchase"})),...s.usageHistory.map(v=>({...v,type:"usage"}))].sort((v,g)=>new Date(g.date)-new Date(v.date)),u=l.filter(v=>{const g=e==="all"||v.type===e,b=!r||v.date.includes(r);return g&&b}),d=s.purchases.reduce((v,g)=>v+g.currency,0),h=s.usageHistory.reduce((v,g)=>v+g.usage,0),f=h*s.unitCost,m=[{id:"all",name:"All Activity",icon:Ys},{id:"purchase",name:"Purchases",icon:st},{id:"usage",name:"Usage",icon:xn}];return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${a.text}`,children:"History"}),c.jsx("p",{className:`mt-2 ${a.textSecondary}`,children:"View detailed logs of all purchases and usage patterns"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[c.jsx("div",{className:`${a.card} rounded-2xl shadow-lg p-6 border ${a.border} bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg",children:c.jsx(st,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${a.textSecondary} opacity-80`,children:"Total Spent"}),c.jsxs("p",{className:"text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:[s.currencySymbol||"R",d.toFixed(2)]}),c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})]})]})}),c.jsx("div",{className:`${a.card} rounded-2xl shadow-lg p-6 border ${a.border} bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-rose-400 to-pink-500 shadow-lg",children:c.jsx(xn,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsxs("p",{className:`text-sm font-medium ${a.textSecondary} opacity-80`,children:["Total ",o()," Used"]}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:h.toFixed(2)}),c.jsx("p",{className:"text-xs text-rose-500 font-medium",children:o()})]})]})}),c.jsx("div",{className:`${a.card} rounded-2xl shadow-lg p-6 border ${a.border} bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg",children:c.jsx(Sr,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${a.textSecondary} opacity-80`,children:"Usage Cost"}),c.jsxs("p",{className:"text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:[s.currencySymbol||"R",f.toFixed(2)]}),c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Total Cost"})]})]})}),c.jsx("div",{className:`${a.card} rounded-2xl shadow-lg p-6 border ${a.border} bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg",children:c.jsx(Ys,{className:"h-6 w-6 text-white"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:`text-sm font-medium ${a.textSecondary} opacity-80`,children:"Total Records"}),c.jsx("p",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:l.length}),c.jsx("p",{className:"text-xs text-blue-500 font-medium",children:"Entries"})]})]})})]}),c.jsxs("div",{className:`${a.card} rounded-2xl shadow-lg border ${a.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsx("div",{className:"p-8 bg-white/60 backdrop-blur-sm rounded-t-2xl",children:c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[c.jsx("div",{className:"flex space-x-1",children:m.map(v=>c.jsxs("button",{onClick:()=>n(v.id),className:`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${e===v.id?`${a.primary} text-white`:`${a.text} hover:${a.secondary}`}`,children:[c.jsx(v.icon,{className:"mr-2 h-4 w-4"}),v.name]},v.id))}),c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(x1,{className:`h-5 w-5 ${a.textSecondary}`}),c.jsx("input",{type:"date",value:r,onChange:v=>i(v.target.value),className:`px-3 py-2 border ${a.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${a.card} ${a.text}`}),r&&c.jsx("button",{onClick:()=>i(""),className:`px-3 py-2 text-sm ${a.textSecondary} hover:${a.text}`,children:"Clear"})]})]})}),c.jsx("div",{className:"border-t border-gray-200",children:c.jsx(kS,{history:u})})]}),u.length===0&&c.jsxs("div",{className:`${a.card} rounded-2xl shadow-lg p-16 text-center border ${a.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 rounded-full opacity-20 scale-110"}),c.jsx("div",{className:"relative p-6 rounded-2xl bg-gradient-to-br from-gray-100 to-slate-200 w-fit mx-auto",children:c.jsx(Ys,{className:"h-16 w-16 text-gray-400"})})]}),c.jsx("h3",{className:`mt-6 text-2xl font-bold ${a.text}`,children:"No history found"}),c.jsx("p",{className:`mt-3 ${a.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`,children:r?"No records found for the selected date. Try a different date or clear the filter.":"Start by making purchases or recording usage to see your history here."}),!r&&c.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row justify-center gap-4",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"💰"}),"Add Purchase"]})}),c.jsx("button",{onClick:()=>t("/usage"),className:"bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"⚡"}),"Record Usage"]})})]})]})]})}function jS(){const[t,e]=k.useState(!1),[n,r]=k.useState(!1),[i,s]=k.useState(!1),o=k.useRef(null),a=k.useRef(null),l=k.useRef(null),{currentTheme:u,setCurrentTheme:d,fontSize:h,setFontSize:f,fontFamily:m,setFontFamily:v,theme:g}=Pe();return k.useEffect(()=>{function b(p){o.current&&!o.current.contains(p.target)&&e(!1),a.current&&!a.current.contains(p.target)&&r(!1),l.current&&!l.current.contains(p.target)&&s(!1)}return document.addEventListener("mousedown",b),()=>{document.removeEventListener("mousedown",b)}},[]),c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${g.text} mb-4 flex items-center`,children:[c.jsx(Rp,{className:"mr-2 h-5 w-5"}),"Choose Theme"]}),c.jsxs("div",{className:"relative",ref:o,children:[c.jsxs("button",{onClick:()=>e(!t),className:`w-full p-4 ${g.card} border ${g.border} rounded-lg flex items-center justify-between hover:${g.secondary} transition-colors`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:`h-6 w-16 ${mt[u].gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-2 w-2 ${mt[u].primary} rounded`}),c.jsx("div",{className:`h-2 w-2 ${mt[u].accent} rounded`}),c.jsx("div",{className:`h-2 w-2 ${mt[u].secondary} rounded`})]})]}),c.jsx("span",{className:`text-lg font-medium ${g.text}`,children:mt[u].name})]}),c.jsx(Aa,{className:`h-5 w-5 ${g.textSecondary} transition-transform ${t?"rotate-180":""}`})]}),t&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${g.card} border ${g.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:c.jsx("div",{className:"grid grid-cols-1 gap-2 p-2",children:Object.entries(mt).map(([b,p])=>c.jsx("button",{onClick:()=>{d(b),e(!1)},className:`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${u===b?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2 flex-shrink-0",children:[c.jsx("div",{className:`h-8 w-20 ${p.gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-3 w-3 ${p.primary} rounded`}),c.jsx("div",{className:`h-3 w-3 ${p.accent} rounded`}),c.jsx("div",{className:`h-3 w-3 ${p.secondary} rounded`})]})]}),c.jsx("div",{className:"flex-1",children:c.jsx("p",{className:`text-sm font-medium ${g.text}`,children:p.name})}),u===b&&c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Fa,{className:"h-5 w-5 text-blue-500"})})]})},b))})})]})]}),c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${g.text} mb-4 flex items-center`,children:[c.jsx(N1,{className:"mr-2 h-5 w-5"}),"Font Family"]}),c.jsxs("div",{className:"relative",ref:a,children:[c.jsxs("button",{onClick:()=>r(!n),className:`w-full p-4 ${g.card} border ${g.border} rounded-lg flex items-center justify-between hover:${g.secondary} transition-colors`,children:[c.jsx("span",{className:`text-lg font-medium ${g.text}`,style:{fontFamily:Lt[m].fallback},children:Lt[m].name}),c.jsx(Aa,{className:`h-5 w-5 ${g.textSecondary} transition-transform ${n?"rotate-180":""}`})]}),n&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${g.card} border ${g.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:Object.entries(Lt).map(([b,p])=>c.jsx("button",{onClick:()=>{v(b),r(!1)},className:`w-full p-4 text-left hover:${g.secondary} transition-colors border-b ${g.border} last:border-b-0 ${m===b?g.secondary:""}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("span",{className:`text-lg font-medium ${g.text}`,style:{fontFamily:p.fallback},children:p.name}),m===b&&c.jsx(Fa,{className:"h-5 w-5 text-blue-500"})]})},b))})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${g.text} mb-4`,children:"Font Size"}),c.jsxs("div",{className:"relative",ref:l,children:[c.jsxs("button",{onClick:()=>s(!i),className:`w-full p-4 ${g.card} border ${g.border} rounded-lg flex items-center justify-between hover:${g.secondary} transition-colors`,children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`${rn[h].class} font-medium ${g.text}`,children:"Aa"}),c.jsxs("span",{className:`text-sm ${g.textSecondary}`,children:[rn[h].name," (",rn[h].size,")"]})]}),c.jsx(Aa,{className:`h-5 w-5 ${g.textSecondary} transition-transform ${i?"rotate-180":""}`})]}),i&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${g.card} border ${g.border} rounded-lg shadow-lg z-50`,children:Object.entries(rn).map(([b,p])=>c.jsx("button",{onClick:()=>{f(b),s(!1)},className:`w-full p-4 text-left hover:${g.secondary} transition-colors border-b ${g.border} last:border-b-0 ${h===b?g.secondary:""}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("div",{className:`${p.class} font-medium ${g.text}`,children:"Aa"}),c.jsxs("div",{children:[c.jsx("p",{className:`font-medium ${g.text}`,children:p.name}),c.jsx("p",{className:`text-sm ${g.textSecondary}`,children:p.size})]})]}),h===b&&c.jsx(Fa,{className:"h-5 w-5 text-blue-500"})]})},b))})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${g.text} mb-4`,children:"Preview"}),c.jsxs("div",{className:`p-6 ${g.card} rounded-lg border ${g.border} space-y-4`,children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("h4",{className:`text-xl font-bold ${g.text}`,children:"Sample Dashboard"}),c.jsx("div",{className:`px-3 py-1 ${g.primary} text-white rounded-full text-sm`,children:"Active"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[c.jsxs("div",{className:`p-4 ${g.secondary} rounded-lg`,children:[c.jsx("p",{className:`text-sm ${g.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-2xl font-bold ${g.text}`,children:"125.50"})]}),c.jsxs("div",{className:`p-4 ${g.secondary} rounded-lg`,children:[c.jsx("p",{className:`text-sm ${g.textSecondary}`,children:"Usage Today"}),c.jsx("p",{className:`text-2xl font-bold ${g.text}`,children:"8.25"})]}),c.jsxs("div",{className:`p-4 ${g.secondary} rounded-lg`,children:[c.jsx("p",{className:`text-sm ${g.textSecondary}`,children:"Cost"}),c.jsx("p",{className:`text-2xl font-bold ${g.text}`,children:"R20.63"})]})]}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{className:`px-4 py-2 ${g.primary} text-white rounded-lg text-sm`,children:"Primary Button"}),c.jsx("button",{className:`px-4 py-2 border ${g.border} ${g.text} rounded-lg text-sm`,children:"Secondary Button"})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${g.text} mb-4`,children:"Reset Appearance"}),c.jsxs("div",{className:"flex space-x-4",children:[c.jsx("button",{onClick:()=>{d("electric"),f("base"),v("inter"),e(!1),r(!1),s(!1)},className:`px-6 py-3 border ${g.border} ${g.text} rounded-lg hover:${g.secondary} transition-colors`,children:"Reset All to Default"}),c.jsx("button",{onClick:()=>{f("base"),v("inter"),r(!1),s(!1)},className:`px-6 py-3 border ${g.border} ${g.text} rounded-lg hover:${g.secondary} transition-colors`,children:"Reset Fonts Only"})]})]})]})}function CS(){const[t,e]=k.useState(!1),[n,r]=k.useState(!1),[i,s]=k.useState(!1),{state:o,factoryReset:a,dashboardReset:l}=Ye(),{theme:u}=Pe(),d=async()=>{s(!0);try{a(),e(!1),alert("Factory reset completed successfully! The app will now restart.")}catch(f){console.error("Error during factory reset:",f),alert("Error during factory reset. Please try again.")}finally{s(!1)}},h=async()=>{s(!0);try{l(),r(!1),alert("Dashboard data reset successfully! Your history has been preserved.")}catch(f){console.error("Error during dashboard reset:",f),alert("Error during dashboard reset. Please try again.")}finally{s(!1)}};return c.jsxs("div",{className:"space-y-8",children:[c.jsx("div",{className:`p-6 border ${u.border} rounded-lg`,children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Op,{className:"h-6 w-6 text-orange-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text}`,children:"Dashboard Data Reset"}),c.jsx("p",{className:`mt-2 text-sm ${u.textSecondary}`,children:"Reset current units and previous readings to zero. This will clear your dashboard data but preserve your purchase and usage history for reference."}),c.jsxs("div",{className:"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:[c.jsx("h4",{className:"text-sm font-medium text-orange-800 mb-2",children:"What will be reset:"}),c.jsxs("ul",{className:"text-xs text-orange-700 space-y-1",children:[c.jsx("li",{children:"• Current units will be set to 0"}),c.jsx("li",{children:"• Previous units will be set to 0"}),c.jsx("li",{children:"• Usage since last recording will be reset"})]}),c.jsx("h4",{className:"text-sm font-medium text-orange-800 mt-3 mb-2",children:"What will be preserved:"}),c.jsxs("ul",{className:"text-xs text-orange-700 space-y-1",children:[c.jsx("li",{children:"• All purchase history"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]})]}),n?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:"flex items-center p-3 bg-red-50 border border-red-200 rounded-lg",children:[c.jsx(Hi,{className:"h-5 w-5 text-red-600 mr-2"}),c.jsx("span",{className:"text-sm text-red-800",children:"Are you sure? This action cannot be undone."})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:h,disabled:i,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Reset Dashboard"}),c.jsx("button",{onClick:()=>r(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>r(!0),className:"mt-4 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:"Reset Dashboard Data"})]})]})}),c.jsx("div",{className:"p-6 border border-red-200 rounded-lg bg-red-50",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(S1,{className:"h-6 w-6 text-red-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:"text-lg font-semibold text-red-800",children:"Factory Reset"}),c.jsx("p",{className:"mt-2 text-sm text-red-700",children:"Completely reset the app to its initial state. This will delete ALL data including purchases, usage history, and settings. You will need to set up the app again from scratch."}),c.jsxs("div",{className:"mt-4 p-3 bg-red-100 border border-red-300 rounded-lg",children:[c.jsx("h4",{className:"text-sm font-medium text-red-800 mb-2",children:"What will be deleted:"}),c.jsxs("ul",{className:"text-xs text-red-700 space-y-1",children:[c.jsx("li",{children:"• All purchase records"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Current and previous unit readings"}),c.jsx("li",{children:"• All settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]}),c.jsxs("div",{className:"mt-3 p-2 bg-red-200 border border-red-400 rounded text-xs text-red-800",children:[c.jsx("strong",{children:"Warning:"})," This action is irreversible. Make sure you have backed up any important data."]})]}),t?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:"flex items-center p-3 bg-red-100 border border-red-300 rounded-lg",children:[c.jsx(Hi,{className:"h-5 w-5 text-red-700 mr-2"}),c.jsx("span",{className:"text-sm text-red-800 font-medium",children:"This will permanently delete ALL your data. Are you absolutely sure?"})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:d,disabled:i,className:"px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Delete Everything"}),c.jsx("button",{onClick:()=>e(!1),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>e(!0),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Factory Reset"})]})]})}),c.jsxs("div",{className:`p-6 ${u.card} border ${u.border} rounded-lg`,children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-4`,children:"Current Data Summary"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"App Data"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Current Units: ",o.currentUnits.toFixed(2)]}),c.jsxs("li",{children:["• Previous Units: ",o.previousUnits.toFixed(2)]}),c.jsxs("li",{children:["• Unit Cost: R",o.unitCost.toFixed(2)]}),c.jsxs("li",{children:["• Threshold: ",o.thresholdLimit.toFixed(2)," units"]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"History"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Purchases: ",o.purchases.length," records"]}),c.jsxs("li",{children:["• Usage Records: ",o.usageHistory.length," records"]}),c.jsxs("li",{children:["• Last Reset: ",o.lastResetDate?new Date(o.lastResetDate).toLocaleDateString():"Never"]}),c.jsxs("li",{children:["• App Initialized: ",o.isInitialized?"Yes":"No"]})]})]})]})]})]})}function MS(){var S,N;const{state:t,updateSettings:e}=Ye(),{theme:n}=Pe(),[r,i]=k.useState(t.unitCost.toString()),[s,o]=k.useState(t.thresholdLimit.toString()),[a,l]=k.useState(t.currency||"ZAR"),[u,d]=k.useState(t.unitName||"kWh"),[h,f]=k.useState(t.customUnitName||""),[m,v]=k.useState(!1),g=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],b=[{value:"kWh",label:"kWh (Kilowatt Hours)"},{value:"Units",label:"Units"},{value:"custom",label:"Custom"}],p=async _=>{_.preventDefault(),v(!0);try{const j=parseFloat(r),E=parseFloat(s);if(isNaN(j)||j<=0){alert("Please enter a valid unit cost (greater than 0)");return}if(isNaN(E)||E<0){alert("Please enter a valid threshold limit (0 or greater)");return}if(u==="custom"&&!h.trim()){alert("Please enter a custom unit name");return}const M=g.find(T=>T.code===a);e({unitCost:j,thresholdLimit:E,currency:a,currencySymbol:(M==null?void 0:M.symbol)||"R",unitName:u,customUnitName:u==="custom"?h.trim():""}),alert("Settings saved successfully!")}catch(j){console.error("Error saving settings:",j),alert("Error saving settings. Please try again.")}finally{v(!1)}},x=[{id:"general",title:"General Settings",icon:Xs,description:"Configure unit costs and usage thresholds"},{id:"appearance",title:"Appearance",icon:Rp,description:"Customize themes, fonts, and colors"},{id:"reset",title:"Reset Options",icon:Op,description:"Factory reset and data management"}],[y,w]=k.useState("general");return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${n.text}`,children:"Settings"}),c.jsx("p",{className:`mt-2 ${n.textSecondary}`,children:"Configure your app preferences and manage your data"})]}),c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg border ${n.border} bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50`,children:[c.jsx("div",{className:"flex border-b border-gray-200",children:x.map(_=>c.jsx("button",{onClick:()=>w(_.id),className:`flex-1 px-6 py-4 text-left transition-colors ${y===_.id?`${n.primary} text-white`:`${n.text} hover:${n.secondary}`}`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx(_.icon,{className:"mr-3 h-5 w-5"}),c.jsxs("div",{children:[c.jsx("h3",{className:"font-medium",children:_.title}),c.jsx("p",{className:`text-sm ${y===_.id?"text-white opacity-80":n.textSecondary}`,children:_.description})]})]})},_.id))}),c.jsxs("div",{className:"p-6",children:[y==="general"&&c.jsx("div",{className:"space-y-6",children:c.jsxs("form",{onSubmit:p,className:"space-y-6",children:[c.jsxs("div",{className:"p-6 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl border border-emerald-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500 mr-3",children:c.jsx(v1,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Currency Settings"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currency",className:`block text-sm font-semibold ${n.text} mb-3`,children:"💰 Currency"}),c.jsx("select",{id:"currency",value:a,onChange:_=>l(_.target.value),className:`w-full px-4 py-4 border-2 border-emerald-100 rounded-xl focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 bg-white ${n.text} font-medium shadow-sm transition-all duration-200`,children:g.map(_=>c.jsxs("option",{value:_.code,children:[_.symbol," - ",_.name," (",_.code,")"]},_.code))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 Select your preferred currency for cost calculations"})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3",children:c.jsx(Ue,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Unit Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"unitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⚡ Unit Name"}),c.jsx("select",{id:"unitName",value:u,onChange:_=>d(_.target.value),className:`w-full px-4 py-4 border-2 border-blue-100 rounded-xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 bg-white ${n.text} font-medium shadow-sm transition-all duration-200`,children:b.map(_=>c.jsx("option",{value:_.value,children:_.label},_.value))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚡ Choose how your units are displayed throughout the app"})]}),u==="custom"&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customUnitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"🎯 Custom Unit Name"}),c.jsx("input",{type:"text",id:"customUnitName",value:h,onChange:_=>f(_.target.value),placeholder:"Enter custom unit name (e.g., Donkey, Credits, Points)",className:`w-full px-4 py-4 border-2 border-purple-100 rounded-xl focus:ring-2 focus:ring-purple-400 focus:border-purple-400 bg-gradient-to-br from-purple-50 to-pink-50 ${n.text} placeholder-purple-400 font-medium shadow-sm transition-all duration-200`,required:u==="custom"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['🎯 This name will be used everywhere (e.g., "Cost per ',h||"YourUnit",'")']})]})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(st,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Cost Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-semibold ${n.text} mb-3`,children:["💵 Cost per ",u==="custom"?h||"Unit":u]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500",children:c.jsx(st,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"unitCost",value:r,onChange:_=>i(_.target.value),step:"0.01",min:"0.01",placeholder:"Enter cost per unit",className:`w-full pl-12 pr-4 py-4 border-2 border-violet-100 rounded-xl focus:ring-2 focus:ring-violet-400 focus:border-violet-400 bg-white ${n.text} placeholder-violet-400 font-medium shadow-sm transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 This is used to calculate the cost of your electricity usage"})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl border border-amber-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3",children:c.jsx(Hi,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Alert Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"thresholdLimit",className:`block text-sm font-semibold ${n.text} mb-3`,children:["⚠️ Low ",u==="custom"?h||"Units":u," Warning Threshold"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500",children:c.jsx(Hi,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"thresholdLimit",value:s,onChange:_=>o(_.target.value),step:"0.01",min:"0",placeholder:"Enter low units warning threshold",className:`w-full pl-12 pr-4 py-4 border-2 border-amber-100 rounded-xl focus:ring-2 focus:ring-amber-400 focus:border-amber-400 bg-white ${n.text} placeholder-amber-400 font-medium shadow-sm transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚠️ You'll receive a warning when your remaining units drop below this threshold"})]})]}),c.jsxs("div",{className:"p-6 bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl border border-gray-100 shadow-sm",children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3",children:c.jsx(Xs,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Current Settings Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Currency:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[((S=g.find(_=>_.code===(t.currency||"ZAR")))==null?void 0:S.symbol)||"R"," - ",((N=g.find(_=>_.code===(t.currency||"ZAR")))==null?void 0:N.name)||"South African Rand"]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Name:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)," per ",t.unitName==="custom"?t.customUnitName||"Unit":t.unitName||"kWh"]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Low Units Warning:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.thresholdLimit.toFixed(2)," ",t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"]})]}),c.jsxs("div",{className:"flex justify-between items-center p-3 bg-white/60 rounded-lg",children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Last Reset:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.lastResetDate?new Date(t.lastResetDate).toLocaleDateString():"Never"})]})]})]}),c.jsx("button",{type:"submit",disabled:m,className:"w-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-600 hover:via-indigo-600 hover:to-purple-700 transition-all duration-300 focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:m?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Saving Settings..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Xs,{className:"h-5 w-5"}),"Save Settings"]})})})]})}),y==="appearance"&&c.jsx("div",{className:"space-y-6",children:c.jsx(jS,{})}),y==="reset"&&c.jsx("div",{className:"space-y-6",children:c.jsx(CS,{})})]})]})]})}function PS(){const[t,e]=k.useState(""),[n,r]=k.useState(""),{initializeApp:i}=Ye(),{theme:s}=Pe(),o=a=>{a.preventDefault(),r("");const l=parseFloat(t);if(isNaN(l)||l<0){r("Please enter a valid number of units (0 or greater)");return}i(l)};return c.jsx("div",{className:`min-h-screen flex items-center justify-center ${s.background} px-4`,children:c.jsxs("div",{className:`max-w-md w-full ${s.card} rounded-lg shadow-xl p-8 border ${s.border}`,children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"flex justify-center mb-4",children:c.jsx(Po,{size:"xl"})}),c.jsx("h1",{className:`text-3xl font-bold ${s.text} mb-2`,children:"Welcome to Prepaid Meter App"}),c.jsx("p",{className:`${s.textSecondary}`,children:"Let's get started by setting up your initial meter reading"})]}),c.jsxs("form",{onSubmit:o,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${s.text} mb-2`,children:"Initial Unit Value"}),c.jsx("input",{type:"number",id:"initialUnits",value:t,onChange:a=>e(a.target.value),step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-4 py-3 border ${s.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${s.card} ${s.text}`,required:!0}),n&&c.jsx("p",{className:"mt-2 text-sm text-red-600",children:n})]}),c.jsxs("div",{className:`p-4 ${s.secondary} rounded-lg`,children:[c.jsx("h3",{className:`font-medium ${s.text} mb-2`,children:"What happens next?"}),c.jsxs("ul",{className:`text-sm ${s.textSecondary} space-y-1`,children:[c.jsx("li",{children:"• This will be your starting point for tracking usage"}),c.jsx("li",{children:"• You can add purchases to increase your units"}),c.jsx("li",{children:"• Track your daily electricity consumption"}),c.jsx("li",{children:"• Set up warnings and monthly resets"})]})]}),c.jsx("button",{type:"submit",className:`w-full ${s.primary} text-white py-3 px-4 rounded-lg font-medium hover:opacity-90 transition-opacity focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`,children:"Initialize App"})]}),c.jsx("div",{className:"mt-6 text-center",children:c.jsx("p",{className:`text-xs ${s.textSecondary}`,children:"You can always reset this value later in Settings"})})]})})}function ES(){const[t,e]=k.useState(!1),{state:n}=Ye(),{theme:r}=Pe();return n.isInitialized?c.jsxs("div",{className:`flex h-screen ${r.background}`,children:[c.jsx(C1,{isOpen:t,onClose:()=>e(!1)}),c.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[c.jsx(j1,{onMenuClick:()=>e(!0)}),c.jsx("main",{className:"flex-1 overflow-x-hidden overflow-y-auto p-4",children:c.jsx("div",{className:"max-w-7xl mx-auto",children:c.jsxs(Wv,{children:[c.jsx(On,{path:"/",element:c.jsx(qh,{})}),c.jsx(On,{path:"/dashboard",element:c.jsx(qh,{})}),c.jsx(On,{path:"/purchases",element:c.jsx(wS,{})}),c.jsx(On,{path:"/usage",element:c.jsx(_S,{})}),c.jsx(On,{path:"/history",element:c.jsx(NS,{})}),c.jsx(On,{path:"/settings",element:c.jsx(MS,{})})]})})})]}),t&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>e(!1)})]}):c.jsx(PS,{})}function TS(){return c.jsx(qv,{children:c.jsx(h1,{children:c.jsx(d1,{children:c.jsx(ES,{})})})})}Za.createRoot(document.getElementById("root")).render(c.jsx(pt.StrictMode,{children:c.jsx(TS,{})}));
