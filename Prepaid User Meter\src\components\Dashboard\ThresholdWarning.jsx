import React from 'react'
import { useNavigate } from 'react-router-dom'
import { HiExclamation, HiX } from 'react-icons/hi'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'

function ThresholdWarning() {
  const navigate = useNavigate()
  const { state, getDisplayUnitName } = useApp()
  const { theme } = useTheme()

  const remainingUnits = state.currentUnits
  const thresholdLimit = state.thresholdLimit

  return (
    <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <div className="p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg">
            <HiExclamation className="h-6 w-6 text-white animate-pulse" />
          </div>
        </div>
        <div className="ml-4 flex-1">
          <h3 className="text-xl font-bold text-amber-800 mb-2">
            ⚠️ Low {getDisplayUnitName()} Warning!
          </h3>
          <div className="text-sm text-amber-700 space-y-2">
            <p className="font-medium">
              You have <strong className="text-amber-900">{remainingUnits.toFixed(2)} {getDisplayUnitName()}</strong> remaining,
              which is below your threshold of <strong className="text-amber-900">{thresholdLimit.toFixed(2)} {getDisplayUnitName()}</strong>.
            </p>
            <p>
              💡 <strong>Time to top up!</strong> Consider purchasing more {getDisplayUnitName()} to avoid running out of power.
            </p>
          </div>
          <div className="mt-4 flex flex-wrap gap-3">
            <button
              onClick={() => navigate('/purchases')}
              className="bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
            >
              🛒 Top Up Now
            </button>
            <button
              onClick={() => navigate('/settings')}
              className="bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200"
            >
              ⚙️ Adjust Threshold
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ThresholdWarning
