import React, { createContext, useContext, useState, useEffect } from 'react'

const ThemeContext = createContext()

export const themes = {
  electric: {
    name: 'Electric Blue',
    primary: 'bg-blue-600',
    secondary: 'bg-blue-100',
    accent: 'bg-yellow-400',
    background: 'bg-gray-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-gray-200',
    card: 'bg-white',
    gradient: 'from-blue-500 to-blue-700'
  },
  dark: {
    name: 'Dark Mode',
    primary: 'bg-gray-800',
    secondary: 'bg-gray-700',
    accent: 'bg-blue-500',
    background: 'bg-gray-900',
    text: 'text-white',
    textSecondary: 'text-gray-300',
    border: 'border-gray-600',
    card: 'bg-gray-800',
    gradient: 'from-gray-700 to-gray-900'
  },
  green: {
    name: 'Eco Green',
    primary: 'bg-green-600',
    secondary: 'bg-green-100',
    accent: 'bg-lime-400',
    background: 'bg-green-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-green-200',
    card: 'bg-white',
    gradient: 'from-green-500 to-green-700'
  },
  purple: {
    name: 'Royal Purple',
    primary: 'bg-purple-600',
    secondary: 'bg-purple-100',
    accent: 'bg-pink-400',
    background: 'bg-purple-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-purple-200',
    card: 'bg-white',
    gradient: 'from-purple-500 to-purple-700'
  },
  orange: {
    name: 'Sunset Orange',
    primary: 'bg-orange-600',
    secondary: 'bg-orange-100',
    accent: 'bg-yellow-400',
    background: 'bg-orange-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-orange-200',
    card: 'bg-white',
    gradient: 'from-orange-500 to-red-600'
  },
  teal: {
    name: 'Ocean Teal',
    primary: 'bg-teal-600',
    secondary: 'bg-teal-100',
    accent: 'bg-cyan-400',
    background: 'bg-teal-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-teal-200',
    card: 'bg-white',
    gradient: 'from-teal-500 to-blue-600'
  },
  red: {
    name: 'Fire Red',
    primary: 'bg-red-600',
    secondary: 'bg-red-100',
    accent: 'bg-orange-400',
    background: 'bg-red-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-red-200',
    card: 'bg-white',
    gradient: 'from-red-500 to-red-700'
  },
  indigo: {
    name: 'Deep Indigo',
    primary: 'bg-indigo-600',
    secondary: 'bg-indigo-100',
    accent: 'bg-purple-400',
    background: 'bg-indigo-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-indigo-200',
    card: 'bg-white',
    gradient: 'from-indigo-500 to-purple-600'
  },
  pink: {
    name: 'Rose Pink',
    primary: 'bg-pink-600',
    secondary: 'bg-pink-100',
    accent: 'bg-rose-400',
    background: 'bg-pink-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-pink-200',
    card: 'bg-white',
    gradient: 'from-pink-500 to-rose-600'
  },
  slate: {
    name: 'Modern Slate',
    primary: 'bg-slate-600',
    secondary: 'bg-slate-100',
    accent: 'bg-blue-400',
    background: 'bg-slate-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-slate-200',
    card: 'bg-white',
    gradient: 'from-slate-500 to-slate-700'
  }
}

export const fontFamilies = {
  inter: {
    name: 'Inter',
    class: 'font-inter',
    import: '@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");',
    fallback: 'Inter, system-ui, -apple-system, sans-serif'
  },
  roboto: {
    name: 'Roboto',
    class: 'font-roboto',
    import: '@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");',
    fallback: 'Roboto, system-ui, sans-serif'
  },
  opensans: {
    name: 'Open Sans',
    class: 'font-opensans',
    import: '@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap");',
    fallback: 'Open Sans, system-ui, sans-serif'
  },
  lato: {
    name: 'Lato',
    class: 'font-lato',
    import: '@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap");',
    fallback: 'Lato, system-ui, sans-serif'
  },
  poppins: {
    name: 'Poppins',
    class: 'font-poppins',
    import: '@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");',
    fallback: 'Poppins, system-ui, sans-serif'
  },
  nunito: {
    name: 'Nunito',
    class: 'font-nunito',
    import: '@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700&display=swap");',
    fallback: 'Nunito, system-ui, sans-serif'
  },
  sourcesans: {
    name: 'Source Sans Pro',
    class: 'font-sourcesans',
    import: '@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap");',
    fallback: 'Source Sans Pro, system-ui, sans-serif'
  },
  ubuntu: {
    name: 'Ubuntu',
    class: 'font-ubuntu',
    import: '@import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap");',
    fallback: 'Ubuntu, system-ui, sans-serif'
  },
  raleway: {
    name: 'Raleway',
    class: 'font-raleway',
    import: '@import url("https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap");',
    fallback: 'Raleway, system-ui, sans-serif'
  },
  montserrat: {
    name: 'Montserrat',
    class: 'font-montserrat',
    import: '@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap");',
    fallback: 'Montserrat, system-ui, sans-serif'
  },
  worksans: {
    name: 'Work Sans',
    class: 'font-worksans',
    import: '@import url("https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600;700&display=swap");',
    fallback: 'Work Sans, system-ui, sans-serif'
  },
  firasans: {
    name: 'Fira Sans',
    class: 'font-firasans',
    import: '@import url("https://fonts.googleapis.com/css2?family=Fira+Sans:wght@300;400;500;600;700&display=swap");',
    fallback: 'Fira Sans, system-ui, sans-serif'
  },
  dmsans: {
    name: 'DM Sans',
    class: 'font-dmsans',
    import: '@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");',
    fallback: 'DM Sans, system-ui, sans-serif'
  },
  lexend: {
    name: 'Lexend',
    class: 'font-lexend',
    import: '@import url("https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap");',
    fallback: 'Lexend, system-ui, sans-serif'
  },
  karla: {
    name: 'Karla',
    class: 'font-karla',
    import: '@import url("https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700&display=swap");',
    fallback: 'Karla, system-ui, sans-serif'
  },
  rubik: {
    name: 'Rubik',
    class: 'font-rubik',
    import: '@import url("https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&display=swap");',
    fallback: 'Rubik, system-ui, sans-serif'
  },
  manrope: {
    name: 'Manrope',
    class: 'font-manrope',
    import: '@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap");',
    fallback: 'Manrope, system-ui, sans-serif'
  },
  plusjakarta: {
    name: 'Plus Jakarta Sans',
    class: 'font-plusjakarta',
    import: '@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap");',
    fallback: 'Plus Jakarta Sans, system-ui, sans-serif'
  },
  outfit: {
    name: 'Outfit',
    class: 'font-outfit',
    import: '@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap");',
    fallback: 'Outfit, system-ui, sans-serif'
  },
  system: {
    name: 'System Default',
    class: 'font-system',
    import: '',
    fallback: 'system-ui, -apple-system, BlinkMacSystemFont, sans-serif'
  }
}

export const fontSizes = {
  xs: { name: 'Extra Small', class: 'text-xs', size: '12px' },
  sm: { name: 'Small', class: 'text-sm', size: '14px' },
  base: { name: 'Medium', class: 'text-base', size: '16px' },
  lg: { name: 'Large', class: 'text-lg', size: '18px' },
  xl: { name: 'Extra Large', class: 'text-xl', size: '20px' },
  '2xl': { name: 'XXL', class: 'text-2xl', size: '24px' }
}

export function ThemeProvider({ children }) {
  const [currentTheme, setCurrentTheme] = useState('electric')
  const [fontSize, setFontSize] = useState('base')
  const [fontFamily, setFontFamily] = useState('inter')
  const [customColors, setCustomColors] = useState({})

  // Load theme settings from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('prepaid-meter-theme')
    const savedFontSize = localStorage.getItem('prepaid-meter-font-size')
    const savedFontFamily = localStorage.getItem('prepaid-meter-font-family')
    const savedCustomColors = localStorage.getItem('prepaid-meter-custom-colors')

    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme)
    }
    if (savedFontSize && fontSizes[savedFontSize]) {
      setFontSize(savedFontSize)
    }
    if (savedFontFamily && fontFamilies[savedFontFamily]) {
      setFontFamily(savedFontFamily)
    }
    if (savedCustomColors) {
      try {
        setCustomColors(JSON.parse(savedCustomColors))
      } catch (error) {
        console.error('Error loading custom colors:', error)
      }
    }
  }, [])

  // Save theme settings to localStorage
  useEffect(() => {
    localStorage.setItem('prepaid-meter-theme', currentTheme)
  }, [currentTheme])

  useEffect(() => {
    localStorage.setItem('prepaid-meter-font-size', fontSize)
  }, [fontSize])

  useEffect(() => {
    localStorage.setItem('prepaid-meter-font-family', fontFamily)
  }, [fontFamily])

  useEffect(() => {
    localStorage.setItem('prepaid-meter-custom-colors', JSON.stringify(customColors))
  }, [customColors])

  // Dynamically load Google Fonts
  useEffect(() => {
    const currentFont = fontFamilies[fontFamily]
    if (currentFont && currentFont.import) {
      // Remove existing font imports
      const existingLinks = document.querySelectorAll('link[data-font-import]')
      existingLinks.forEach(link => link.remove())

      // Add new font import
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = currentFont.import.replace('@import url("', '').replace('");', '')
      link.setAttribute('data-font-import', 'true')
      document.head.appendChild(link)
    }
  }, [fontFamily])

  const value = {
    currentTheme,
    setCurrentTheme,
    fontSize,
    setFontSize,
    fontFamily,
    setFontFamily,
    customColors,
    setCustomColors,
    theme: themes[currentTheme],
    themes,
    fontSizes,
    fontFamilies,
    currentFontSize: fontSizes[fontSize],
    currentFontFamily: fontFamilies[fontFamily]
  }

  return (
    <ThemeContext.Provider value={value}>
      <div
        className={`${themes[currentTheme].background} ${themes[currentTheme].text} ${fontFamilies[fontFamily].class} ${fontSizes[fontSize].class} min-h-screen transition-all duration-300`}
        style={{ fontFamily: fontFamilies[fontFamily].fallback }}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
