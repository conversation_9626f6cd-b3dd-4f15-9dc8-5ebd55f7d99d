import React from 'react'
import { HiMenu } from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'
import { useApp } from '../../context/AppContext'
import Logo from '../Common/Logo'

function Header({ onMenuClick }) {
  const { theme } = useTheme()
  const { state } = useApp()

  return (
    <header className={`${theme.card} ${theme.border} border-b px-4 py-3 flex items-center justify-between shadow-sm`}>
      {/* Left side - Menu button and logo */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onMenuClick}
          className={`lg:hidden p-2 rounded-md ${theme.primary} text-white hover:opacity-80 transition-opacity`}
        >
          <HiMenu className="h-6 w-6" />
        </button>
        
        <div className="flex items-center">
          <Logo size="md" animated={true} showText={true} />
        </div>
      </div>

      {/* Right side - Current units display */}
      <div className={`hidden sm:flex items-center space-x-4 ${theme.card} rounded-lg px-4 py-2 border ${theme.border}`}>
        <div className="text-right">
          <p className={`text-sm ${theme.textSecondary}`}>Current Units</p>
          <p className={`text-lg font-bold ${theme.text}`}>
            {state.currentUnits.toFixed(2)}
          </p>
        </div>
        <div className={`w-3 h-3 rounded-full ${state.currentUnits > state.thresholdLimit ? 'bg-red-500' : 'bg-green-500'} pulse-glow`} />
      </div>
    </header>
  )
}

export default Header
