@echo off
echo ========================================
echo Building Prepaid Meter Android APK
echo ========================================

echo.
echo Step 1: Building React app for production...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build React app
    pause
    exit /b 1
)

echo.
echo Step 2: Copying web assets to Android...
call npx cap copy android
if %errorlevel% neq 0 (
    echo Error: Failed to copy assets
    pause
    exit /b 1
)

echo.
echo Step 3: Syncing Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo Error: Failed to sync Capacitor
    pause
    exit /b 1
)

echo.
echo Step 4: Building APK...
cd android
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo Error: Failed to build APK
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo ========================================
echo SUCCESS! APK built successfully!
echo ========================================
echo.
echo Your APK is located at:
echo android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo You can install this APK on your phone and tablet.
echo.
pause
