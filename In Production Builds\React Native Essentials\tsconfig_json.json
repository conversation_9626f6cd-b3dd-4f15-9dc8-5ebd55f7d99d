{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "baseUrl": "./src", "paths": {"@components/*": ["components/*"], "@screens/*": ["screens/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"], "@hooks/*": ["hooks/*"], "@store/*": ["store/*"], "@services/*": ["services/*"], "@constants/*": ["constants/*"]}, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}