apply plugin: "com.android.application"
apply plugin: "com.facebook.react"

/**
 * This is the configuration block to customize your React Native Android app.
 */
react {
    /* Folders */
    root = file("../")
    reactNativeDir = file("../node_modules/react-native")
    codegenDir = file("../node_modules/@react-native/codegen")
    cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    debuggableVariants = ["liveRelease", "stagingRelease"]

    /* Bundling */
    bundleCommand = "ram-bundle"
    bundleInStaging = true
    bundleInRelease = true

    /* Hermes Commands */
    hermesCommand = "../node_modules/react-native/sdks/hermesvm/%OS-BIN%/hermes"

    /* Code Push */
    bundleInDebug = false
    bundleInStaging = true
    bundleInRelease = true
}

android {
    ndkVersion rootProject.ext.ndkVersion
    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.yourappname"
    defaultConfig {
        applicationId "com.yourappname"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                storeFile file(MYAPP_UPLOAD_STORE_FILE)
                storePassword MYAPP_UPLOAD_STORE_PASSWORD
                keyAlias MYAPP_UPLOAD_KEY_ALIAS
                keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    packagingOptions {
        pickFirst "lib/x86/libc++_shared.so"
        pickFirst "lib/x86_64/libc++_shared.so"
        pickFirst "lib/arm64-v8a/libc++_shared.so"
        pickFirst "lib/armeabi-v7a/libc++_shared.so"
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation "com.facebook.react:react-android"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    
    // Hermes
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}